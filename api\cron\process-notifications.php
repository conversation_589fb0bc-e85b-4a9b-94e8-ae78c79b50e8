<?php
/**
 * Script de traitement automatique des notifications
 * À exécuter via cron job toutes les 5 minutes
 * 
 * Cron job exemple:
 * 0,5,10,15,20,25,30,35,40,45,50,55 * * * * /usr/bin/php /path/to/project/api/cron/process-notifications.php
 */

// Empêcher l'exécution depuis le navigateur
if (isset($_SERVER['HTTP_HOST'])) {
    die('Ce script ne peut être exécuté que depuis la ligne de commande.');
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/Notification.php';
require_once __DIR__ . '/../services/EmailService.php';

echo "[" . date('Y-m-d H:i:s') . "] 🔄 Démarrage du traitement des notifications...\n";

try {
    $notificationModel = new Notification($pdo);
    $emailService = new EmailService();
    
    // 1. Créer les rappels pour les RDV de demain
    echo "[" . date('Y-m-d H:i:s') . "] 📅 Création des rappels automatiques...\n";
    
    $appointmentsNeedingReminders = $notificationModel->getAppointmentsNeedingReminders();
    $remindersCreated = 0;
    
    foreach ($appointmentsNeedingReminders as $appointment) {
        try {
            $reminderIds = $notificationModel->createReminderNotifications($appointment['id']);
            $remindersCreated += count($reminderIds);
            echo "   ✅ Rappels créés pour RDV {$appointment['id']} ({$appointment['patient_nom']} - " . date('d/m/Y H:i', strtotime($appointment['date_rendez_vous'])) . ")\n";
        } catch (Exception $e) {
            echo "   ❌ Erreur rappels RDV {$appointment['id']}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] 📊 $remindersCreated rappels créés pour " . count($appointmentsNeedingReminders) . " RDV\n";
    
    // 2. Traiter les notifications en attente
    echo "[" . date('Y-m-d H:i:s') . "] 📧 Traitement des notifications en attente...\n";
    
    $pendingNotifications = $notificationModel->getPendingNotifications();
    $processed = 0;
    $errors = 0;
    
    foreach ($pendingNotifications as $notification) {
        try {
            $success = sendNotification($notification, $emailService, $pdo);
            
            if ($success) {
                $notificationModel->markAsSent($notification['id']);
                $processed++;
                echo "   ✅ Envoyé: {$notification['type']} à {$notification['destinataire_nom']} ({$notification['sujet']})\n";
            } else {
                $errors++;
                echo "   ❌ Échec: {$notification['type']} à {$notification['destinataire_nom']}\n";
            }
        } catch (Exception $e) {
            $errors++;
            echo "   ❌ Erreur notification {$notification['id']}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] 📊 Notifications traitées: $processed envoyées, $errors erreurs\n";
    
    // 3. Nettoyer les anciennes notifications (une fois par jour)
    $hour = date('H');
    if ($hour == '02') { // 2h du matin
        echo "[" . date('Y-m-d H:i:s') . "] 🧹 Nettoyage des anciennes notifications...\n";
        
        $deleted = $notificationModel->cleanOldNotifications(30);
        echo "[" . date('Y-m-d H:i:s') . "] 📊 $deleted anciennes notifications supprimées\n";
    }
    
    // 4. Afficher les statistiques
    echo "[" . date('Y-m-d H:i:s') . "] 📈 Statistiques du jour:\n";
    
    $statsQuery = "
        SELECT 
            COUNT(*) as total_today,
            SUM(CASE WHEN statut = 'envoye' THEN 1 ELSE 0 END) as envoyes_today,
            SUM(CASE WHEN type = 'confirmation' THEN 1 ELSE 0 END) as confirmations_today,
            SUM(CASE WHEN type = 'rappel' THEN 1 ELSE 0 END) as rappels_today
        FROM notifications
        WHERE DATE(cree_le) = CURDATE()
    ";
    
    $stmt = $pdo->query($statsQuery);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "   📊 Total aujourd'hui: {$stats['total_today']}\n";
    echo "   📊 Envoyées: {$stats['envoyes_today']}\n";
    echo "   📊 Confirmations: {$stats['confirmations_today']}\n";
    echo "   📊 Rappels: {$stats['rappels_today']}\n";
    
    echo "[" . date('Y-m-d H:i:s') . "] ✅ Traitement terminé avec succès\n\n";
    
} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] ❌ Erreur fatale: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Envoyer une notification spécifique
 */
function sendNotification($notification, $emailService, $pdo) {
    try {
        // Préparer les données pour l'email
        $appointmentData = [
            'patient_nom' => $notification['destinataire_type'] === 'patient' ? $notification['destinataire_nom'] : getPatientName($notification['rdv_id'], $pdo),
            'patient_email' => $notification['destinataire_type'] === 'patient' ? $notification['destinataire_email'] : getPatientEmail($notification['rdv_id'], $pdo),
            'patient_telephone' => $notification['destinataire_type'] === 'patient' ? $notification['destinataire_telephone'] : getPatientPhone($notification['rdv_id'], $pdo),
            'medecin_nom' => $notification['destinataire_type'] === 'medecin' ? $notification['destinataire_nom'] : getDoctorName($notification['rdv_id'], $pdo),
            'medecin_email' => $notification['destinataire_type'] === 'medecin' ? $notification['destinataire_email'] : getDoctorEmail($notification['rdv_id'], $pdo),
            'date_rendez_vous' => $notification['date_rendez_vous'],
            'duree' => $notification['duree'],
            'type' => $notification['rdv_type'],
            'notes' => $notification['notes'],
            'specialite' => getDoctorSpecialty($notification['rdv_id'], $pdo)
        ];
        
        switch ($notification['type']) {
            case 'confirmation':
                if ($notification['destinataire_type'] === 'patient') {
                    return $emailService->sendAppointmentConfirmationToPatient($appointmentData);
                } else {
                    return $emailService->sendAppointmentNotificationToDoctor($appointmentData);
                }
                break;
                
            case 'notification':
                return $emailService->sendAppointmentNotificationToDoctor($appointmentData);
                break;
                
            case 'rappel':
                if ($notification['destinataire_type'] === 'patient') {
                    return $emailService->sendAppointmentReminderToPatient($appointmentData);
                } else {
                    return $emailService->sendAppointmentReminderToDoctor($appointmentData);
                }
                break;
                
            case 'annulation':
                return $emailService->sendAppointmentCancellation($appointmentData, $notification['destinataire_type'] === 'medecin');
                break;
                
            default:
                echo "   ⚠️  Type de notification non supporté: {$notification['type']}\n";
                return false;
        }
    } catch (Exception $e) {
        echo "   ❌ Erreur lors de l'envoi: " . $e->getMessage() . "\n";
        return false;
    }
}

// Fonctions utilitaires
function getPatientName($rdvId, $pdo) {
    $query = "SELECT CONCAT(p.nom, ' ', p.prenom) as nom FROM rendez_vous rdv INNER JOIN patients p ON rdv.id_patient = p.id WHERE rdv.id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$rdvId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['nom'] : '';
}

function getPatientEmail($rdvId, $pdo) {
    $query = "SELECT p.email FROM rendez_vous rdv INNER JOIN patients p ON rdv.id_patient = p.id WHERE rdv.id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$rdvId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['email'] : '';
}

function getPatientPhone($rdvId, $pdo) {
    $query = "SELECT p.telephone FROM rendez_vous rdv INNER JOIN patients p ON rdv.id_patient = p.id WHERE rdv.id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$rdvId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['telephone'] : '';
}

function getDoctorName($rdvId, $pdo) {
    $query = "SELECT CONCAT('Dr. ', u.nom, ' ', u.prenom) as nom FROM rendez_vous rdv INNER JOIN medecins m ON rdv.id_medecin = m.id INNER JOIN utilisateur u ON m.user_id = u.id WHERE rdv.id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$rdvId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['nom'] : '';
}

function getDoctorEmail($rdvId, $pdo) {
    $query = "SELECT u.email FROM rendez_vous rdv INNER JOIN medecins m ON rdv.id_medecin = m.id INNER JOIN utilisateur u ON m.user_id = u.id WHERE rdv.id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$rdvId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['email'] : '';
}

function getDoctorSpecialty($rdvId, $pdo) {
    $query = "SELECT m.specialite FROM rendez_vous rdv INNER JOIN medecins m ON rdv.id_medecin = m.id WHERE rdv.id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$rdvId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['specialite'] : '';
}
?>
