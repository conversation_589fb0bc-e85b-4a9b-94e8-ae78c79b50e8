import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import RegisterView from '@/views/RegisterView.vue' // adapte le chemin

// Vues principales avec chargement dynamique
const DoctorView = () => import('@/views/DoctorView.vue')
const PatientView = () => import('@/views/PatientView.vue')
const AdminView = () => import('@/views/AdminView.vue')
const Login = () => import('@/views/Login.vue')
const UnauthorizedView = () => import('@/views/UnauthorizedView.vue')
const SlotTypesDemo = () => import('@/views/SlotTypesDemo.vue')


const routes = [
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { hideForAuth: true } 
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterView
  },
  {
    path: '/unauthorized',
    name: 'unauthorized',
    component: Unauthorized<PERSON>iew
  },
  {
    path: '/doctor',
    name: 'doctor',
    component: <PERSON><PERSON>ie<PERSON>,
    meta: { 
      requiresAuth: true, 
      requiredRole: 'doctor',
      breadcrumb: 'Espace Médecin' 
    }
  },
  {
    path: '/patient',
    name: 'patient',
    component: PatientView,
    meta: { 
      requiresAuth: true, 
      requiredRole: 'patient',
      breadcrumb: 'Espace Patient' 
    }
  },
  {
    path: '/admin',
    name: 'admin',
    component: AdminView,
    meta: {
      requiresAuth: true,
      requiredRole: 'admin',
      breadcrumb: 'Administration'
    }
  },
  {
    path: '/demo-types',
    name: 'demo-types',
    component: SlotTypesDemo,
    meta: {
      breadcrumb: 'Démonstration Types de Créneaux'
    }
  },
  {
    path: '/',
    redirect: to => {
      const authStore = useAuthStore()
      if (!authStore.isLoggedIn) return { name: 'login' }
      
      switch(authStore.userRole) {
        case 'doctor': return { name: 'doctor' }
        case 'patient': return { name: 'patient' }
        case 'admin': return { name: 'admin' }
        default: return { name: 'unauthorized' }
      }
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return { 
        el: to.hash,
        behavior: 'smooth' 
      }
    } else {
      return { top: 0 }
    }
  }
})

router.beforeEach(async (to, from, next) => {
  console.log('Navigation vers:', to.path)
  const authStore = useAuthStore()
  
  // Attendre que le store soit prêt
  if (authStore.isLoading) {
    console.log('Store en cours de chargement...')
    await new Promise(resolve => {
      const unwatch = authStore.$subscribe(() => {
        if (!authStore.isLoading) {
          unwatch()
          resolve()
        }
      })
    })
  }

  console.log('État de connexion:', {
    isLoggedIn: authStore.isLoggedIn,
    userRole: authStore.userRole,
    requiresAuth: to.meta.requiresAuth,
    requiredRole: to.meta.requiredRole
  })

  // Redirection si déjà connecté
  if (to.meta.hideForAuth && authStore.isLoggedIn) {
    console.log('Utilisateur déjà connecté, redirection...')
    const role = authStore.userRole
    let redirectTo = '/'
    
    switch (role) {
      case 'doctor': redirectTo = '/doctor'; break
      case 'patient': redirectTo = '/patient'; break
      case 'admin': redirectTo = '/admin'; break
    }
    
    console.log('Redirection vers:', redirectTo)
    return next(redirectTo)
  }

  // Vérification des routes protégées
  if (to.meta.requiresAuth) {
    if (!authStore.isLoggedIn) {
      console.log('Accès non autorisé - redirection vers login')
      return next({ 
        name: 'login',
        query: { redirect: to.fullPath }
      })
    }

    if (to.meta.requiredRole && authStore.userRole !== to.meta.requiredRole) {
      console.log('Rôle incorrect - redirection vers unauthorized')
      return next('/unauthorized')
    }
  }

  console.log('Navigation autorisée')
  next()
})

export default router