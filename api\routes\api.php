<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit(0);
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../controllers/AppointmentController.php';
require_once __DIR__ . '/../controllers/DoctorController.php';
require_once __DIR__ . '/../controllers/PatientController.php';
require_once __DIR__ . '/../controllers/ConflictController.php';
require_once __DIR__ . '/../controllers/SystemController.php';
require_once __DIR__ . '/../controllers/AuditController.php';
require_once __DIR__ . '/../controllers/PatientHistoryController.php';

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

error_log("URI reçue: " . $uri);
error_log("Méthode: " . $method);

// Router simple
switch (true) {
    // Routes d'authentification
    case preg_match('#^/api/auth/login$#', $uri):
        if ($method === 'POST') {
            require __DIR__ . '/../auth/login.php';
        }
        break;

    case preg_match('#^/api/auth/register$#', $uri):
        if ($method === 'POST') {
            require __DIR__ . '/../register.php';
        }
        break;

    case preg_match('#^/api/doctor/statistics/(\d+)$#', $uri, $matches):
        error_log("Route statistics trouvée. ID: " . $matches[1]);
        $controller = new DoctorController();
        if ($method === 'GET') {
            error_log("Appel de getStatistics");
            $controller->getStatistics($matches[1]);
        }
        break;

    case preg_match('#^/api/appointments$#', $uri):
        require_once __DIR__ . '/../controllers/AppointmentController.php';
        $controller = new AppointmentController($pdo);
        if ($method === 'GET') {
            $controller->index();
        } elseif ($method === 'POST') {
            $controller->store();
        }
        break;

    case preg_match('#^/api/appointments/(\d+)$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/AppointmentController.php';
        $controller = new AppointmentController($pdo);
        $id = $matches[1];
        if ($method === 'GET') {
            $controller->show($id);
        } elseif ($method === 'PUT') {
            $controller->update($id);
        } elseif ($method === 'DELETE') {
            $controller->delete($id);
        }
        break;

    case preg_match('#^/api/appointments/doctor/(\d+)$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/AppointmentController.php';
        $controller = new AppointmentController($pdo);
        $doctorId = $matches[1];
        if ($method === 'GET') {
            $controller->getByDoctor($doctorId);
        }
        break;

    case preg_match('#^/api/appointments/availability/(\d+)$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/AppointmentController.php';
        $controller = new AppointmentController($pdo);
        $doctorId = $matches[1];
        if ($method === 'GET') {
            $controller->checkAvailability($doctorId);
        }
        break;

    case preg_match('#^/api/doctors/(\d+)/availability$#', $uri, $matches):
        $controller = new DoctorController();
        $controller->getAvailability($matches[1]);
        break;

    // Routes pour les conflits
    case preg_match('#^/api/conflicts$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'GET') {
            $controller->index();
        } elseif ($method === 'POST') {
            $controller->detectConflicts();
        }
        break;

    case preg_match('#^/api/conflicts/stats$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'GET') {
            $controller->getStats();
        }
        break;

    case preg_match('#^/api/conflicts/(\d+)/resolve$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        $conflictId = $matches[1];
        if ($method === 'PUT' || $method === 'POST') {
            $controller->resolve($conflictId);
        }
        break;

    case preg_match('#^/api/conflicts/(\d+)/ignore$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        $conflictId = $matches[1];
        if ($method === 'PUT' || $method === 'POST') {
            $controller->ignore($conflictId);
        }
        break;

    case preg_match('#^/api/conflicts/auto-cleanup$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'DELETE') {
            $controller->autoCleanup();
        }
        break;

    case preg_match('#^/api/conflicts/trends$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'GET') {
            $controller->analyzeTrends();
        }
        break;

    case preg_match('#^/api/conflicts/schedule-notifications$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'POST') {
            $controller->scheduleNotifications();
        }
        break;

    case preg_match('#^/api/conflicts/notify$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'POST') {
            $controller->notifyConflicts();
        }
        break;

    case preg_match('#^/api/conflicts/expired$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'DELETE') {
            $controller->cleanupExpiredConflicts();
        }
        break;

    case preg_match('#^/api/conflicts/detect$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'POST') {
            $controller->detectConflicts();
        }
        break;

    case preg_match('#^/api/conflicts/validate-slot$#', $uri):
        require_once __DIR__ . '/../controllers/ConflictController.php';
        $controller = new ConflictController($pdo);
        if ($method === 'POST') {
            $controller->validateTimeSlot();
        }
        break;
    
    // Ajout des nouvelles routes
    case preg_match('#^/api/system/settings$#', $uri):
        $controller = new SystemController();
        if ($method === 'GET') $controller->getSettings();
        if ($method === 'POST') $controller->updateSettings();
        break;

    case preg_match('#^/api/patients$#', $uri):
        $controller = new PatientController();
        if ($method === 'GET') $controller->index();
        if ($method === 'POST') $controller->store();
        break;

    case preg_match('#^/api/patients/(\d+)/history$#', $uri, $matches):
        $controller = new PatientHistoryController();
        if ($method === 'GET') $controller->getHistory($matches[1]);
        break;

    case preg_match('#^/api/patients/(\d+)/archive$#', $uri, $matches):
        $controller = new PatientController();
        if ($method === 'PATCH') {
            $controller->toggleArchive($matches[1]);
        } elseif ($method === 'OPTIONS') {
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, PATCH, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization');
            header('Access-Control-Allow-Origin: http://localhost:3000');
            header('Access-Control-Allow-Credentials: true');
            http_response_code(200);
            exit();
        }
        break;

    case preg_match('#^/api/patients/(\d+)$#', $uri, $matches):
        $controller = new PatientController();
        $id = $matches[1];
        if ($method === 'GET') $controller->show($id);
        if ($method === 'PUT') $controller->update($id);
        if ($method === 'DELETE') $controller->delete($id);
        break;
    
    case preg_match('#^/api/doctors$#', $uri):
        $controller = new DoctorController();
        if ($method === 'GET') $controller->getAll();
        if ($method === 'POST') $controller->create();
        break;

    case preg_match('#^/api/doctors/(\d+)$#', $uri, $matches):
        $controller = new DoctorController();
        $id = $matches[1];
        if ($method === 'GET') $controller->getById($id);
        if ($method === 'PUT') $controller->update($id);
        if ($method === 'DELETE') $controller->delete($id);
        break;
    
    case preg_match('#^/api/audit/logs$#', $uri):
        $controller = new AuditController();
        if ($method === 'GET') $controller->getLogs();
        break;
    
    case preg_match('#^/api/doctors?/statistics/(\d+)$#i', $uri, $matches):
        $controller = new DoctorController();
        if ($method === 'GET') $controller->getStatistics($matches[1]);
        break;
    
    case preg_match('#^/api/dashboard/advanced-stats$#', $uri):
        $controller = new SystemController();
        if ($method === 'GET') {
            $period = $_GET['period'] ?? 'month';
            $controller->getAdvancedStats($period);
        }
        break;

    // Routes pour les créneaux suggérés
    case preg_match('#^/api/suggested-slots$#', $uri):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        if ($method === 'GET') {
            $controller->index();
        } elseif ($method === 'POST') {
            $controller->store();
        }
        break;

    case preg_match('#^/api/suggested-slots/stats$#', $uri):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        if ($method === 'GET') {
            $controller->getStats();
        }
        break;

    case preg_match('#^/api/suggested-slots/(\d+)$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        $slotId = $matches[1];
        if ($method === 'DELETE') {
            $controller->delete($slotId);
        }
        break;

    case preg_match('#^/api/suggested-slots/(\d+)/accept$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        $slotId = $matches[1];
        if ($method === 'PUT' || $method === 'POST') {
            $controller->updateAcceptance($slotId);
        }
        break;

    case preg_match('#^/api/suggested-slots/patient/(\d+)$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        $patientId = $matches[1];
        if ($method === 'GET') {
            $controller->getByPatient($patientId);
        }
        break;

    case preg_match('#^/api/suggested-slots/clean-expired$#', $uri):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        if ($method === 'POST') {
            $controller->cleanExpired();
        }
        break;

    case preg_match('#^/api/suggested-slots/doctor/(\d+)$#', $uri, $matches):
        require_once __DIR__ . '/../controllers/SuggestedSlotController.php';
        $controller = new SuggestedSlotController($pdo);
        $doctorId = $matches[1];
        if ($method === 'GET') {
            $_GET['medecin_id'] = $doctorId; // Passer l'ID du médecin
            $controller->index();
        }
        break;



    default:
        error_log("Aucune route trouvée pour: " . $uri);
        http_response_code(404);
        echo json_encode(['error' => 'Route not found', 'uri' => $uri, 'method' => $method]);
}
?>