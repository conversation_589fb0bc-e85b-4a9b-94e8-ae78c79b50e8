<template>
  <nav class="pagination" aria-label="Pagination">
    <button
      class="pagination-btn"
      :disabled="currentPage === 1"
      @click="changePage(currentPage - 1)"
      aria-label="Page précédente"
    >
      &laquo;
    </button>

    <button
      v-for="page in pages"
      :key="page"
      class="pagination-btn"
      :class="{ active: page === currentPage }"
      @click="changePage(page)"
      :aria-current="page === currentPage ? 'page' : null"
    >
      {{ page }}
    </button>

    <button
      class="pagination-btn"
      :disabled="currentPage === totalPages"
      @click="changePage(currentPage + 1)"
      aria-label="Page suivante"
    >
      &raquo;
    </button>
  </nav>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  currentPage: { type: Number, required: true },
  totalPages: { type: Number, required: true },
  maxVisible: { type: Number, default: 5 } // nombre max de boutons visibles
})

const emit = defineEmits(['update:currentPage'])

function changePage(page) {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('update:currentPage', page)
  }
}

// Calcul des pages à afficher (pagination intelligente)
const pages = computed(() => {
  const half = Math.floor(props.maxVisible / 2)
  let start = Math.max(1, props.currentPage - half)
  let end = Math.min(props.totalPages, start + props.maxVisible - 1)
  if (end - start + 1 < props.maxVisible) {
    start = Math.max(1, end - props.maxVisible + 1)
  }
  return Array.from({ length: end - start + 1 }, (_, i) => start + i)
})
</script>

<style scoped>
.pagination {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
}
.pagination-btn {
  border: none;
  background: #f1f5f9;
  color: #334155;
  padding: 0.5em 0.9em;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.15s;
}
.pagination-btn.active,
.pagination-btn:focus {
  background: #2563eb;
  color: #fff;
  outline: none;
}
.pagination-btn:disabled {
  background: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
}
</style>
