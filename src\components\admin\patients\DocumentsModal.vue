<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Documents du Patient</h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Barre d'outils -->
      <div class="toolbar">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            type="text" 
            v-model="searchQuery" 
            placeholder="Rechercher un document..."
          />
        </div>
        <div class="filters">
          <select v-model="selectedType">
            <option value="">Tous les types</option>
            <option value="prescription">Ordonnances</option>
            <option value="report">Comptes rendus</option>
            <option value="analysis">Analyses</option>
            <option value="certificate">Certificats</option>
            <option value="other">Autres</option>
          </select>
        </div>
        <button class="btn-primary" @click="showUploadModal = true">
          <i class="fas fa-upload"></i>
          Ajouter un document
        </button>
      </div>

      <!-- Liste des documents -->
      <div class="documents-grid">
        <div v-if="loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          Chargement des documents...
        </div>
        <div v-else-if="error" class="error-state">
          <i class="fas fa-exclamation-circle"></i>
          {{ error }}
        </div>
        <div v-else-if="filteredDocuments.length === 0" class="empty-state">
          <i class="fas fa-folder-open"></i>
          <p>Aucun document trouvé</p>
        </div>
        <div v-else class="document-cards">
          <div 
            v-for="doc in filteredDocuments" 
            :key="doc.id" 
            class="document-card"
            :class="doc.type"
          >
            <div class="document-icon">
              <i :class="getDocumentIcon(doc.type)"></i>
            </div>
            <div class="document-info">
              <h3>{{ doc.name }}</h3>
              <p class="document-meta">
                <span class="document-type">{{ getDocumentTypeLabel(doc.type) }}</span>
                <span class="document-date">{{ formatDate(doc.date) }}</span>
              </p>
              <p class="document-description">{{ doc.description }}</p>
            </div>
            <div class="document-actions">
              <button @click="previewDocument(doc)" title="Aperçu">
                <i class="fas fa-eye"></i>
              </button>
              <button @click="downloadDocument(doc)" title="Télécharger">
                <i class="fas fa-download"></i>
              </button>
              <button @click="deleteDocument(doc)" title="Supprimer">
                <i class="fas fa-trash-alt"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal d'upload -->
      <div v-if="showUploadModal" class="upload-modal">
        <div class="upload-content">
          <h3>Ajouter un document</h3>
          <form @submit.prevent="uploadDocument">
            <div class="form-group">
              <label>Type de document</label>
              <select v-model="newDocument.type" required>
                <option value="prescription">Ordonnance</option>
                <option value="report">Compte rendu</option>
                <option value="analysis">Analyse</option>
                <option value="certificate">Certificat</option>
                <option value="other">Autre</option>
              </select>
            </div>
            <div class="form-group">
              <label>Nom du document</label>
              <input type="text" v-model="newDocument.name" required>
            </div>
            <div class="form-group">
              <label>Description</label>
              <textarea v-model="newDocument.description" rows="3"></textarea>
            </div>
            <div class="form-group">
              <label>Fichier</label>
              <input 
                type="file" 
                @change="handleFileSelect" 
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                required
              >
            </div>
            <div class="form-actions">
              <button type="button" class="btn-secondary" @click="showUploadModal = false">
                Annuler
              </button>
              <button type="submit" class="btn-primary" :disabled="uploading">
                <i v-if="uploading" class="fas fa-spinner fa-spin"></i>
                <span v-else>Enregistrer</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Modal de prévisualisation -->
      <div v-if="previewUrl" class="preview-modal">
        <div class="preview-content">
          <div class="preview-header">
            <h3>Aperçu du document</h3>
            <button class="close-btn" @click="previewUrl = null">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="preview-body">
            <iframe :src="previewUrl" frameborder="0"></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { formatDate } from '@/utils/dateHelpers'
import { patientService } from '@/services/api'

const props = defineProps({
  patient: {
    type: Object,
    required: true
  }
})

// État
const loading = ref(false)
const error = ref(null)
const documents = ref([])
const searchQuery = ref('')
const selectedType = ref('')
const showUploadModal = ref(false)
const uploading = ref(false)
const previewUrl = ref(null)

const newDocument = ref({
  type: '',
  name: '',
  description: '',
  file: null
})

// Computed
const filteredDocuments = computed(() => {
  if (!documents.value) return []
  
  return documents.value.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesType = !selectedType.value || doc.type === selectedType.value
    return matchesSearch && matchesType
  }).sort((a, b) => new Date(b.date) - new Date(a.date))
})

// Méthodes
const loadDocuments = async () => {
  loading.value = true
  error.value = null
  try {
    const response = await patientService.getPatientDocuments(props.patient.id)
    documents.value = response.data
  } catch (err) {
    console.error('Erreur chargement documents:', err)
    error.value = 'Erreur lors du chargement des documents'
  } finally {
    loading.value = false
  }
}

const getDocumentIcon = (type) => {
  const icons = {
    prescription: 'fas fa-prescription',
    report: 'fas fa-file-medical-alt',
    analysis: 'fas fa-vial',
    certificate: 'fas fa-certificate',
    other: 'fas fa-file-alt'
  }
  return icons[type] || 'fas fa-file'
}

const getDocumentTypeLabel = (type) => {
  const labels = {
    prescription: 'Ordonnance',
    report: 'Compte rendu',
    analysis: 'Analyse',
    certificate: 'Certificat',
    other: 'Autre'
  }
  return labels[type] || type
}

const handleFileSelect = (event) => {
  newDocument.value.file = event.target.files[0]
}

const uploadDocument = async () => {
  if (!newDocument.value.file) return

  uploading.value = true
  const formData = new FormData()
  formData.append('file', newDocument.value.file)
  formData.append('type', newDocument.value.type)
  formData.append('name', newDocument.value.name)
  formData.append('description', newDocument.value.description)

  try {
    await patientService.uploadDocument(props.patient.id, formData)
    await loadDocuments()
    showUploadModal.value = false
    newDocument.value = { type: '', name: '', description: '', file: null }
  } catch (err) {
    console.error('Erreur upload:', err)
    error.value = 'Erreur lors de l\'upload du document'
  } finally {
    uploading.value = false
  }
}

const previewDocument = async (doc) => {
  try {
    const response = await patientService.previewDocument(doc.id)
    previewUrl.value = URL.createObjectURL(response)
  } catch (err) {
    console.error('Erreur prévisualisation:', err)
    error.value = 'Erreur lors de la prévisualisation du document'
  }
}

const downloadDocument = async (doc) => {
  try {
    const response = await patientService.downloadDocument(doc.id)
    const url = URL.createObjectURL(response)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', doc.name)
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (err) {
    console.error('Erreur téléchargement:', err)
    error.value = 'Erreur lors du téléchargement du document'
  }
}

const deleteDocument = async (doc) => {
  if (!confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return

  try {
    await patientService.deleteDocument(doc.id)
    await loadDocuments()
  } catch (err) {
    console.error('Erreur suppression:', err)
    error.value = 'Erreur lors de la suppression du document'
  }
}

// Chargement initial
onMounted(loadDocuments)
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
}

.toolbar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 200px;
  position: relative;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.search-box input {
  width: 100%;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filters select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  min-width: 150px;
}

.documents-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.document-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  display: flex;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.document-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.document-card.prescription .document-icon { background: #3b82f6; }
.document-card.report .document-icon { background: #10b981; }
.document-card.analysis .document-icon { background: #f59e0b; }
.document-card.certificate .document-icon { background: #6366f1; }
.document-card.other .document-icon { background: #64748b; }

.document-info {
  flex: 1;
  min-width: 0;
}

.document-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  color: #1e293b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.document-type {
  color: #3b82f6;
  font-weight: 500;
}

.document-description {
  font-size: 0.9rem;
  color: #475569;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.document-actions {
  display: flex;
  gap: 0.5rem;
}

.document-actions button {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.document-actions button:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.loading-state, .error-state, .empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.loading-state i, .error-state i {
  margin-right: 0.5rem;
}

.empty-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #94a3b8;
}

.upload-modal, .preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.upload-content, .preview-content {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  width: 90%;
  max-width: 500px;
}

.preview-content {
  max-width: 800px;
  height: 90vh;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.preview-body {
  flex: 1;
  min-height: 0;
}

.preview-body iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #1e293b;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group textarea {
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-secondary {
  background: #f1f5f9;
  color: #64748b;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary:hover {
  background: #e2e8f0;
}

@media (max-width: 640px) {
  .toolbar {
    flex-direction: column;
  }

  .search-box {
    width: 100%;
  }

  .filters {
    width: 100%;
  }

  .filters select {
    width: 100%;
  }

  .documents-grid {
    grid-template-columns: 1fr;
  }
}
</style> 