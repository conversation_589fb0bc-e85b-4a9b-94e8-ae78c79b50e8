<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

require_once __DIR__ . '/../controllers/DoctorStatisticsController.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$controller = new DoctorStatisticsController();

// Route pour les statistiques générales
if (preg_match('/^\/api\/doctor\/statistics\/(\d+)$/', $_SERVER['REQUEST_URI'], $matches)) {
    $doctorId = $matches[1];
    try {
        $stats = $controller->getStatistics($doctorId);
        echo json_encode($stats);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// Route pour les statistiques par spécialité
else if (preg_match('/^\/api\/doctor\/statistics\/(\d+)\/speciality$/', $_SERVER['REQUEST_URI'], $matches)) {
    $doctorId = $matches[1];
    try {
        $stats = $controller->getSpecialityStats($doctorId);
        echo json_encode($stats);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// Route pour l'analyse des créneaux horaires
else if (preg_match('/^\/api\/doctor\/statistics\/(\d+)\/timeslots$/', $_SERVER['REQUEST_URI'], $matches)) {
    $doctorId = $matches[1];
    try {
        $stats = $controller->getTimeSlotAnalysis($doctorId);
        echo json_encode($stats);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// Route non trouvée
else {
    http_response_code(404);
    echo json_encode(['error' => 'Route non trouvée']);
} 