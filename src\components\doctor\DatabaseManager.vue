<template>
  <div class="database-manager">
    <!-- En-tête -->
    <div class="manager-header">
      <h2>
        <i class="fas fa-database"></i>
        Gestion de la Base de Données
      </h2>
      <div class="header-actions">
        <button @click="refreshData" class="refresh-btn" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Actualiser
        </button>
        <button @click="showMaintenanceModal = true" class="maintenance-btn">
          <i class="fas fa-tools"></i>
          Maintenance
        </button>
      </div>
    </div>

    <!-- Notifications -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <div class="notification-content">
        <i :class="notification.icon"></i>
        <span>{{ notification.message }}</span>
      </div>
      <button @click="hideNotification" class="notification-close">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Onglets -->
    <div class="tabs-container">
      <div class="tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          class="tab-btn"
          :class="{ active: activeTab === tab.id }"
        >
          <i :class="tab.icon"></i>
          {{ tab.label }}
        </button>
      </div>
    </div>

    <!-- Contenu des onglets -->
    <div class="tab-content">
      <!-- Vue d'ensemble -->
      <div v-if="activeTab === 'overview'" class="overview-tab">
        <div class="stats-grid">
          <!-- Statistiques de la base -->
          <div class="stat-card database-info">
            <div class="stat-header">
              <h3><i class="fas fa-server"></i> Informations Base</h3>
              <div class="data-source-badge">
                <i class="fas fa-database"></i>
                Données réelles
              </div>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="label">Nom :</span>
                <span class="value">{{ overview.database?.name || 'N/A' }}</span>
              </div>
              <div class="stat-item">
                <span class="label">Taille :</span>
                <span class="value">{{ overview.database?.size_mb || 0 }} MB</span>
              </div>
              <div class="stat-item">
                <span class="label">Tables :</span>
                <span class="value">{{ overview.database?.table_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="label">Total enregistrements :</span>
                <span class="value">{{ totalRecords }}</span>
              </div>
            </div>
          </div>

          <!-- Statistiques des tables -->
          <div class="stat-card tables-stats">
            <div class="stat-header">
              <h3><i class="fas fa-table"></i> Données Principales</h3>
            </div>
            <div class="tables-list">
              <div 
                v-for="(table, key) in overview.tables" 
                :key="key"
                class="table-item"
                :style="{ borderLeftColor: getTableColor(key) }"
              >
                <div class="table-info">
                  <span class="table-name">{{ table.label }}</span>
                  <span class="table-count">{{ table.count }} enregistrements</span>
                </div>
                <div class="table-actions">
                  <button @click="viewTable(key)" class="view-btn">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance -->
          <div class="stat-card performance-stats">
            <div class="stat-header">
              <h3><i class="fas fa-chart-line"></i> Performance (24h)</h3>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="label">Nouveaux RDV :</span>
                <span class="value">{{ overview.performance?.recent_appointments || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="label">Conflits détectés :</span>
                <span class="value">{{ overview.performance?.recent_conflicts || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="label">Utilisateurs actifs :</span>
                <span class="value">{{ overview.performance?.active_users || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tables -->
      <div v-if="activeTab === 'tables'" class="tables-tab">
        <div class="tables-header">
          <h3>Liste des Tables</h3>
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input 
              v-model="tableSearch" 
              type="text" 
              placeholder="Rechercher une table..."
              class="search-input"
            >
          </div>
        </div>

        <div class="tables-grid">
          <div 
            v-for="table in filteredTables" 
            :key="table.table_name"
            class="table-card"
            @click="selectTable(table)"
          >
            <div class="table-card-header">
              <h4>{{ table.table_name }}</h4>
              <span class="table-size">{{ table.size_mb }} MB</span>
            </div>
            <div class="table-card-content">
              <div class="table-stat">
                <i class="fas fa-list-ol"></i>
                <span>{{ table.table_rows }} lignes</span>
              </div>
              <div class="table-comment">
                {{ table.table_comment || 'Aucune description' }}
              </div>
              <div class="table-dates">
                <small>Créée : {{ formatDate(table.create_time) }}</small>
                <small>Modifiée : {{ formatDate(table.update_time) }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Maintenance -->
      <div v-if="activeTab === 'maintenance'" class="maintenance-tab">
        <div class="maintenance-grid">
          <div class="maintenance-card">
            <div class="maintenance-header">
              <h3><i class="fas fa-broom"></i> Optimisation</h3>
            </div>
            <div class="maintenance-content">
              <p>Optimise les tables pour améliorer les performances</p>
              <button @click="optimizeDatabase" class="action-btn optimize" :disabled="optimizing">
                <i class="fas fa-rocket" :class="{ 'fa-spin': optimizing }"></i>
                {{ optimizing ? 'Optimisation...' : 'Optimiser' }}
              </button>
            </div>
          </div>

          <div class="maintenance-card">
            <div class="maintenance-header">
              <h3><i class="fas fa-save"></i> Sauvegarde</h3>
            </div>
            <div class="maintenance-content">
              <p>Crée une sauvegarde complète de la base de données</p>
              <button @click="createBackup" class="action-btn backup" :disabled="backing">
                <i class="fas fa-download" :class="{ 'fa-spin': backing }"></i>
                {{ backing ? 'Sauvegarde...' : 'Sauvegarder' }}
              </button>
            </div>
          </div>

          <div class="maintenance-card">
            <div class="maintenance-header">
              <h3><i class="fas fa-shield-alt"></i> Intégrité</h3>
            </div>
            <div class="maintenance-content">
              <p>Vérifie l'intégrité des données et des index</p>
              <button @click="checkIntegrity" class="action-btn integrity" :disabled="checking">
                <i class="fas fa-search" :class="{ 'fa-spin': checking }"></i>
                {{ checking ? 'Vérification...' : 'Vérifier' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Résultats de maintenance -->
        <div v-if="maintenanceResults.length > 0" class="maintenance-results">
          <h3>Résultats</h3>
          <div class="results-list">
            <div 
              v-for="(result, index) in maintenanceResults" 
              :key="index"
              class="result-item"
              :class="result.type"
            >
              <i :class="getResultIcon(result.type)"></i>
              <span>{{ result.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de détails de table -->
    <div v-if="selectedTable" class="modal-overlay" @click="closeTableModal">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>
            <i class="fas fa-table"></i>
            Table : {{ selectedTable.table_name }}
          </h3>
          <button @click="closeTableModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="table-details-tabs">
            <button 
              @click="tableDetailTab = 'structure'"
              class="tab-btn"
              :class="{ active: tableDetailTab === 'structure' }"
            >
              Structure
            </button>
            <button 
              @click="tableDetailTab = 'data'"
              class="tab-btn"
              :class="{ active: tableDetailTab === 'data' }"
            >
              Données
            </button>
          </div>

          <div v-if="tableDetailTab === 'structure'" class="structure-view">
            <div v-if="tableStructure.length > 0" class="structure-table">
              <table>
                <thead>
                  <tr>
                    <th>Colonne</th>
                    <th>Type</th>
                    <th>Null</th>
                    <th>Clé</th>
                    <th>Défaut</th>
                    <th>Extra</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="column in tableStructure" :key="column.Field">
                    <td class="column-name">{{ column.Field }}</td>
                    <td class="column-type">{{ column.Type }}</td>
                    <td class="column-null">{{ column.Null }}</td>
                    <td class="column-key">{{ column.Key }}</td>
                    <td class="column-default">{{ column.Default || 'NULL' }}</td>
                    <td class="column-extra">{{ column.Extra }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div v-if="tableDetailTab === 'data'" class="data-view">
            <div v-if="tableData.rows && tableData.rows.length > 0" class="data-container">
              <div class="data-pagination">
                <span>
                  Page {{ tableData.pagination?.current_page || 1 }} sur {{ tableData.pagination?.total_pages || 1 }}
                  ({{ tableData.pagination?.total || 0 }} enregistrements)
                </span>
                <div class="pagination-controls">
                  <button 
                    @click="loadTableData(selectedTable.table_name, (tableData.pagination?.current_page || 1) - 1)"
                    :disabled="(tableData.pagination?.current_page || 1) <= 1"
                    class="page-btn"
                  >
                    <i class="fas fa-chevron-left"></i>
                  </button>
                  <button 
                    @click="loadTableData(selectedTable.table_name, (tableData.pagination?.current_page || 1) + 1)"
                    :disabled="(tableData.pagination?.current_page || 1) >= (tableData.pagination?.total_pages || 1)"
                    class="page-btn"
                  >
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </div>
              </div>
              
              <div class="data-table-container">
                <table class="data-table">
                  <thead>
                    <tr>
                      <th v-for="column in Object.keys(tableData.rows[0])" :key="column">
                        {{ column }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, index) in tableData.rows" :key="index">
                      <td v-for="(value, column) in row" :key="column" class="data-cell">
                        {{ formatCellValue(value) }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else class="no-data">
              <i class="fas fa-inbox"></i>
              <p>Aucune donnée disponible</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import databaseService from '@/services/databaseService'

// Variables réactives
const loading = ref(false)
const activeTab = ref('overview')
const tableSearch = ref('')
const selectedTable = ref(null)
const tableDetailTab = ref('structure')

// Données
const overview = ref({})
const tables = ref([])
const tableStructure = ref([])
const tableData = ref({})

// États de maintenance
const optimizing = ref(false)
const backing = ref(false)
const checking = ref(false)
const maintenanceResults = ref([])

// Notifications
const notification = ref({
  show: false,
  type: 'success',
  message: '',
  icon: 'fas fa-check-circle'
})

// Configuration des onglets
const tabs = [
  { id: 'overview', label: 'Vue d\'ensemble', icon: 'fas fa-chart-pie' },
  { id: 'tables', label: 'Tables', icon: 'fas fa-table' },
  { id: 'maintenance', label: 'Maintenance', icon: 'fas fa-tools' }
]

// Computed properties
const filteredTables = computed(() => {
  if (!tableSearch.value) return tables.value

  return tables.value.filter(table =>
    table.table_name.toLowerCase().includes(tableSearch.value.toLowerCase()) ||
    (table.table_comment && table.table_comment.toLowerCase().includes(tableSearch.value.toLowerCase()))
  )
})

const totalRecords = computed(() => {
  if (!overview.value.tables) return 0

  return Object.values(overview.value.tables).reduce((total, table) => {
    return total + (table.count || 0)
  }, 0)
})

// Méthodes
const loadOverview = async () => {
  try {
    const response = await databaseService.getOverview()
    overview.value = response.data

    // Afficher une notification selon la source des données
    if (response.source === 'database') {
      console.log('✅ Données chargées depuis la vraie base de données')
    } else if (response.source === 'fallback') {
      showNotification('warning', 'Données de secours utilisées (API non disponible)', 'fas fa-exclamation-triangle')
    }
  } catch (error) {
    console.error('Erreur lors du chargement de la vue d\'ensemble:', error)
    showNotification('error', 'Erreur lors du chargement des statistiques', 'fas fa-exclamation-circle')
  }
}

const loadTables = async () => {
  try {
    const response = await databaseService.getTables()
    tables.value = response.data

    // Afficher une notification selon la source des données
    if (response.source === 'database') {
      console.log('✅ Tables chargées depuis la vraie base de données')
    } else if (response.source === 'fallback') {
      showNotification('warning', 'Tables de secours utilisées (API non disponible)', 'fas fa-exclamation-triangle')
    }
  } catch (error) {
    console.error('Erreur lors du chargement des tables:', error)
    showNotification('error', 'Erreur lors du chargement des tables', 'fas fa-exclamation-circle')
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([loadOverview(), loadTables()])
    showNotification('success', 'Données actualisées avec succès', 'fas fa-check-circle')
  } catch (error) {
    showNotification('error', 'Erreur lors de l\'actualisation', 'fas fa-exclamation-circle')
  } finally {
    loading.value = false
  }
}

const selectTable = async (table) => {
  selectedTable.value = table
  tableDetailTab.value = 'structure'
  
  try {
    // Charger la structure
    const structureResponse = await databaseService.getTableStructure(table.table_name)
    tableStructure.value = structureResponse.data.columns
    
    // Charger les données
    const dataResponse = await databaseService.getTableData(table.table_name, 1, 20)
    tableData.value = dataResponse.data
  } catch (error) {
    console.error('Erreur lors du chargement des détails de la table:', error)
    showNotification('error', 'Erreur lors du chargement des détails', 'fas fa-exclamation-circle')
  }
}

const loadTableData = async (tableName, page) => {
  try {
    const response = await databaseService.getTableData(tableName, page, 20)
    tableData.value = response.data
  } catch (error) {
    console.error('Erreur lors du chargement des données:', error)
    showNotification('error', 'Erreur lors du chargement des données', 'fas fa-exclamation-circle')
  }
}

const closeTableModal = () => {
  selectedTable.value = null
  tableStructure.value = []
  tableData.value = {}
}

const viewTable = (tableName) => {
  const table = tables.value.find(t => t.table_name === tableName)
  if (table) {
    selectTable(table)
  }
}

// Méthodes de maintenance
const optimizeDatabase = async () => {
  optimizing.value = true
  try {
    const response = await databaseService.optimize()
    maintenanceResults.value.push({
      type: 'success',
      message: response.data.message
    })
    showNotification('success', 'Optimisation terminée avec succès', 'fas fa-rocket')
  } catch (error) {
    maintenanceResults.value.push({
      type: 'error',
      message: 'Erreur lors de l\'optimisation'
    })
    showNotification('error', 'Erreur lors de l\'optimisation', 'fas fa-exclamation-circle')
  } finally {
    optimizing.value = false
  }
}

const createBackup = async () => {
  backing.value = true
  try {
    const response = await databaseService.createBackup()
    maintenanceResults.value.push({
      type: 'success',
      message: `Sauvegarde créée: ${response.data.backup_name} (${response.data.size_mb} MB)`
    })
    showNotification('success', 'Sauvegarde créée avec succès', 'fas fa-download')
  } catch (error) {
    maintenanceResults.value.push({
      type: 'error',
      message: 'Erreur lors de la création de la sauvegarde'
    })
    showNotification('error', 'Erreur lors de la sauvegarde', 'fas fa-exclamation-circle')
  } finally {
    backing.value = false
  }
}

const checkIntegrity = async () => {
  checking.value = true
  try {
    const response = await databaseService.checkIntegrity()
    const data = response.data
    
    maintenanceResults.value.push({
      type: 'info',
      message: `Vérification terminée: ${data.tables_checked} tables, ${data.errors_found} erreurs, ${data.warnings} avertissements`
    })
    
    if (data.issues) {
      data.issues.forEach(issue => {
        maintenanceResults.value.push({
          type: issue.type,
          message: `${issue.table}: ${issue.message}`
        })
      })
    }
    
    showNotification('success', 'Vérification d\'intégrité terminée', 'fas fa-shield-alt')
  } catch (error) {
    maintenanceResults.value.push({
      type: 'error',
      message: 'Erreur lors de la vérification d\'intégrité'
    })
    showNotification('error', 'Erreur lors de la vérification', 'fas fa-exclamation-circle')
  } finally {
    checking.value = false
  }
}

// Méthodes utilitaires
const getTableColor = (tableName) => {
  return databaseService.getTableColor(tableName)
}

const formatDate = (dateString) => {
  return databaseService.formatDate(dateString)
}

const formatCellValue = (value) => {
  if (value === null || value === undefined) return 'NULL'
  if (typeof value === 'string' && value.length > 50) {
    return value.substring(0, 50) + '...'
  }
  return value
}

const getResultIcon = (type) => {
  const icons = {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle'
  }
  return icons[type] || 'fas fa-info-circle'
}

// Notifications
const showNotification = (type, message, icon) => {
  notification.value = {
    show: true,
    type,
    message,
    icon
  }
  
  setTimeout(() => {
    hideNotification()
  }, 5000)
}

const hideNotification = () => {
  notification.value.show = false
}

// Hooks de cycle de vie
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.database-manager {
  padding: 1.5rem;
  background: #f8f9fa;
  min-height: 100vh;
}

/* En-tête */
.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.manager-header h2 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.manager-header h2 i {
  color: #3498db;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.refresh-btn, .maintenance-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.refresh-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.maintenance-btn {
  background: linear-gradient(135deg, #e67e22, #d35400);
  color: white;
}

.maintenance-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInRight 0.3s ease-out;
}

.notification.success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.notification.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.notification.warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.notification.info {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.notification-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Onglets */
.tabs-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 12px 12px 0 0;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

/* Contenu des onglets */
.tab-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

/* Vue d'ensemble */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-header {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.data-source-badge {
  background: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  border: 1px solid rgba(46, 204, 113, 0.3);
}

.stat-content {
  padding: 1.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item .label {
  font-weight: 500;
  color: #6c757d;
}

.stat-item .value {
  font-weight: 600;
  color: #2c3e50;
}

/* Tables */
.tables-list {
  max-height: 400px;
  overflow-y: auto;
}

.table-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-left: 4px solid #3498db;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.table-item:hover {
  background: #f8f9fa;
}

.table-info .table-name {
  font-weight: 600;
  color: #2c3e50;
  display: block;
}

.table-info .table-count {
  font-size: 0.9rem;
  color: #6c757d;
}

.view-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  transform: scale(1.1);
}

/* Tables tab */
.tables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 1rem;
  color: #6c757d;
}

.search-input {
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  width: 300px;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid #3498db;
}

.table-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.table-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.table-card-header h4 {
  margin: 0;
  color: #2c3e50;
  font-family: 'Courier New', monospace;
}

.table-size {
  background: #e9ecef;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.table-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #6c757d;
}

.table-comment {
  font-style: italic;
  color: #6c757d;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.table-dates {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.table-dates small {
  color: #adb5bd;
  font-size: 0.8rem;
}

/* Maintenance */
.maintenance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.maintenance-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.maintenance-header {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  color: white;
  padding: 1rem 1.5rem;
}

.maintenance-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.maintenance-content {
  padding: 1.5rem;
  text-align: center;
}

.maintenance-content p {
  margin-bottom: 1.5rem;
  color: #6c757d;
}

.action-btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn.optimize {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.action-btn.backup {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.action-btn.integrity {
  background: linear-gradient(135deg, #e67e22, #d35400);
  color: white;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Résultats de maintenance */
.maintenance-results {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.maintenance-results h3 {
  margin-top: 0;
  color: #2c3e50;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.result-item.success {
  background: #d4edda;
  border-left-color: #28a745;
  color: #155724;
}

.result-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
  color: #721c24;
}

.result-item.warning {
  background: #fff3cd;
  border-left-color: #ffc107;
  color: #856404;
}

.result-item.info {
  background: #d1ecf1;
  border-left-color: #17a2b8;
  color: #0c5460;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.large {
  width: 90%;
  max-width: 1200px;
}

.modal-header {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  flex: 1;
  overflow: auto;
  padding: 1.5rem;
}

/* Détails de table */
.table-details-tabs {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
}

.table-details-tabs .tab-btn {
  flex: none;
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  border-radius: 0;
}

.table-details-tabs .tab-btn.active {
  background: none;
  color: #3498db;
  border-bottom: 2px solid #3498db;
}

/* Structure */
.structure-table {
  overflow-x: auto;
}

.structure-table table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.structure-table th,
.structure-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.structure-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.column-name {
  font-weight: 600;
  color: #2c3e50;
}

.column-type {
  color: #e67e22;
}

.column-key {
  color: #e74c3c;
  font-weight: 500;
}

/* Données */
.data-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
}

.page-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.page-btn:hover:not(:disabled) {
  background: #2980b9;
}

.data-table-container {
  overflow: auto;
  max-height: 400px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th,
.data-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  border-right: 1px solid #e9ecef;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-cell {
  font-family: 'Courier New', monospace;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.no-data i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .database-manager {
    padding: 1rem;
  }

  .manager-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-grid,
  .tables-grid,
  .maintenance-grid {
    grid-template-columns: 1fr;
  }

  .search-input {
    width: 100%;
  }

  .modal-content.large {
    width: 95%;
    margin: 1rem;
  }

  .data-pagination {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
