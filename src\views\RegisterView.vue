<template>
  <div class="register-container">
    <h1>C<PERSON>er un compte</h1>
    <form @submit.prevent="handleRegister">
      <div class="form-group">
        <label for="first_name">Nom</label>
        <input v-model="first_name" id="first_name" type="text" required />
      </div>
      <div class="form-group">
        <label for="last_name">Prénom</label>
        <input v-model="last_name" id="last_name" type="text" required />
      </div>
      <div class="form-group">
        <label for="email">Email</label>
        <input v-model="email" id="email" type="email" required />
      </div>
      <div class="form-group">
        <label for="password">Mot de passe</label>
        <input v-model="password" id="password" type="password" required minlength="6" />
      </div>
      <div class="form-group">
        <label for="passwordConfirm">Confirmer le mot de passe</label>
        <input v-model="passwordConfirm" id="passwordConfirm" type="password" required minlength="6" />
      </div>
      <button type="submit" class="btn">S'inscrire</button>
      <p v-if="error" class="error">{{ error }}</p>
      <p v-if="success" class="success">{{ success }}</p>
    </form>
    <router-link to="/login" class="login-link">Déjà un compte ? Connectez-vous</router-link>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const first_name = ref('')
const last_name = ref('')
const email = ref('')
const role = ref('patient')
const password = ref('')
const passwordConfirm = ref('')
const error = ref('')
const success = ref('')
const router = useRouter()

function validateEmail(email) {
  const re = /\S+@\S+\.\S+/
  return re.test(email)
}
async function handleRegister() {
  error.value = ''
  success.value = ''
  console.log("Soumission du formulaire") // Pour debug

  if (!validateEmail(email.value)) {
    error.value = 'Email invalide.'
    return
  }
  if (password.value !== passwordConfirm.value) {
    error.value = 'Les mots de passe ne correspondent pas.'
    return
  }

  try {
    const response = await fetch('http://localhost:8000/api/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        nom: last_name.value,
        prenom: first_name.value,
        email: email.value,
        role: role.value,
        password: password.value
      })
    })
    const result = await response.json()
    if (!response.ok) {
      error.value = result.message || "Erreur lors de la création du compte."
      return
    }
    success.value = result.message || 'Compte créé avec succès !'
    first_name.value = ''
    last_name.value = ''
    email.value = ''
    password.value = ''
    passwordConfirm.value = ''
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (e) {
    error.value = 'Une erreur est survenue lors de la création du compte.'
  }
}

</script>

<style scoped>
.register-container {
  max-width: 400px;
  margin: 3rem auto;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}
.form-group {
  margin-bottom: 1.25rem;
}
label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}
input, select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  font-size: 1rem;
}
.btn {
  width: 100%;
  padding: 0.75rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.btn:hover {
  background: #1e40af;
}
.error {
  margin-top: 1rem;
  color: #dc2626;
  font-weight: 600;
  text-align: center;
}
.success {
  margin-top: 1rem;
  color: #16a34a;
  font-weight: 600;
  text-align: center;
}
.login-link {
  display: block;
  margin-top: 2rem;
  text-align: center;
  color: #2563eb;
  text-decoration: none;
  font-weight: 500;
}
.login-link:hover {
  text-decoration: underline;
  color: #1e40af;
}
</style>
