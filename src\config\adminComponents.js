export const adminComponents = {
  dashboard: {
    name: 'Tableau de Bord',
    icon: 'chart-line',
    components: {
      stats: () => import('@/components/admin/dashboard/DashboardStats.vue'),
      advancedStats: () => import('@/components/admin/dashboard/DashboardAdvancedStats.vue')
    }
  },
  appointments: {
    name: 'Rendez-vous',
    icon: 'calendar-alt',
    components: {
      management: () => import('@/components/admin/appointments/AppointmentManagement.vue'),
      calendar: () => import('@/components/admin/appointments/AdminCalendar.vue'),
      editor: () => import('@/components/admin/appointments/AppointmentEditor.vue'),
    }
  },
  patients: {
    name: 'Patients',
    icon: 'user-injured',
    components: {
      management: () => import('@/components/admin/patients/PatientManagement.vue'),
     
    }
  },
  doctors: {
    name: 'Médecins',
    icon: 'user-md',
    components: {
      management: () => import('@/components/admin/doctors/DoctorManagement.vue'),
    }
  },
  users: {
    name: 'Utilisateurs',
    icon: 'users-cog',
    components: {
      table: () => import('@/components/admin/users/UserTable.vue'),
      form: () => import('@/components/admin/users/UserForm.vue'),
    }
  },
  system: {
    name: 'Système',
    icon: 'cogs',
    components: {
      settings: () => import('@/components/admin/system/SystemSettings.vue'),
      audit: () => import('@/components/admin/system/AuditLog.vue'),
      health: () => import('@/components/admin/system/SystemHealth.vue'),
    }
  }
}
