<template>
  <div v-if="show" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <div class="header-content">
          <div class="header-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="header-info">
            <div class="header-title">
              <h3>Gestion des Conflits de Planning</h3>
              <div class="header-badge" :class="getHeaderBadgeClass()">
                {{ getHeaderBadgeText() }}
              </div>
            </div>
            <div class="header-subtitle">
              <div class="conflict-summary">
                <span class="conflict-count">{{ conflicts.length }}</span>
                <span class="conflict-description">
                  {{ conflicts.length === 1 ? 'situation critique identifiée' : 'situations critiques identifiées' }}
                </span>
              </div>
              <div class="urgency-indicator">
                <i class="fas fa-clock"></i>
                <span>Intervention requise</span>
              </div>
            </div>
            <div class="header-metrics">
              <div class="metric-item">
                <div class="metric-value">{{ activeConflicts.length }}</div>
                <div class="metric-label">Actifs</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ getCriticalCount() }}</div>
                <div class="metric-label">Critiques</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ criticalityRate }}%</div>
                <div class="metric-label">Urgence</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ totalTimeWasted }}</div>
                <div class="metric-label">Min perdues</div>
              </div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: Math.min(resolutionProgress, 100) + '%' }"></div>
            </div>
          </div>
        </div>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <div v-if="conflicts.length === 0" class="no-conflicts">
          <div class="no-conflicts-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h4>Aucun conflit détecté</h4>
          <p>Votre planning est parfaitement organisé ! Tous les rendez-vous sont correctement espacés.</p>
        </div>

        <div v-else class="conflicts-container">
          <div class="main-content">
            <div class="conflicts-toolbar">
              <div class="toolbar-filters">
                <div class="filter-group">
                  <label class="filter-label">Sévérité</label>
                  <select class="filter-select" v-model="selectedFilter" @change="setFilter(selectedFilter)">
                    <option value="all">Tous</option>
                    <option value="critical">Critique</option>
                    <option value="high">Urgent</option>
                    <option value="medium">Important</option>
                  </select>
                </div>
                <div class="filter-group">
                  <label class="filter-label">Tri</label>
                  <select class="filter-select" v-model="selectedSort" @change="setSort(selectedSort)">
                    <option value="severity">Par sévérité</option>
                    <option value="time">Par heure</option>
                    <option value="patient">Par patient</option>
                  </select>
                </div>
              </div>
              <div class="toolbar-actions">
                <button class="toolbar-btn" :class="{ active: selectedView === 'list' }" @click="setView('list')">
                  <i class="fas fa-list"></i> Liste
                </button>
                <button class="toolbar-btn" :class="{ active: selectedView === 'cards' }" @click="setView('cards')">
                  <i class="fas fa-th-large"></i> Cartes
                </button>
                <button class="toolbar-btn" :class="{ active: selectedView === 'timeline' }" @click="setView('timeline')">
                  <i class="fas fa-calendar"></i> Timeline
                </button>
              </div>
            </div>

            <div class="conflicts-summary">
              <div class="summary-stats">
                <div class="stat-item critical" v-if="getCriticalCount() > 0">
                  <span class="stat-number">{{ getCriticalCount() }}</span>
                  <span class="stat-label">Critique{{ getCriticalCount() > 1 ? 's' : '' }}</span>
                </div>
                <div class="stat-item urgent" v-if="getUrgentCount() > 0">
                  <span class="stat-number">{{ getUrgentCount() }}</span>
                  <span class="stat-label">Urgent{{ getUrgentCount() > 1 ? 's' : '' }}</span>
                </div>
                <div class="stat-item important" v-if="getImportantCount() > 0">
                  <span class="stat-number">{{ getImportantCount() }}</span>
                  <span class="stat-label">Important{{ getImportantCount() > 1 ? 's' : '' }}</span>
                </div>
              </div>
            </div>

          <div class="conflicts-list">
            <div
              v-for="(conflict, index) in filteredConflicts"
              :key="`conflict-${conflict.id || 'idx-' + index}`"
              class="conflict-resolution-item"
              :class="[getSeverityClass(conflict.gapMinutes), { 'resolved': resolvedConflicts.includes(conflict.id || `conflict-${index}-${Date.now()}`) }]"
            >
            <div class="conflict-header">
              <div class="conflict-number">
                <span class="number">{{ index + 1 }}</span>
              </div>
              <div class="conflict-title">
                <h4>Conflit de planning {{ getSeverityLabel(conflict.gapMinutes) }}</h4>
                <div class="conflict-badges">
                  <div class="conflict-severity-badge" :class="getSeverityClass(conflict.gapMinutes)">
                    <i class="fas fa-exclamation-triangle"></i>
                    {{ getSeverityLabel(conflict.gapMinutes) }}
                  </div>
                  <div class="status-indicator">
                    <span>En attente</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="conflict-timeline">
              <div class="timeline-item first">
                <div class="timeline-dot"></div>
                <div class="appointment-card">
                  <div class="appointment-time">
                    {{ formatTime(conflict.apt1?.date_rendez_vous || conflict.apt1?.date || conflict.time1) }}
                  </div>
                  <div class="appointment-info">
                    <div class="patient-name">
                      <i class="fas fa-user"></i>
                      {{ conflict.apt1?.patient_nom || conflict.apt1?.patient || 'Patient non défini' }}
                    </div>
                    <div class="appointment-details">
                      <span class="appointment-type">
                        <i class="fas fa-stethoscope"></i>
                        {{ conflict.apt1?.type || 'Consultation' }}
                      </span>
                      <span class="appointment-duration">
                        <i class="fas fa-clock"></i>
                        {{ conflict.apt1?.duree || 30 }} min
                      </span>
                    </div>
                    <div class="appointment-id">RDV #{{ conflict.apt1?.id }}</div>
                  </div>
                </div>
              </div>

              <div class="timeline-gap">
                <div class="gap-indicator" :class="getSeverityClass(conflict.gapMinutes)">
                  <i class="fas fa-arrows-alt-v"></i>
                  <span class="gap-time">{{ conflict.gapMinutes || 0 }} min</span>
                </div>
              </div>

              <div class="timeline-item second">
                <div class="timeline-dot"></div>
                <div class="appointment-card">
                  <div class="appointment-time">
                    {{ formatTime(conflict.apt2?.date_rendez_vous || conflict.apt2?.date || conflict.time2) }}
                  </div>
                  <div class="appointment-info">
                    <div class="patient-name">
                      <i class="fas fa-user"></i>
                      {{ conflict.apt2?.patient_nom || conflict.apt2?.patient || 'Patient non défini' }}
                    </div>
                    <div class="appointment-details">
                      <span class="appointment-type">
                        <i class="fas fa-stethoscope"></i>
                        {{ conflict.apt2?.type || 'Consultation' }}
                      </span>
                      <span class="appointment-duration">
                        <i class="fas fa-clock"></i>
                        {{ conflict.apt2?.duree || 30 }} min
                      </span>
                    </div>
                    <div class="appointment-id">RDV #{{ conflict.apt2?.id }}</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="resolution-actions">
              <div class="actions-header">
                <h5>
                  <i class="fas fa-tools"></i>
                  Solutions recommandées
                </h5>
                <div class="priority-indicator" :class="getSeverityClass(conflict.gapMinutes)">
                  Priorité {{ getSeverityLabel(conflict.gapMinutes) }}
                </div>
              </div>

              <div class="action-grid">
                <div class="action-card primary"
                     @click="rescheduleAppointment(conflict.apt2 || conflict.secondAppointment, conflict.id || `conflict-${index}-${Date.now()}`)"
                     :class="getActionButtonClass('reschedule', conflict.apt2?.id || conflict.secondAppointment?.id || `apt-${index}`)">
                  <div class="action-icon">
                    <i class="fas fa-calendar-alt" :class="{ 'fa-spin': isActionInProgress('reschedule', conflict.apt2?.id || conflict.secondAppointment?.id || `apt-${index}`) }"></i>
                  </div>
                  <div class="action-content">
                    <div class="action-title">Reprogrammer</div>
                    <div class="action-description">Déplacer le 2ème rendez-vous vers un autre créneau disponible</div>
                    <div class="action-benefit">
                      <i class="fas fa-check"></i>
                      Solution recommandée
                    </div>
                  </div>
                </div>

                <div class="action-card secondary"
                     @click="extendAppointment(conflict.apt1 || conflict.firstAppointment, conflict.id || `conflict-${index}-${Date.now()}`)"
                     :class="getActionButtonClass('extend', conflict.apt1?.id || conflict.firstAppointment?.id || `apt1-${index}`)">
                  <div class="action-icon">
                    <i class="fas fa-clock" :class="{ 'fa-spin': isActionInProgress('extend', conflict.apt1?.id || conflict.firstAppointment?.id || `apt1-${index}`) }"></i>
                  </div>
                  <div class="action-content">
                    <div class="action-title">Ajuster la durée</div>
                    <div class="action-description">Modifier la durée du 1er rendez-vous pour résoudre le conflit</div>
                    <div class="action-benefit">
                      <i class="fas fa-info"></i>
                      Solution rapide
                    </div>
                  </div>
                </div>

                <div class="action-card tertiary"
                     @click="contactPatient(conflict.apt2 || conflict.secondAppointment, conflict.id || `conflict-${index}-${Date.now()}`)"
                     :class="getActionButtonClass('contact', conflict.apt2?.id || conflict.secondAppointment?.id || `apt2-${index}`)">
                  <div class="action-icon">
                    <i class="fas fa-phone" :class="{ 'fa-spin': isActionInProgress('contact', conflict.apt2?.id || conflict.secondAppointment?.id || `apt2-${index}`) }"></i>
                  </div>
                  <div class="action-content">
                    <div class="action-title">Contacter</div>
                    <div class="action-description">Informer le patient du conflit et proposer des solutions</div>
                    <div class="action-benefit">
                      <i class="fas fa-users"></i>
                      Communication
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>

          <div class="sidebar">
            <div class="sidebar-title">Tableau de bord</div>

            <div class="quick-stats">
              <div class="quick-stat">
                <span class="quick-stat-label">Temps total perdu</span>
                <span class="quick-stat-value">{{ totalTimeWasted }} min</span>
              </div>
              <div class="quick-stat">
                <span class="quick-stat-label">Patients affectés</span>
                <span class="quick-stat-value">{{ affectedPatients }}</span>
              </div>
              <div class="quick-stat">
                <span class="quick-stat-label">Taux de criticité</span>
                <span class="quick-stat-value">{{ criticalityRate }}%</span>
              </div>
              <div class="quick-stat">
                <span class="quick-stat-label">Résolution estimée</span>
                <span class="quick-stat-value">{{ estimatedResolutionTime }} min</span>
              </div>
              <div class="quick-stat" v-if="resolvedConflicts.length > 0">
                <span class="quick-stat-label">Conflits résolus</span>
                <span class="quick-stat-value">{{ resolvedConflicts.length }}/{{ conflicts.length }}</span>
              </div>
              <div class="quick-stat" v-if="activeConflicts.length !== conflicts.length">
                <span class="quick-stat-label">Expirés auto</span>
                <span class="quick-stat-value">{{ conflicts.length - activeConflicts.length }}</span>
              </div>
            </div>

            <div class="time-status" v-if="filteredConflicts.length > 0">
              <div class="time-status-title">
                <i class="fas fa-clock"></i>
                Suivi temporel actif
              </div>
              <div class="time-status-info">
                Les conflits disparaîtront automatiquement une fois les rendez-vous terminés
              </div>
              <div class="current-time">
                {{ currentTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) }}
              </div>
            </div>

            <div class="resolution-progress" v-if="resolutionProgress > 0">
              <div class="progress-label">Progrès de résolution</div>
              <div class="progress-bar-sidebar">
                <div class="progress-fill-sidebar" :style="{ width: resolutionProgress + '%' }"></div>
              </div>
              <div class="progress-text">{{ Math.round(resolutionProgress) }}%</div>
            </div>

            <div class="quick-actions">
              <button class="quick-action-btn" @click="resolveAllConflicts" :disabled="autoResolveInProgress">
                <i class="fas fa-magic" :class="{ 'fa-spin': autoResolveInProgress }"></i>
                {{ autoResolveInProgress ? 'Résolution...' : 'Résolution auto' }}
              </button>
              <button class="quick-action-btn" @click="openSlotSuggestionModal">
                <i class="fas fa-clock"></i> Créneaux suggérés
              </button>
              <button class="quick-action-btn" @click="exportReport">
                <i class="fas fa-download"></i> Exporter rapport
              </button>
              <button class="quick-action-btn" @click="sendSMSAlerts">
                <i class="fas fa-bell"></i> Alertes SMS
              </button>
              <button class="quick-action-btn" @click="createNewAppointment">
                <i class="fas fa-calendar-plus"></i> Nouveau RDV
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
      
      <div class="modal-footer">
        <div class="footer-info">
          <div class="conflict-impact">
            <i class="fas fa-exclamation-circle"></i>
            <span>{{ conflicts.length }} conflit{{ conflicts.length > 1 ? 's' : '' }} affecte{{ conflicts.length > 1 ? 'nt' : '' }} votre planning</span>
          </div>
        </div>

        <div class="footer-actions">
          <button class="btn-secondary" @click="closeModal">
            <i class="fas fa-times"></i>
            Fermer
          </button>
          <button class="btn-auto" @click="resolveAllConflicts" v-if="conflicts.length > 0">
            <i class="fas fa-magic"></i>
            Résolution automatique
            <span class="btn-subtitle">{{ conflicts.length }} conflit{{ conflicts.length > 1 ? 's' : '' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Système de notifications -->
    <div class="notifications-container" v-if="notifications.length > 0">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification"
        :class="notification.type"
      >
        <div class="notification-icon">
          <i class="fas fa-check-circle" v-if="notification.type === 'success'"></i>
          <i class="fas fa-exclamation-triangle" v-if="notification.type === 'warning'"></i>
          <i class="fas fa-times-circle" v-if="notification.type === 'error'"></i>
          <i class="fas fa-info-circle" v-if="notification.type === 'info'"></i>
        </div>
        <div class="notification-content">
          <span>{{ notification.message }}</span>
        </div>
        <button class="notification-close" @click="removeNotification(notification.id)">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Modal de suggestion de créneaux -->
    <div v-if="showSlotSuggestionModal" class="modal-overlay" @click="closeSlotSuggestionModal">
      <div class="slot-suggestion-modal" @click.stop>
        <div class="slot-modal-header">
          <h3>
            <i class="fas fa-clock"></i>
            Suggérer un nouveau créneau
          </h3>
          <button class="close-btn" @click="closeSlotSuggestionModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="slot-modal-body">
          <form @submit.prevent="saveSlotSuggestion">
            <div class="form-row">
              <div class="form-group">
                <label for="slot-date">Date *</label>
                <input
                  type="date"
                  id="slot-date"
                  v-model="newSlotForm.date"
                  class="form-input"
                  required
                >
              </div>

              <div class="form-group">
                <label for="slot-type">Type de consultation</label>
                <select
                  id="slot-type"
                  v-model="newSlotForm.type_consultation"
                  class="form-input"
                >
                  <option value="consultation">Consultation</option>
                  <option value="urgence">Urgence</option>
                  <option value="suivi">Suivi</option>
                  <option value="controle">Contrôle</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="slot-start">Heure de début *</label>
                <input
                  type="time"
                  id="slot-start"
                  v-model="newSlotForm.heure_debut"
                  class="form-input"
                  required
                >
              </div>

              <div class="form-group">
                <label for="slot-end">Heure de fin *</label>
                <input
                  type="time"
                  id="slot-end"
                  v-model="newSlotForm.heure_fin"
                  class="form-input"
                  required
                >
              </div>
            </div>

            <div class="form-group">
              <label for="slot-notes">Notes (optionnel)</label>
              <textarea
                id="slot-notes"
                v-model="newSlotForm.notes"
                class="form-textarea"
                placeholder="Informations complémentaires sur ce créneau..."
                rows="3"
              ></textarea>
            </div>

            <div class="slot-modal-actions">
              <button type="button" class="btn-secondary" @click="closeSlotSuggestionModal">
                Annuler
              </button>
              <button type="submit" class="btn-primary">
                <i class="fas fa-save"></i>
                Enregistrer le créneau
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  conflicts: {
    type: Array,
    default: () => []
  },
  doctorId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['close', 'reschedule', 'extend', 'contact', 'resolve-all', 'conflict-resolved', 'appointment-updated', 'save-suggested-slot', 'create-appointment'])

// Variables réactives pour la gestion d'état
const isResolving = ref(false)
const selectedFilter = ref('all')
const selectedSort = ref('severity')
const selectedView = ref('cards')
const resolutionProgress = ref(0)
const resolvedConflicts = ref([])
const notifications = ref([])

// Variables pour les actions en cours
const processingActions = ref(new Set())
const lastAction = ref(null)
const autoResolveInProgress = ref(false)

// Variables pour la gestion temporelle
const currentTime = ref(new Date())
const timeCheckInterval = ref(null)
const expiredConflicts = ref([])

// Variables pour les créneaux suggérés
const showSlotSuggestionModal = ref(false)
const suggestedSlots = ref([])
const newSlotForm = ref({
  date: '',
  heure_debut: '',
  heure_fin: '',
  type_consultation: 'consultation',
  notes: ''
})

// Computed properties pour les filtres et statistiques
const activeConflicts = computed(() => {
  const now = currentTime.value

  return props.conflicts.filter(conflict => {
    // Vérifier si le conflit est encore d'actualité
    const firstAppointment = conflict.apt1 || conflict.firstAppointment
    const secondAppointment = conflict.apt2 || conflict.secondAppointment

    if (!firstAppointment || !secondAppointment) return false

    // Obtenir les dates/heures des rendez-vous
    const firstDateTime = new Date(firstAppointment.date_rendez_vous || firstAppointment.date)
    const secondDateTime = new Date(secondAppointment.date_rendez_vous || secondAppointment.date)

    // Si les deux rendez-vous sont passés, le conflit n'est plus d'actualité
    const latestAppointmentTime = new Date(Math.max(firstDateTime.getTime(), secondDateTime.getTime()))

    // Ajouter la durée du rendez-vous le plus long pour être sûr
    const firstDuration = firstAppointment.duree || 30
    const secondDuration = secondAppointment.duree || 30
    const maxDuration = Math.max(firstDuration, secondDuration)

    latestAppointmentTime.setMinutes(latestAppointmentTime.getMinutes() + maxDuration)

    return latestAppointmentTime > now
  })
})

const filteredConflicts = computed(() => {
  let filtered = activeConflicts.value.filter(conflict =>
    !resolvedConflicts.value.includes(getConflictId(conflict, 0)) &&
    !expiredConflicts.value.includes(getConflictId(conflict, 0))
  )

  // Filtrage par sévérité
  if (selectedFilter.value !== 'all') {
    filtered = filtered.filter(conflict => {
      const severity = getSeverityClass(conflict.gapMinutes)
      return severity === selectedFilter.value
    })
  }

  // Tri
  filtered.sort((a, b) => {
    switch (selectedSort.value) {
      case 'severity':
        return a.gapMinutes - b.gapMinutes
      case 'time':
        return new Date(a.firstAppointment?.date || a.firstAppointment?.date_rendez_vous) -
               new Date(b.firstAppointment?.date || b.firstAppointment?.date_rendez_vous)
      case 'patient':
        const nameA = a.firstAppointment?.patient_nom || a.firstAppointment?.patient || ''
        const nameB = b.firstAppointment?.patient_nom || b.firstAppointment?.patient || ''
        return nameA.localeCompare(nameB)
      default:
        return 0
    }
  })

  return filtered
})

const totalTimeWasted = computed(() => {
  return filteredConflicts.value.reduce((total, conflict) => {
    return total + Math.abs(conflict.gapMinutes)
  }, 0)
})

const affectedPatients = computed(() => {
  const patients = new Set()
  filteredConflicts.value.forEach(conflict => {
    if (conflict.firstAppointment?.patient_nom) patients.add(conflict.firstAppointment.patient_nom)
    if (conflict.secondAppointment?.patient_nom) patients.add(conflict.secondAppointment.patient_nom)
  })
  return patients.size
})

const criticalityRate = computed(() => {
  if (filteredConflicts.value.length === 0) return 0
  return Math.round((getCriticalCount() / filteredConflicts.value.length) * 100)
})

const estimatedResolutionTime = computed(() => {
  return filteredConflicts.value.length * 5 // 5 minutes par conflit
})

const closeModal = () => {
  // Sauvegarder l'état avant fermeture si nécessaire
  if (resolvedConflicts.value.length > 0) {
    console.log('Conflits résolus avant fermeture:', resolvedConflicts.value)
    addNotification('success', `✅ ${resolvedConflicts.value.length} conflit(s) résolu(s) avec succès`)
  }

  // Log pour debug
  console.log('Fermeture du modal de résolution de conflits')
  console.log('État final:', {
    totalConflicts: props.conflicts.length,
    resolvedConflicts: resolvedConflicts.value.length,
    remainingConflicts: props.conflicts.length - resolvedConflicts.value.length
  })

  emit('close')
}

const formatTime = (dateString) => {
  if (!dateString) {
    console.warn('formatTime: dateString est vide')
    return 'Heure non définie'
  }

  const date = new Date(dateString)

  if (isNaN(date.getTime())) {
    console.warn('formatTime: Date invalide pour:', dateString)
    return 'Date invalide'
  }

  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const formatDateTime = (dateString) => {
  if (!dateString) return 'Date non définie'

  const date = new Date(dateString)
  if (isNaN(date.getTime())) return 'Date invalide'

  return date.toLocaleDateString('fr-FR') + ' à ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const rescheduleAppointment = async (appointment, conflictId = null) => {
  const appointmentId = appointment?.id || Date.now()
  const actionId = `reschedule-${appointmentId}`

  try {
    // Marquer l'action comme en cours
    processingActions.value.add(actionId)

    console.log('Reprogrammation du rendez-vous:', appointment, 'Conflit ID:', conflictId)

    // Proposer des créneaux alternatifs
    const currentDate = new Date(appointment.date_rendez_vous || appointment.date)
    const alternatives = generateAlternativeSlots(currentDate)

    const patientName = appointment.patient_nom || appointment.patient || 'ce patient'
    const message = `Reprogrammer le rendez-vous de ${patientName} ?\n\nCréneaux disponibles:\n${alternatives.join('\n')}`

    if (confirm(message)) {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Émettre l'événement de reprogrammation
      emit('reschedule', {
        appointment,
        alternatives,
        action: 'reschedule',
        conflictId
      })

      // Si c'est lié à un conflit, le marquer comme résolu
      if (conflictId !== null && conflictId !== undefined) {
        const finalConflictId = conflictId || `conflict-reschedule-${appointmentId}`
        resolveConflict(finalConflictId, 'reschedule', `Rendez-vous reprogrammé pour ${patientName}`)
      }

      // Notification de succès
      addNotification('success', `✅ Reprogrammation confirmée pour ${patientName}`)
    }

  } catch (error) {
    addNotification('error', 'Erreur lors de la reprogrammation')
    console.error('Erreur reprogrammation:', error)
  } finally {
    processingActions.value.delete(actionId)
  }
}

const extendAppointment = async (appointment, conflictId = null) => {
  const appointmentId = appointment?.id || Date.now()
  const actionId = `extend-${appointmentId}`

  try {
    // Marquer l'action comme en cours
    processingActions.value.add(actionId)

    console.log('Extension du rendez-vous:', appointment, 'Conflit ID:', conflictId)

    const currentDuration = appointment.duree || 30
    const newDuration = prompt(`Durée actuelle: ${currentDuration} minutes\nNouvelle durée (en minutes):`, currentDuration + 15)

    if (newDuration && !isNaN(newDuration) && newDuration > 0) {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1000))

      const patientName = appointment.patient_nom || appointment.patient || 'ce patient'

      emit('extend', {
        appointment,
        oldDuration: currentDuration,
        newDuration: parseInt(newDuration),
        action: 'extend',
        conflictId
      })

      // Si c'est lié à un conflit, le marquer comme résolu
      if (conflictId !== null && conflictId !== undefined) {
        const finalConflictId = conflictId || `conflict-extend-${appointmentId}`
        resolveConflict(finalConflictId, 'extend', `Durée étendue de ${currentDuration} à ${newDuration} minutes pour ${patientName}`)
      }

      // Notification de succès
      addNotification('success', `✅ Durée étendue à ${newDuration} minutes pour ${patientName}`)
    }

  } catch (error) {
    addNotification('error', 'Erreur lors de l\'extension')
    console.error('Erreur extension:', error)
  } finally {
    processingActions.value.delete(actionId)
  }
}

const contactPatient = async (appointment, conflictId = null) => {
  const appointmentId = appointment?.id || Date.now()
  const actionId = `contact-${appointmentId}`

  try {
    // Marquer l'action comme en cours
    processingActions.value.add(actionId)

    console.log('Contact du patient:', appointment, 'Conflit ID:', conflictId)

    const patientName = appointment.patient_nom || appointment.patient || 'Patient'
    const appointmentTime = formatDateTime(appointment.date_rendez_vous || appointment.date)

    const contactOptions = [
      'Appeler le patient',
      'Envoyer un SMS',
      'Envoyer un email',
      'Annuler'
    ]

    const choice = prompt(`Contacter ${patientName} pour le rendez-vous du ${appointmentTime}\n\nOptions:\n1. ${contactOptions[0]}\n2. ${contactOptions[1]}\n3. ${contactOptions[2]}\n\nChoisissez (1-3):`)

    if (choice && choice >= 1 && choice <= 3) {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 800))

      emit('contact', {
        appointment,
        method: contactOptions[choice - 1],
        action: 'contact',
        conflictId
      })

      // Notification de succès
      addNotification('info', `📞 ${contactOptions[choice - 1]} pour ${patientName}`)

      // Marquer comme action prise (pas forcément résolu, mais action effectuée)
      const finalConflictId = conflictId || `conflict-contact-${appointmentId}`
      lastAction.value = {
        type: 'contact',
        conflictId: finalConflictId,
        details: `Contact ${patientName} via ${contactOptions[choice - 1]}`
      }

      // Optionnel: marquer comme partiellement résolu si c'est un contact
      if (conflictId !== null && conflictId !== undefined) {
        addNotification('warning', `Action de contact effectuée pour le conflit. Résolution manuelle requise.`)
      }
    }

  } catch (error) {
    addNotification('error', 'Erreur lors du contact')
    console.error('Erreur contact:', error)
  } finally {
    processingActions.value.delete(actionId)
  }
}

// Générer des créneaux alternatifs
const generateAlternativeSlots = (currentDate) => {
  const alternatives = []
  const baseDate = new Date(currentDate)

  // Proposer des créneaux le même jour
  for (let hour = 8; hour <= 17; hour++) {
    const slot = new Date(baseDate)
    slot.setHours(hour, 0, 0, 0)

    if (slot.getTime() !== baseDate.getTime()) {
      alternatives.push(`${slot.toLocaleDateString('fr-FR')} à ${slot.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`)
    }
  }

  // Proposer des créneaux le lendemain
  const nextDay = new Date(baseDate)
  nextDay.setDate(nextDay.getDate() + 1)

  for (let hour = 8; hour <= 12; hour++) {
    const slot = new Date(nextDay)
    slot.setHours(hour, 0, 0, 0)
    alternatives.push(`${slot.toLocaleDateString('fr-FR')} à ${slot.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`)
  }

  return alternatives.slice(0, 5) // Limiter à 5 suggestions
}

// Fonctions de gestion des conflits
const resolveConflict = (conflictId, method, details) => {
  // Générer un ID unique si conflictId est undefined
  const finalConflictId = conflictId || `conflict-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  if (!resolvedConflicts.value.includes(finalConflictId)) {
    resolvedConflicts.value.push(finalConflictId)

    // Émettre l'événement de résolution
    emit('conflict-resolved', {
      conflictId: finalConflictId,
      method,
      details,
      timestamp: new Date().toISOString()
    })

    // Mettre à jour le progrès
    updateResolutionProgress()

    console.log(`Conflit résolu: ${finalConflictId} via ${method}`)
  }
}

const addNotification = (type, message) => {
  const notification = {
    id: Date.now(),
    type, // 'success', 'error', 'warning', 'info'
    message,
    timestamp: new Date().toISOString()
  }

  notifications.value.push(notification)

  // Auto-supprimer après 5 secondes
  setTimeout(() => {
    removeNotification(notification.id)
  }, 5000)
}

const removeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const updateResolutionProgress = () => {
  if (props.conflicts.length > 0) {
    resolutionProgress.value = (resolvedConflicts.value.length / props.conflicts.length) * 100
  }
}

const resolveAllConflicts = async () => {
  if (autoResolveInProgress.value) return

  try {
    autoResolveInProgress.value = true
    addNotification('info', 'Résolution automatique en cours...')

    const conflictsToResolve = filteredConflicts.value
    console.log('Conflits à résoudre:', conflictsToResolve)

    // Simuler la résolution progressive
    for (let i = 0; i < conflictsToResolve.length; i++) {
      const conflict = conflictsToResolve[i]

      // Générer un ID unique pour chaque conflit
      const conflictId = conflict.id || `conflict-${i}-${Date.now()}`

      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Choisir la meilleure stratégie selon la sévérité
      let method = 'auto-reschedule'
      let details = `Résolution automatique du conflit ${i + 1}/${conflictsToResolve.length}`

      if (conflict.gapMinutes <= 0) {
        method = 'emergency-reschedule'
        details = `Reprogrammation d'urgence - conflit critique ${i + 1}`
      } else if (conflict.gapMinutes <= 10) {
        method = 'extend-duration'
        details = `Extension de durée - conflit urgent ${i + 1}`
      } else {
        method = 'auto-reschedule'
        details = `Reprogrammation automatique - conflit ${i + 1}`
      }

      // Ajouter des informations sur les patients concernés
      const patient1 = conflict.apt1?.patient_nom || conflict.firstAppointment?.patient_nom || 'Patient 1'
      const patient2 = conflict.apt2?.patient_nom || conflict.secondAppointment?.patient_nom || 'Patient 2'
      details += ` (${patient1} et ${patient2})`

      resolveConflict(conflictId, method, details)

      // Notification de progression
      addNotification('info', `Conflit ${i + 1}/${conflictsToResolve.length} résolu`)

      // Mettre à jour le progrès visuellement
      updateResolutionProgress()
    }

    // Émettre l'événement global
    emit('resolve-all', {
      conflicts: props.conflicts,
      resolvedCount: resolvedConflicts.value.length,
      methods: ['auto-reschedule', 'extend-duration', 'emergency-reschedule'],
      details: `${resolvedConflicts.value.length} conflits résolus automatiquement`
    })

    addNotification('success', `✅ ${resolvedConflicts.value.length} conflits résolus automatiquement`)

    // Fermer le modal après un délai
    setTimeout(() => {
      closeModal()
    }, 3000)

  } catch (error) {
    addNotification('error', 'Erreur lors de la résolution automatique')
    console.error('Erreur résolution auto:', error)
  } finally {
    autoResolveInProgress.value = false
  }
}

// Fonctions utilitaires pour la sévérité des conflits
const getSeverityClass = (gapMinutes) => {
  if (gapMinutes <= 0) return 'critical'
  if (gapMinutes <= 10) return 'high'
  if (gapMinutes <= 20) return 'medium'
  return 'low'
}

const getSeverityLabel = (gapMinutes) => {
  if (gapMinutes <= 0) return 'CRITIQUE'
  if (gapMinutes <= 10) return 'URGENT'
  if (gapMinutes <= 20) return 'IMPORTANT'
  return 'MINEUR'
}

// Fonctions pour les statistiques
const getCriticalCount = () => {
  return props.conflicts.filter(c => c.gapMinutes <= 0).length
}

const getUrgentCount = () => {
  return props.conflicts.filter(c => c.gapMinutes > 0 && c.gapMinutes <= 10).length
}

const getImportantCount = () => {
  return props.conflicts.filter(c => c.gapMinutes > 10 && c.gapMinutes <= 20).length
}

// Fonctions de filtrage et tri
const setFilter = (filter) => {
  selectedFilter.value = filter
  addNotification('info', `Filtre appliqué: ${filter}`)
}

const setSort = (sort) => {
  selectedSort.value = sort
  addNotification('info', `Tri appliqué: ${sort}`)
}

const setView = (view) => {
  selectedView.value = view
  addNotification('info', `Vue changée: ${view}`)
}

// Fonctions d'export et d'actions rapides
const exportReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    totalConflicts: props.conflicts.length,
    resolvedConflicts: resolvedConflicts.value.length,
    criticalCount: getCriticalCount(),
    urgentCount: getUrgentCount(),
    importantCount: getImportantCount(),
    totalTimeWasted: totalTimeWasted.value,
    affectedPatients: affectedPatients.value,
    conflicts: filteredConflicts.value.map(conflict => ({
      id: conflict.id,
      severity: getSeverityClass(conflict.gapMinutes),
      gapMinutes: conflict.gapMinutes,
      firstAppointment: conflict.firstAppointment || conflict.apt1,
      secondAppointment: conflict.secondAppointment || conflict.apt2
    }))
  }

  // Simuler l'export
  console.log('Rapport exporté:', report)
  addNotification('success', 'Rapport exporté avec succès')

  // Dans un vrai projet, vous pourriez télécharger un fichier CSV/PDF
  const dataStr = JSON.stringify(report, null, 2)
  const dataBlob = new Blob([dataStr], {type: 'application/json'})
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `conflict-report-${new Date().toISOString().split('T')[0]}.json`
  link.click()
}

const sendSMSAlerts = async () => {
  try {
    addNotification('info', 'Envoi des alertes SMS...')

    // Simuler l'envoi
    await new Promise(resolve => setTimeout(resolve, 2000))

    const patientsToAlert = affectedPatients.value
    addNotification('success', `Alertes SMS envoyées à ${patientsToAlert} patients`)

  } catch (error) {
    addNotification('error', 'Erreur lors de l\'envoi des SMS')
  }
}

const createNewAppointment = () => {
  addNotification('info', 'Redirection vers la création de rendez-vous...')
  // Ici vous pourriez émettre un événement ou naviguer vers une autre page
  emit('create-appointment')
}

// Fonctions de gestion temporelle
const startTimeTracking = () => {
  // Mettre à jour l'heure actuelle toutes les minutes
  timeCheckInterval.value = setInterval(() => {
    currentTime.value = new Date()
    checkExpiredConflicts()
  }, 60000) // Vérifier toutes les minutes

  // Vérification initiale
  checkExpiredConflicts()
}

const stopTimeTracking = () => {
  if (timeCheckInterval.value) {
    clearInterval(timeCheckInterval.value)
    timeCheckInterval.value = null
  }
}

const checkExpiredConflicts = () => {
  const now = currentTime.value
  const previousActiveCount = activeConflicts.value.length

  // Identifier les conflits qui viennent d'expirer
  props.conflicts.forEach((conflict, index) => {
    const conflictId = getConflictId(conflict, index)

    if (!expiredConflicts.value.includes(conflictId)) {
      const firstAppointment = conflict.apt1 || conflict.firstAppointment
      const secondAppointment = conflict.apt2 || conflict.secondAppointment

      if (firstAppointment && secondAppointment) {
        const firstDateTime = new Date(firstAppointment.date_rendez_vous || firstAppointment.date)
        const secondDateTime = new Date(secondAppointment.date_rendez_vous || secondAppointment.date)
        const latestAppointmentTime = new Date(Math.max(firstDateTime.getTime(), secondDateTime.getTime()))

        // Ajouter la durée du rendez-vous
        const maxDuration = Math.max(firstAppointment.duree || 30, secondAppointment.duree || 30)
        latestAppointmentTime.setMinutes(latestAppointmentTime.getMinutes() + maxDuration)

        if (latestAppointmentTime <= now) {
          expiredConflicts.value.push(conflictId)
          addNotification('info', `⏰ Conflit automatiquement résolu (rendez-vous terminé)`)
        }
      }
    }
  })

  // Si des conflits ont expiré, mettre à jour les statistiques
  const newActiveCount = activeConflicts.value.length
  if (newActiveCount < previousActiveCount) {
    updateResolutionProgress()
  }
}

// Fonctions pour le header professionnel
const getHeaderBadgeClass = () => {
  const criticalCount = getCriticalCount()
  const urgentCount = getUrgentCount()

  if (criticalCount > 0) return 'critical'
  if (urgentCount > 0) return 'urgent'
  if (getImportantCount() > 0) return 'important'
  return 'minor'
}

const getHeaderBadgeText = () => {
  const criticalCount = getCriticalCount()
  const urgentCount = getUrgentCount()
  const importantCount = getImportantCount()

  if (criticalCount > 0) return 'CRITIQUE'
  if (urgentCount > 0) return 'URGENT'
  if (importantCount > 0) return 'IMPORTANT'
  return 'MINEUR'
}

const getConflictSeverityText = () => {
  const criticalCount = getCriticalCount()
  const urgentCount = getUrgentCount()

  if (criticalCount > 0) {
    return `${criticalCount} conflit${criticalCount > 1 ? 's' : ''} critique${criticalCount > 1 ? 's' : ''}`
  }
  if (urgentCount > 0) {
    return `${urgentCount} conflit${urgentCount > 1 ? 's' : ''} urgent${urgentCount > 1 ? 's' : ''}`
  }
  return `${props.conflicts.length} conflit${props.conflicts.length > 1 ? 's' : ''} détecté${props.conflicts.length > 1 ? 's' : ''}`
}

// Fonctions utilitaires pour les actions
const generateUniqueId = (prefix = 'id') => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

const getConflictId = (conflict, index) => {
  return conflict.id || conflict.conflictId || generateUniqueId(`conflict-${index}`)
}

const isActionInProgress = (actionType, itemId) => {
  return processingActions.value.has(`${actionType}-${itemId || 'unknown'}`)
}

const getActionButtonClass = (actionType, itemId) => {
  return isActionInProgress(actionType, itemId) ? 'processing' : ''
}

// Fonctions pour les créneaux suggérés
const openSlotSuggestionModal = () => {
  // Initialiser le formulaire avec la date d'aujourd'hui
  const today = new Date()
  newSlotForm.value.date = today.toISOString().split('T')[0]

  showSlotSuggestionModal.value = true
  addNotification('info', 'Ouverture du formulaire de suggestion de créneaux')
}

const closeSlotSuggestionModal = () => {
  showSlotSuggestionModal.value = false
  resetSlotForm()
}

const resetSlotForm = () => {
  newSlotForm.value = {
    date: '',
    heure_debut: '',
    heure_fin: '',
    type_consultation: 'consultation',
    notes: ''
  }
}

const validateSlotForm = () => {
  const form = newSlotForm.value

  if (!form.date || !form.heure_debut || !form.heure_fin) {
    addNotification('error', 'Veuillez remplir tous les champs obligatoires')
    return false
  }

  // Vérifier que l'heure de fin est après l'heure de début
  const debut = new Date(`${form.date}T${form.heure_debut}`)
  const fin = new Date(`${form.date}T${form.heure_fin}`)

  if (fin <= debut) {
    addNotification('error', 'L\'heure de fin doit être après l\'heure de début')
    return false
  }

  // Vérifier que la date n'est pas dans le passé
  const slotDate = new Date(form.date)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  if (slotDate < today) {
    addNotification('error', 'Impossible de créer un créneau dans le passé')
    return false
  }

  return true
}

const saveSlotSuggestion = async () => {
  if (!validateSlotForm()) return

  try {
    const form = newSlotForm.value

    // Simuler l'enregistrement en base de données
    addNotification('info', 'Enregistrement du créneau suggéré...')

    await new Promise(resolve => setTimeout(resolve, 1500))

    const newSlot = {
      id: generateUniqueId('slot'),
      medecin_id: props.doctorId || 'current-doctor', // ID du médecin connecté
      date: form.date,
      heure_debut: form.heure_debut,
      heure_fin: form.heure_fin,
      type_consultation: form.type_consultation,
      notes: form.notes,
      statut: 'suggere',
      created_at: new Date().toISOString()
    }

    // Ajouter à la liste locale
    suggestedSlots.value.push(newSlot)

    // Émettre l'événement pour sauvegarder en base
    emit('save-suggested-slot', newSlot)

    addNotification('success', `✅ Créneau suggéré enregistré: ${form.date} de ${form.heure_debut} à ${form.heure_fin}`)

    closeSlotSuggestionModal()

  } catch (error) {
    addNotification('error', 'Erreur lors de l\'enregistrement du créneau')
    console.error('Erreur sauvegarde créneau:', error)
  }
}

const loadSuggestedSlots = async () => {
  try {
    addNotification('info', 'Chargement des créneaux suggérés...')

    // Appel API réel pour charger les créneaux suggérés
    // TODO: Implémenter l'API des créneaux suggérés
    // const response = await api.getSuggestedSlots(props.doctorId)
    // suggestedSlots.value = response.data || []

    // Pour l'instant, initialiser avec un tableau vide
    suggestedSlots.value = []

    addNotification('success', `${suggestedSlots.value.length} créneaux suggérés chargés`)

  } catch (error) {
    addNotification('error', 'Erreur lors du chargement des créneaux')
    console.error('Erreur chargement créneaux:', error)
    suggestedSlots.value = []
  }
}

// Hooks de cycle de vie
onMounted(() => {
  // Démarrer le suivi temporel
  startTimeTracking()

  // Charger les créneaux suggérés
  loadSuggestedSlots()

  // Initialiser les statistiques
  updateResolutionProgress()

  // Ajouter une notification de bienvenue si il y a des conflits
  if (props.conflicts.length > 0) {
    addNotification('warning', `${props.conflicts.length} conflit(s) détecté(s) nécessitant votre attention`)
  }

  // Analyser automatiquement les conflits critiques
  const criticalConflicts = props.conflicts.filter(c => c.gapMinutes <= 0)
  if (criticalConflicts.length > 0) {
    addNotification('error', `${criticalConflicts.length} conflit(s) critique(s) nécessitent une action immédiate`)
  }

  // Notification sur la gestion automatique
  addNotification('info', '⏰ Suivi automatique des conflits activé - Les conflits expirés disparaîtront automatiquement')
})

// Nettoyage à la destruction du composant
onUnmounted(() => {
  stopTimeTracking()
})
</script>

<style scoped>
/* Variables CSS */
:root {
  --critical-color: #ff4757;
  --urgent-color: #ff6b6b;
  --important-color: #ffa502;
  --minor-color: #2ed573;
  --primary-color: #3498db;
  --secondary-color: #95a5a6;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --border-color: #bdc3c7;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.3);
  --border-radius: 12px;
  --border-radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-content {
  background: white;
  border-radius: var(--border-radius-large);
  width: 99%;
  max-width: 1800px;
  max-height: 99vh;
  min-height: 90vh;
  overflow: hidden;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  position: relative;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(52, 152, 219, 0.02) 0%,
    rgba(39, 174, 96, 0.02) 50%,
    rgba(243, 156, 18, 0.02) 100%
  );
  pointer-events: none;
  z-index: 1;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
  position: relative;
  overflow: hidden;
  z-index: 2;
  min-height: 80px;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>'),
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0.4;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.3) 100%);
  animation: headerShimmer 3s ease-in-out infinite;
}

@keyframes headerShimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.header-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 2;
}

.header-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  z-index: 2;
}

.header-metrics {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 70px;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 800;
  margin-bottom: 0.2rem;
}

.metric-label {
  font-size: 0.65rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
  border-radius: 2px;
  transition: width 1s ease-out;
  animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
  100% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.8); }
}

.header-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.header-title h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: -0.3px;
  font-family: 'Segoe UI', system-ui, sans-serif;
}

.header-badge {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
  }
}

.header-badge.critical {
  background: rgba(255, 71, 87, 0.9);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.header-badge.urgent {
  background: rgba(255, 107, 107, 0.9);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.header-badge.important {
  background: rgba(255, 165, 2, 0.9);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.header-badge.minor {
  background: rgba(46, 213, 115, 0.9);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.header-subtitle {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.conflict-summary {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.conflict-count {
  font-size: 2rem;
  font-weight: 800;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  min-width: 60px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.conflict-description {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.95;
  line-height: 1.4;
}

.urgency-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  align-self: flex-start;
}

.urgency-indicator i {
  animation: tick 1s infinite;
}

@keyframes tick {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.8rem;
  border-radius: 50%;
  transition: var(--transition);
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: rotate(90deg) scale(1.1);
}

.modal-body {
  padding: 0;
  flex: 1;
  overflow-y: auto;
}

.no-conflicts {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--success-color);
}

.no-conflicts-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--success-color), #229954);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
  box-shadow: var(--shadow-medium);
}

.no-conflicts h4 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.no-conflicts p {
  color: var(--secondary-color);
  font-size: 1rem;
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

.conflicts-container {
  padding: 2rem 3rem;
  display: grid;
  grid-template-columns: 1fr 380px;
  gap: 3rem;
  min-height: 700px;
  flex: 1;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.sidebar {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: var(--border-radius-large);
  padding: 2rem;
  border: 1px solid #e2e8f0;
  box-shadow: var(--shadow-light);
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.sidebar-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-title::before {
  content: '📊';
  font-size: 1rem;
}

.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid #e2e8f0;
  box-shadow: var(--shadow-light);
}

.quick-stat-label {
  font-size: 0.9rem;
  color: var(--secondary-color);
  font-weight: 500;
}

.quick-stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--dark-color);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quick-action-btn {
  padding: 0.75rem 1rem;
  background: white;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  text-align: center;
  font-size: 0.9rem;
}

.quick-action-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.quick-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.resolution-progress {
  margin: 1.5rem 0;
  padding: 1rem;
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid #e2e8f0;
}

.progress-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.progress-bar-sidebar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill-sidebar {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--primary-color));
  border-radius: 4px;
  transition: width 0.5s ease-out;
}

.progress-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--primary-color);
  text-align: center;
}

.time-status {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: var(--border-radius-large);
  border: 2px solid rgba(52, 152, 219, 0.2);
  text-align: center;
}

.time-status-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.time-status-info {
  font-size: 0.85rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.current-time {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
  background: white;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  border: 2px solid rgba(52, 152, 219, 0.2);
  box-shadow: var(--shadow-light);
  font-family: 'Courier New', monospace;
}

.conflict-resolution-item.resolved {
  opacity: 0.6;
  transform: scale(0.98);
  filter: grayscale(0.3);
}

.conflict-resolution-item.resolved::after {
  content: '✓ Résolu';
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--success-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 600;
}

.action-card.processing {
  opacity: 0.7;
  pointer-events: none;
}

.action-card.processing .action-icon {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Système de notifications */
.notifications-container {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
}

.notification {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  border-left: 4px solid;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification.success {
  border-left-color: var(--success-color);
  background: linear-gradient(135deg, #f8fff8, white);
}

.notification.error {
  border-left-color: var(--danger-color);
  background: linear-gradient(135deg, #fff5f5, white);
}

.notification.warning {
  border-left-color: var(--warning-color);
  background: linear-gradient(135deg, #fffbf5, white);
}

.notification.info {
  border-left-color: var(--primary-color);
  background: linear-gradient(135deg, #f8fbff, white);
}

.notification-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.notification.success .notification-icon {
  color: var(--success-color);
}

.notification.error .notification-icon {
  color: var(--danger-color);
}

.notification.warning .notification-icon {
  color: var(--warning-color);
}

.notification.info .notification-icon {
  color: var(--primary-color);
}

.notification-content {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--dark-color);
}

.notification-close {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: var(--transition);
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--dark-color);
}

/* Modal de suggestion de créneaux */
.slot-suggestion-modal {
  background: white;
  border-radius: var(--border-radius-large);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  animation: modalSlideIn 0.3s ease-out;
}

.slot-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: white;
}

.slot-modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.slot-modal-body {
  padding: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 0.9rem;
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  transition: var(--transition);
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.slot-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: #f8f9fa;
  color: var(--secondary-color);
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.btn-secondary:hover {
  background: #e9ecef;
  border-color: #ced4da;
}

@media (max-width: 768px) {
  .slot-suggestion-modal {
    width: 95%;
    max-height: 95vh;
  }

  .slot-modal-header,
  .slot-modal-body {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .slot-modal-actions {
    flex-direction: column;
  }
}

.conflicts-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: var(--border-radius-large);
  border: 1px solid #e2e8f0;
  box-shadow: var(--shadow-light);
}

.toolbar-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--secondary-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  background: white;
  color: var(--dark-color);
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.toolbar-actions {
  display: flex;
  gap: 0.75rem;
}

.toolbar-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background: white;
  color: var(--secondary-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
  font-weight: 500;
}

.toolbar-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.toolbar-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.conflicts-summary {
  margin-bottom: 3rem;
  padding: 2.5rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: var(--border-radius-large);
  border: 2px solid #e2e8f0;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.conflicts-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, var(--critical-color), var(--urgent-color), var(--important-color));
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2.5rem;
  justify-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2.5rem 2rem;
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  min-width: 200px;
  width: 100%;
  border: 3px solid transparent;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.8;
}

.stat-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-item.critical {
  border-color: var(--critical-color);
  color: var(--critical-color);
}

.stat-item.critical:hover {
  box-shadow: 0 12px 40px rgba(255, 71, 87, 0.25);
}

.stat-item.urgent {
  border-color: var(--urgent-color);
  color: var(--urgent-color);
}

.stat-item.urgent:hover {
  box-shadow: 0 12px 40px rgba(255, 107, 107, 0.25);
}

.stat-item.important {
  border-color: var(--important-color);
  color: var(--important-color);
}

.stat-item.important:hover {
  box-shadow: 0 12px 40px rgba(255, 165, 2, 0.25);
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 0.75rem;
  color: inherit;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  color: var(--dark-color);
  text-align: center;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.conflict-resolution-item {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  overflow: hidden;
  transition: var(--transition);
  border: 3px solid transparent;
  position: relative;
  animation: cardSlideIn 0.6s ease-out;
  animation-fill-mode: both;
  margin-bottom: 3rem;
}

.conflict-resolution-item:nth-child(1) { animation-delay: 0.1s; }
.conflict-resolution-item:nth-child(2) { animation-delay: 0.2s; }
.conflict-resolution-item:nth-child(3) { animation-delay: 0.3s; }
.conflict-resolution-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.conflict-resolution-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: currentColor;
  z-index: 1;
}

.conflict-resolution-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s;
}

.conflict-resolution-item:hover::after {
  left: 100%;
}

.conflict-resolution-item:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.conflict-resolution-item.critical {
  border-color: var(--critical-color);
  color: var(--critical-color);
  background: linear-gradient(135deg, #fff5f5, #fef2f2);
}

.conflict-resolution-item.critical:hover {
  box-shadow: 0 20px 50px rgba(255, 71, 87, 0.2);
}

.conflict-resolution-item.high {
  border-color: var(--urgent-color);
  color: var(--urgent-color);
  background: linear-gradient(135deg, #fff8f5, #fef3f2);
}

.conflict-resolution-item.high:hover {
  box-shadow: 0 20px 50px rgba(255, 107, 107, 0.2);
}

.conflict-resolution-item.medium {
  border-color: var(--important-color);
  color: var(--important-color);
  background: linear-gradient(135deg, #fffbf5, #fef9f2);
}

.conflict-resolution-item.medium:hover {
  box-shadow: 0 20px 50px rgba(255, 165, 2, 0.2);
}



.conflict-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  z-index: 2;
}

.conflict-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.2rem;
  box-shadow: var(--shadow-medium);
  position: relative;
  flex-shrink: 0;
}

.conflict-number::before {
  content: '';
  position: absolute;
  inset: -3px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  z-index: -1;
  opacity: 0.3;
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.1; }
  100% { transform: scale(1); opacity: 0.3; }
}

.conflict-title h4 {
  margin: 0 0 0.75rem 0;
  color: var(--dark-color);
  font-size: 1.3rem;
  font-weight: 700;
}

.conflict-badges {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.conflict-severity-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-large);
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  box-shadow: var(--shadow-light);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.conflict-severity-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: badgeShimmer 3s infinite;
}

@keyframes badgeShimmer {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.conflict-severity-badge.critical {
  background: var(--critical-color);
  color: white;
  border-color: rgba(255, 71, 87, 0.3);
  box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
}

.conflict-severity-badge.high {
  background: var(--urgent-color);
  color: white;
  border-color: rgba(255, 107, 107, 0.3);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.conflict-severity-badge.medium {
  background: var(--important-color);
  color: white;
  border-color: rgba(255, 165, 2, 0.3);
  box-shadow: 0 0 20px rgba(255, 165, 2, 0.3);
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.conflict-timeline {
  padding: 2rem;
  position: relative;
}

.conflict-timeline::before {
  content: '';
  position: absolute;
  left: 7px;
  top: 2rem;
  bottom: 2rem;
  width: 3px;
  background: linear-gradient(to bottom, var(--success-color), var(--danger-color));
  z-index: 1;
  border-radius: 2px;
  box-shadow: var(--shadow-light);
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  background: var(--primary-color);
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 0 0 3px var(--primary-color), var(--shadow-light);
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

.timeline-dot::before {
  content: '';
  position: absolute;
  inset: -6px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.2;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { transform: scale(1); opacity: 0.2; }
  50% { transform: scale(1.3); opacity: 0.1; }
}

.timeline-item.first .timeline-dot {
  background: var(--success-color);
  box-shadow: 0 0 0 3px var(--success-color), var(--shadow-light);
  color: var(--success-color);
}

.timeline-item.second .timeline-dot {
  background: var(--danger-color);
  box-shadow: 0 0 0 3px var(--danger-color), var(--shadow-light);
  color: var(--danger-color);
}

.appointment-card {
  flex: 1;
  background: white;
  border-radius: var(--border-radius-large);
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  border: 2px solid #e2e8f0;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.appointment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
}

.appointment-card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  transform: translateX(6px) translateY(-2px);
  border-color: var(--primary-color);
}

.appointment-time {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.appointment-time::before {
  content: '';
  width: 4px;
  height: 30px;
  background: var(--primary-color);
  border-radius: 2px;
}

.patient-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.patient-name i {
  color: var(--primary-color);
  font-size: 1.1rem;
  padding: 0.3rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 50%;
}

.appointment-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.2rem;
}

.appointment-details span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  color: var(--secondary-color);
  font-weight: 500;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: var(--border-radius);
  border: 1px solid #e2e8f0;
}

.appointment-details i {
  color: var(--primary-color);
  font-size: 1rem;
  flex-shrink: 0;
}

.appointment-id {
  font-size: 0.85rem;
  color: var(--secondary-color);
  font-weight: 600;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  padding: 0.4rem 0.8rem;
  border-radius: var(--border-radius);
  display: inline-block;
  border: 1px solid #cbd5e1;
  box-shadow: var(--shadow-light);
}

.timeline-gap {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
  position: relative;
}

.timeline-gap::before {
  content: '';
  position: absolute;
  left: 5px;
  top: -1rem;
  bottom: -1rem;
  width: 2px;
  background: linear-gradient(to bottom, var(--success-color), var(--danger-color));
  z-index: 1;
}

.gap-indicator {
  background: white;
  border-radius: var(--border-radius-large);
  padding: 1rem 1.5rem;
  box-shadow: var(--shadow-medium);
  border: 3px solid;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  z-index: 2;
  position: relative;
  min-width: 120px;
}

.gap-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: currentColor;
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

.gap-indicator.critical {
  border-color: var(--critical-color);
  background: linear-gradient(135deg, #fff5f5, white);
  color: var(--critical-color);
}

.gap-indicator.high {
  border-color: var(--urgent-color);
  background: linear-gradient(135deg, #fff8f5, white);
  color: var(--urgent-color);
}

.gap-indicator.medium {
  border-color: var(--important-color);
  background: linear-gradient(135deg, #fffbf5, white);
  color: var(--important-color);
}

.gap-indicator i {
  font-size: 1.5rem;
  color: inherit;
  animation: gap-pulse 2s infinite;
}

@keyframes gap-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.gap-time {
  font-weight: 800;
  font-size: 1rem;
  color: inherit;
  text-align: center;
}

.gap-indicator.critical,
.gap-indicator.critical i,
.gap-indicator.critical .gap-time {
  color: var(--critical-color);
}

.gap-indicator.high,
.gap-indicator.high i,
.gap-indicator.high .gap-time {
  color: var(--urgent-color);
}

.gap-indicator.medium,
.gap-indicator.medium i,
.gap-indicator.medium .gap-time {
  color: var(--important-color);
}

/* Styles pour les actions de résolution */
.resolution-actions {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-top: 1px solid var(--border-color);
}

.actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.actions-header h5 {
  margin: 0;
  color: var(--dark-color);
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.priority-indicator {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-indicator.critical {
  background: var(--critical-color);
  color: white;
}

.priority-indicator.high {
  background: var(--urgent-color);
  color: white;
}

.priority-indicator.medium {
  background: var(--important-color);
  color: white;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
}

.action-card {
  background: white;
  border-radius: var(--border-radius-large);
  padding: 2.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: 3px solid transparent;
  box-shadow: var(--shadow-medium);
  display: flex;
  gap: 2rem;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  min-height: 160px;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: currentColor;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
}

.action-card.primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.action-card.primary:hover {
  background: linear-gradient(135deg, #f8fbff, white);
  border-color: #2980b9;
  box-shadow: 0 15px 45px rgba(52, 152, 219, 0.2);
}

.action-card.secondary {
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.action-card.secondary:hover {
  background: linear-gradient(135deg, #fffbf5, white);
  border-color: #e67e22;
  box-shadow: 0 15px 45px rgba(243, 156, 18, 0.2);
}

.action-card.tertiary {
  border-color: var(--success-color);
  color: var(--success-color);
}

.action-card.tertiary:hover {
  background: linear-gradient(135deg, #f8fff8, white);
  border-color: #229954;
  box-shadow: 0 15px 45px rgba(39, 174, 96, 0.2);
}

.action-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-large);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-shrink: 0;
  box-shadow: var(--shadow-medium);
}

.action-card.primary .action-icon {
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: white;
}

.action-card.secondary .action-icon {
  background: linear-gradient(135deg, var(--warning-color), #e67e22);
  color: white;
}

.action-card.tertiary .action-icon {
  background: linear-gradient(135deg, var(--success-color), #229954);
  color: white;
}

.action-content {
  flex: 1;
}

.action-title {
  margin: 0 0 1rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--dark-color);
}

.action-description {
  margin: 0 0 1.5rem 0;
  color: var(--secondary-color);
  font-size: 1.05rem;
  line-height: 1.6;
}

.action-benefit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--success-color);
  padding: 0.5rem;
  background: rgba(39, 174, 96, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(39, 174, 96, 0.2);
}

/* Styles pour le footer */
.modal-footer {
  padding: 1.5rem 2rem;
  background: white;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.footer-info {
  flex: 1;
}

.conflict-impact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--danger-color);
  font-weight: 500;
  font-size: 0.9rem;
}

.footer-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn-secondary,
.btn-auto {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-secondary {
  background: var(--light-color);
  color: var(--secondary-color);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--secondary-color);
  color: white;
  transform: translateY(-1px);
}

.btn-auto {
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: white;
  box-shadow: var(--shadow-light);
  flex-direction: column;
  align-items: center;
  padding: 1rem 1.5rem;
}

.btn-auto:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-subtitle {
  font-size: 0.75rem;
  opacity: 0.9;
  font-weight: 400;
}

/* Responsive */
@media (max-width: 1600px) {
  .modal-content {
    max-width: 1500px;
  }

  .conflicts-container {
    grid-template-columns: 1fr 350px;
    gap: 2.5rem;
  }
}

@media (max-width: 1200px) {
  .conflicts-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .header-metrics {
    gap: 1rem;
  }

  .conflicts-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .toolbar-filters {
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
  }

  .toolbar-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-height: 95vh;
    max-width: none;
    min-height: 85vh;
  }

  .modal-header {
    padding: 1rem 1.5rem;
    min-height: 60px;
  }

  .header-metrics {
    display: none;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .header-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .header-title {
    flex-direction: column;
    gap: 0.8rem;
    align-items: center;
  }

  .header-title h3 {
    font-size: 1.3rem;
    text-align: center;
  }

  .conflict-summary {
    justify-content: center;
    flex-wrap: wrap;
  }

  .conflict-count {
    font-size: 1.5rem;
    min-width: 50px;
  }

  .urgency-indicator {
    align-self: center;
  }

  .conflicts-container {
    padding: 1.5rem;
    grid-template-columns: 1fr;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .conflict-timeline {
    padding: 1.5rem;
  }

  .appointment-details {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .action-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .action-card {
    padding: 1.5rem;
  }

  .action-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .modal-footer {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .footer-actions {
    width: 100%;
    justify-content: space-between;
  }

  .sidebar {
    padding: 1.5rem;
  }

  .quick-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}
</style>
