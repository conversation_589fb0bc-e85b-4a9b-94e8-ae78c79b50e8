<?php
/**
 * Script pour initialiser les types de créneaux
 */

require_once 'config/database.php';

try {
    echo "🔧 Initialisation des types de créneaux\n";
    echo "======================================\n\n";
    
    // Connexion à la base de données
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "✅ Connexion à la base de données réussie\n\n";
    
    // 1. Vérifier si la table existe
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'types_creneaux'");
    if ($tableCheck->rowCount() == 0) {
        echo "❌ La table 'types_creneaux' n'existe pas\n";
        echo "💡 Création de la table...\n";
        
        $createTable = "
            CREATE TABLE types_creneaux (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                duree INT NOT NULL,
                description TEXT,
                couleur VARCHAR(7) DEFAULT '#3b82f6',
                prix_base DECIMAL(10,2) DEFAULT 0,
                actif TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        
        $pdo->exec($createTable);
        echo "✅ Table 'types_creneaux' créée\n\n";
    } else {
        echo "✅ Table 'types_creneaux' existe\n\n";
    }
    
    // 2. Vérifier les types existants
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM types_creneaux");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "📊 Types de créneaux existants: {$count}\n\n";
    
    if ($count == 0) {
        echo "🔄 Insertion des types par défaut...\n";
        
        $defaultTypes = [
            [
                'nom' => 'Court',
                'duree' => 15,
                'description' => 'Consultation rapide, suivi simple',
                'couleur' => '#10b981',
                'prix_base' => 25000,
                'actif' => 1
            ],
            [
                'nom' => 'Standard',
                'duree' => 30,
                'description' => 'Consultation standard, examen général',
                'couleur' => '#3b82f6',
                'prix_base' => 50000,
                'actif' => 1
            ],
            [
                'nom' => 'Long',
                'duree' => 60,
                'description' => 'Consultation approfondie, premier rendez-vous',
                'couleur' => '#f59e0b',
                'prix_base' => 80000,
                'actif' => 1
            ],
            [
                'nom' => 'Urgence',
                'duree' => 20,
                'description' => 'Consultation d\'urgence',
                'couleur' => '#ef4444',
                'prix_base' => 75000,
                'actif' => 1
            ]
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO types_creneaux (nom, duree, description, couleur, prix_base, actif)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultTypes as $type) {
            $insertStmt->execute([
                $type['nom'],
                $type['duree'],
                $type['description'],
                $type['couleur'],
                $type['prix_base'],
                $type['actif']
            ]);
            
            echo "✅ Type '{$type['nom']}' ajouté ({$type['duree']}min - {$type['prix_base']} XOF)\n";
        }
        
        echo "\n🎉 {count($defaultTypes)} types par défaut ajoutés avec succès !\n\n";
    } else {
        echo "ℹ️ Types déjà présents, pas d'ajout nécessaire\n\n";
    }
    
    // 3. Afficher tous les types
    echo "📋 Liste des types de créneaux:\n";
    echo "------------------------------\n";
    
    $stmt = $pdo->query("
        SELECT id, nom, duree, description, couleur, prix_base, actif
        FROM types_creneaux
        ORDER BY duree
    ");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($types as $type) {
        $status = $type['actif'] ? '✅ Actif' : '❌ Inactif';
        $price = number_format($type['prix_base'], 0, ',', ' ') . ' XOF';
        
        echo sprintf(
            "🔹 %s - %dmin - %s - %s\n   📝 %s\n   🎨 %s\n\n",
            $type['nom'],
            $type['duree'],
            $price,
            $status,
            $type['description'] ?: 'Pas de description',
            $type['couleur']
        );
    }
    
    // 4. Test de l'API
    echo "🧪 Test de l'API slot-types:\n";
    echo "----------------------------\n";
    
    // Simuler un appel API
    $apiUrl = 'http://localhost:8000/slot-types';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json',
            'timeout' => 5
        ]
    ]);
    
    $response = @file_get_contents($apiUrl, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['status']) && $data['status'] === 'success') {
            echo "✅ API fonctionne - " . count($data['data']) . " types récupérés\n";
        } else {
            echo "⚠️ API répond mais format inattendu: " . substr($response, 0, 100) . "...\n";
        }
    } else {
        echo "❌ API non accessible à {$apiUrl}\n";
        echo "💡 Vérifiez que le serveur PHP est démarré\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "🏁 Initialisation terminée\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📋 Trace: " . $e->getTraceAsString() . "\n";
}
?>
