<template>
  <div class="slot-type-manager">
    <!-- En-tête -->
    <div class="header">
      <h3>🕒 Gestion des Types de Créneaux</h3>
      <button @click="showCreateModal = true" class="btn-primary">
        <i class="fas fa-plus"></i>
        Nouveau Type
      </button>
    </div>

    <!-- Statistiques rapides -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-number">{{ slotTypes.length }}</div>
          <div class="stat-label">Types configurés</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ activeTypesCount }}</div>
          <div class="stat-label">Types actifs</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">🕒</div>
        <div class="stat-content">
          <div class="stat-number">{{ averageDuration }} min</div>
          <div class="stat-label">Durée moyenne</div>
        </div>
      </div>
    </div>

    <!-- Liste des types -->
    <div class="types-list">
      <div 
        v-for="type in slotTypes" 
        :key="type.id"
        class="type-card"
        :class="{ inactive: !type.actif }"
      >
        <div class="type-header">
          <div class="type-info">
            <div 
              class="type-color" 
              :style="{ backgroundColor: type.couleur }"
            ></div>
            <div class="type-details">
              <h4>{{ type.nom }}</h4>
              <p class="type-description">{{ type.description }}</p>
            </div>
          </div>
          
          <div class="type-actions">
            <button 
              @click="editType(type)" 
              class="btn-edit"
              title="Modifier"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button 
              @click="toggleTypeStatus(type)" 
              :class="['btn-toggle', type.actif ? 'active' : 'inactive']"
              :title="type.actif ? 'Désactiver' : 'Activer'"
            >
              <i :class="type.actif ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
            </button>
            <button 
              @click="deleteType(type)" 
              class="btn-delete"
              title="Supprimer"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        
        <div class="type-stats">
          <div class="stat-item">
            <span class="stat-label">Durée:</span>
            <span class="stat-value">{{ type.duree }} min</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Prix:</span>
            <span class="stat-value">{{ formatPriceXOF(type.prix_base) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Utilisé:</span>
            <span class="stat-value">{{ type.nb_creneaux_utilises || 0 }} fois</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de création/édition -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModals">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? 'Modifier' : 'Créer' }} un Type de Créneau</h3>
          <button @click="closeModals" class="btn-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <form @submit.prevent="saveType" class="modal-form">
          <div class="form-group">
            <label for="nom">Nom du type *</label>
            <input 
              id="nom"
              v-model="formData.nom" 
              type="text" 
              required
              placeholder="Ex: Court, Standard, Long"
            >
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="duree">Durée (minutes) *</label>
              <input 
                id="duree"
                v-model.number="formData.duree" 
                type="number" 
                min="5" 
                max="180" 
                required
                placeholder="30"
              >
            </div>
            
            <div class="form-group">
              <label for="prix_base">Prix de base (XOF)</label>
              <input
                id="prix_base"
                v-model.number="formData.prix_base"
                type="number"
                min="0"
                step="1"
                placeholder="32750"
              >
            </div>
          </div>
          
          <div class="form-group">
            <label for="couleur">Couleur</label>
            <div class="color-input">
              <input 
                id="couleur"
                v-model="formData.couleur" 
                type="color"
              >
              <span class="color-preview" :style="{ backgroundColor: formData.couleur }"></span>
            </div>
          </div>
          
          <div class="form-group">
            <label for="description">Description</label>
            <textarea 
              id="description"
              v-model="formData.description" 
              rows="3"
              placeholder="Description du type de créneau..."
            ></textarea>
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                v-model="formData.actif" 
                type="checkbox"
              >
              <span class="checkmark"></span>
              Type actif
            </label>
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="closeModals" class="btn-secondary">
              Annuler
            </button>
            <button type="submit" class="btn-primary" :disabled="saving">
              {{ saving ? 'Enregistrement...' : 'Enregistrer' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { slotTypeService } from '@/services/slotTypeService'

export default {
  name: 'SlotTypeManager',
  
  setup() {
    const slotTypes = ref([])
    const showCreateModal = ref(false)
    const showEditModal = ref(false)
    const saving = ref(false)
    const editingType = ref(null)
    
    const formData = ref({
      nom: '',
      duree: 30,
      description: '',
      couleur: '#3b82f6',
      prix_base: 50,
      actif: true
    })
    
    // Computed
    const activeTypesCount = computed(() => {
      return slotTypes.value.filter(type => type.actif).length
    })
    
    const averageDuration = computed(() => {
      if (slotTypes.value.length === 0) return 0
      const total = slotTypes.value.reduce((sum, type) => sum + type.duree, 0)
      return Math.round(total / slotTypes.value.length)
    })
    
    // Methods
    async function loadSlotTypes() {
      try {
        const response = await slotTypeService.getStats()
        slotTypes.value = response.data
      } catch (error) {
        console.error('Erreur lors du chargement des types:', error)
      }
    }
    
    function editType(type) {
      editingType.value = type
      formData.value = { ...type }
      showEditModal.value = true
    }
    
    async function toggleTypeStatus(type) {
      try {
        const updatedData = { ...type, actif: !type.actif }
        await slotTypeService.update(type.id, updatedData)
        await loadSlotTypes()
      } catch (error) {
        console.error('Erreur lors de la mise à jour:', error)
      }
    }
    
    async function deleteType(type) {
      if (!confirm(`Êtes-vous sûr de vouloir supprimer le type "${type.nom}" ?`)) {
        return
      }
      
      try {
        await slotTypeService.delete(type.id)
        await loadSlotTypes()
      } catch (error) {
        console.error('Erreur lors de la suppression:', error)
        alert('Impossible de supprimer ce type : il est peut-être utilisé par des créneaux existants.')
      }
    }
    
    async function saveType() {
      saving.value = true
      
      try {
        if (showEditModal.value) {
          await slotTypeService.update(editingType.value.id, formData.value)
        } else {
          await slotTypeService.create(formData.value)
        }
        
        await loadSlotTypes()
        closeModals()
      } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error)
      } finally {
        saving.value = false
      }
    }
    
    function closeModals() {
      showCreateModal.value = false
      showEditModal.value = false
      editingType.value = null
      formData.value = {
        nom: '',
        duree: 30,
        description: '',
        couleur: '#3b82f6',
        prix_base: 32750,
        actif: true
      }
    }

    function formatPriceXOF(price) {
      return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price) + ' XOF'
    }
    
    onMounted(() => {
      loadSlotTypes()
    })
    
    return {
      slotTypes,
      showCreateModal,
      showEditModal,
      saving,
      formData,
      activeTypesCount,
      averageDuration,
      editType,
      toggleTypeStatus,
      deleteType,
      saveType,
      closeModals,
      formatPriceXOF
    }
  }
}
</script>

<style scoped>
.slot-type-manager {
  padding: 1.5rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.types-list {
  display: grid;
  gap: 1rem;
}

.type-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: all 0.2s;
}

.type-card.inactive {
  opacity: 0.6;
}

.type-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.type-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.type-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  flex-shrink: 0;
}

.type-details h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.125rem;
}

.type-description {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.type-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit, .btn-toggle, .btn-delete {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-edit {
  background: #3b82f6;
  color: white;
}

.btn-edit:hover {
  background: #2563eb;
}

.btn-toggle.active {
  background: #10b981;
  color: white;
}

.btn-toggle.inactive {
  background: #6b7280;
  color: white;
}

.btn-delete {
  background: #ef4444;
  color: white;
}

.btn-delete:hover {
  background: #dc2626;
}

.type-stats {
  display: flex;
  gap: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-item .stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
}

.stat-item .stat-value {
  font-weight: 600;
  color: #1f2937;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6b7280;
}

.modal-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
}

.color-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-input input[type="color"] {
  width: 50px;
  height: 40px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-primary, .btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .slot-type-manager {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .type-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .type-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>
