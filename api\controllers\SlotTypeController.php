<?php
require_once __DIR__ . '/../models/SlotType.php';

class SlotTypeController {
    private $db;
    private $slotTypeModel;
    
    public function __construct($database) {
        $this->db = $database;
        $this->slotTypeModel = new SlotType($database);
    }
    
    /**
     * Récupérer tous les types de créneaux
     */
    public function index() {
        try {
            $types = $this->slotTypeModel->getAll();
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $types
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->index: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des types de créneaux'
            ]);
        }
    }
    
    /**
     * Récupérer les types actifs
     */
    public function getActive() {
        try {
            $types = $this->slotTypeModel->getActive();
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $types
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->getActive: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des types actifs'
            ]);
        }
    }
    
    /**
     * Créer un nouveau type de créneau
     */
    public function store() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation
            if (!isset($input['nom']) || !isset($input['duree'])) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Nom et durée sont requis'
                ]);
                return;
            }
            
            if ($input['duree'] < 5 || $input['duree'] > 180) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'La durée doit être entre 5 et 180 minutes'
                ]);
                return;
            }
            
            $typeId = $this->slotTypeModel->create($input);
            
            if ($typeId) {
                $newType = $this->slotTypeModel->getById($typeId);
                
                http_response_code(201);
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Type de créneau créé avec succès',
                    'data' => $newType
                ]);
            } else {
                throw new Exception("Erreur lors de la création du type");
            }
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->store: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création du type de créneau'
            ]);
        }
    }
    
    /**
     * Mettre à jour un type de créneau
     */
    public function update($id) {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation
            if (!isset($input['nom']) || !isset($input['duree'])) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Nom et durée sont requis'
                ]);
                return;
            }
            
            if ($input['duree'] < 5 || $input['duree'] > 180) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'La durée doit être entre 5 et 180 minutes'
                ]);
                return;
            }
            
            $updated = $this->slotTypeModel->update($id, $input);
            
            if ($updated) {
                $updatedType = $this->slotTypeModel->getById($id);
                
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Type de créneau mis à jour avec succès',
                    'data' => $updatedType
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Type de créneau non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour du type de créneau'
            ]);
        }
    }
    
    /**
     * Supprimer un type de créneau
     */
    public function delete($id) {
        try {
            $deleted = $this->slotTypeModel->delete($id);
            
            if ($deleted) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Type de créneau supprimé avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Type de créneau non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Récupérer les statistiques d'utilisation
     */
    public function getStats() {
        try {
            $stats = $this->slotTypeModel->getUsageStats();
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->getStats: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }
    
    /**
     * Initialiser les types par défaut
     */
    public function initializeDefaults() {
        try {
            $initialized = $this->slotTypeModel->initializeDefaultTypes();
            
            if ($initialized) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Types de créneaux par défaut initialisés'
                ]);
            } else {
                echo json_encode([
                    'status' => 'info',
                    'message' => 'Types de créneaux déjà initialisés'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans SlotTypeController->initializeDefaults: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'initialisation des types par défaut'
            ]);
        }
    }
}
?>
