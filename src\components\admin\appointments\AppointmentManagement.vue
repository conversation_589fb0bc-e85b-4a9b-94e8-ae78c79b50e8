<template>
  <div class="appointments-management">
    <div class="header">
      <h2>Gestion des Rendez-vous</h2>
      <button class="add-btn" @click="showAddForm = true">
        <i class="fas fa-plus"></i>
        Nouveau Rendez-vous
      </button>
    </div>

    <!-- Filtres -->
    <div class="filters">
      <div class="filter-group">
        <label>Période</label>
        <select v-model="filters.period">
          <option value="today">Aujourd'hui</option>
          <option value="week">Cette semaine</option>
          <option value="month">Ce mois</option>
          <option value="all">Tous</option>
        </select>
      </div>
      <div class="filter-group">
        <label>Statut</label>
        <select v-model="filters.status">
          <option value="all">Tous</option>
          <option value="pending">En attente</option>
          <option value="confirmed">Confirmé</option>
          <option value="completed">Terminé</option>
          <option value="cancelled">Annulé</option>
        </select>
      </div>
      <div class="filter-group">
        <label>Médecin</label>
        <select v-model="filters.doctorId">
          <option value="">Tous les médecins</option>
          <option v-for="doctor in doctors" :key="doctor.id" :value="doctor.id">
            Dr. {{ doctor.nom }} {{ doctor.prenom }}
          </option>
        </select>
      </div>
    </div>

    <!-- Indicateurs de chargement et d'erreur -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Chargement des rendez-vous...</p>
    </div>

    <div v-else-if="error" class="error-state">
      <div class="error-icon">⚠️</div>
      <p>{{ error }}</p>
      <button @click="retryLoad" class="retry-btn">Réessayer</button>
    </div>

    <!-- Liste des rendez-vous -->
    <div v-else-if="filteredAppointments.length" class="appointments-list">
      <div v-for="appointment in filteredAppointments"
           :key="appointment.id"
           class="appointment-card"
           :class="appointment.status || appointment.statut">
        <div class="appointment-header">
          <div class="date-time">
            <i class="fas fa-calendar"></i>
            {{ formatDate(appointment.date) }}
            <i class="fas fa-clock"></i>
            {{ formatTime(appointment.time) }}
          </div>
          <div class="status-badge" :class="appointment.status">
            {{ appointment.status }}
          </div>
        </div>
        
        <div class="appointment-details">
          <div class="patient-info">
            <h4>Patient</h4>
            <p>{{ appointment.patient_name }}</p>
            <small>{{ appointment.patient_email }}</small>
          </div>
          
          <div class="doctor-info">
            <h4>Médecin</h4>
            <p>Dr. {{ appointment.doctor_name }}</p>
            <small>{{ appointment.speciality }}</small>
          </div>
        </div>
        
        <div class="appointment-actions">
          <button class="action-btn edit" @click="editAppointment(appointment)">
            <i class="fas fa-edit"></i>
            Modifier
          </button>
          <button class="action-btn cancel" v-if="appointment.status === 'pending'" 
                  @click="cancelAppointment(appointment)">
            <i class="fas fa-times"></i>
            Annuler
          </button>
          <button class="action-btn complete" v-if="appointment.status === 'confirmed'"
                  @click="completeAppointment(appointment)">
            <i class="fas fa-check"></i>
            Terminer
          </button>
        </div>
      </div>
    </div>
    <div v-else class="no-appointments">
      <i class="fas fa-calendar-times"></i>
      <p>Aucun rendez-vous trouvé</p>
    </div>

    <!-- Modal d'ajout/modification -->
    <div v-if="showAddForm" class="modal-overlay" @click.self="showAddForm = false">
      <div class="modal-content">
        <!-- Formulaire à implémenter -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useDoctorStore } from '@/stores/doctorStore'

const appointmentStore = useAppointmentStore()
const doctorStore = useDoctorStore()
const showAddForm = ref(false)

const filters = ref({
  period: 'week',
  status: 'all',
  doctorId: ''
})

// Utiliser les stores
const appointments = computed(() => appointmentStore.appointments)
const doctors = computed(() => doctorStore.doctors)
const loading = computed(() => appointmentStore.loading || doctorStore.loading)
const error = computed(() => appointmentStore.error || doctorStore.error)

// Charger les rendez-vous et les médecins au montage
onMounted(async () => {
  try {
    console.log('Chargement des données...')
    await Promise.all([
      appointmentStore.fetchAppointments(),
      doctorStore.fetchDoctors()
    ])
    console.log('Données chargées:', {
      appointments: appointments.value.length,
      doctors: doctors.value.length
    })
  } catch (error) {
    console.error('Erreur lors du chargement des données:', error)
  }
})

// Fonction pour réessayer le chargement
const retryLoad = async () => {
  appointmentStore.clearError()
  doctorStore.clearError()
  await Promise.all([
    appointmentStore.fetchAppointments(),
    doctorStore.fetchDoctors()
  ])
}

// Filtrer les rendez-vous
const filteredAppointments = computed(() => {
  return appointments.value.filter(appointment => {
    const matchesStatus = filters.value.status === 'all' || 
                         appointment.status === filters.value.status
    const matchesDoctor = !filters.value.doctorId || 
                         appointment.doctor_id === filters.value.doctorId
    
    // Filtrer par période
    const appointmentDate = new Date(appointment.date)
    const today = new Date()
    const isToday = appointmentDate.toDateString() === today.toDateString()
    
    switch (filters.value.period) {
      case 'today':
        return isToday && matchesStatus && matchesDoctor
      case 'week':
        const weekStart = new Date(today.setDate(today.getDate() - today.getDay()))
        const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6))
        return appointmentDate >= weekStart && 
               appointmentDate <= weekEnd && 
               matchesStatus && 
               matchesDoctor
      case 'month':
        return appointmentDate.getMonth() === today.getMonth() && 
               appointmentDate.getFullYear() === today.getFullYear() && 
               matchesStatus && 
               matchesDoctor
      default:
        return matchesStatus && matchesDoctor
    }
  })
})

// Formatage des dates et heures
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

const formatTime = (time) => {
  return time.substring(0, 5)
}

// Actions sur les rendez-vous
const editAppointment = (appointment) => {
  // À implémenter
}
const cancelAppointment = async (appointment) => {
  if (!confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) return
  
  try {
    const response = await fetch(`http://localhost:8000/api/appointments/${appointment.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'cancelled' }) // Envoyez seulement le nécessaire
    })

    if (!response.ok) throw new Error('Échec de la requête')

    const updatedAppointment = await response.json()
    appointments.value = appointments.value.map(a => 
      a.id === updatedAppointment.id ? updatedAppointment : a
    )
    alert('Rendez-vous annulé avec succès!')
  } catch (error) {
    console.error('Erreur:', error)
    alert(`Erreur lors de l'annulation: ${error.message}`)
  }
}
const completeAppointment = async (appointment) => {
  try {
    const response = await fetch(`http://localhost:8000/api/appointments/${appointment.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'completed' })
    })

    if (!response.ok) throw new Error('Échec de la requête')

    const updatedAppointment = await response.json()
    appointments.value = appointments.value.map(a => 
      a.id === updatedAppointment.id ? updatedAppointment : a
    )
    alert('Rendez-vous marqué comme terminé!')
  } catch (error) {
    console.error('Erreur:', error)
    alert(`Erreur: ${error.message}`)
  }
}
</script>

<style scoped>
.appointments-management {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header h2 {
  font-size: 1.5rem;
  color: #1e293b;
  margin: 0;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.add-btn:hover {
  background: #0284c7;
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  min-width: 150px;
}

.appointments-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.appointment-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.date-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e293b;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.pending {
  background: #fff7ed;
  color: #9a3412;
}

.status-badge.confirmed {
  background: #ecfdf5;
  color: #047857;
}

.status-badge.completed {
  background: #f0f9ff;
  color: #0369a1;
}

.status-badge.cancelled {
  background: #fef2f2;
  color: #991b1b;
}

.appointment-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.patient-info,
.doctor-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.patient-info h4,
.doctor-info h4 {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.patient-info p,
.doctor-info p {
  font-size: 1rem;
  color: #1e293b;
  margin: 0;
}

.patient-info small,
.doctor-info small {
  color: #64748b;
}

.appointment-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.edit {
  background: white;
  color: #0ea5e9;
  border: 1px solid #0ea5e9;
}

.action-btn.edit:hover {
  background: #f0f9ff;
}

.action-btn.cancel {
  background: white;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.action-btn.cancel:hover {
  background: #fef2f2;
}

.action-btn.complete {
  background: white;
  color: #10b981;
  border: 1px solid #10b981;
}

.action-btn.complete:hover {
  background: #ecfdf5;
}

.no-appointments {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.no-appointments i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-appointments p {
  margin: 0;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 2rem;
}

@media (max-width: 640px) {
  .appointments-management {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
  }

  .appointment-details {
    grid-template-columns: 1fr;
  }

  .appointment-actions {
    flex-direction: column;
  }
}

/* États de chargement et d'erreur */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e3e3e3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background: #2980b9;
}
</style>