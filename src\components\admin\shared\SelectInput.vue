<template>
  <div class="relative">
    <select
      :value="modelValue"
      @change="$emit('update:modelValue', $event.target.value)"
      class="select-input"
      :class="{
        'select-input-error': error,
        'select-input-disabled': disabled
      }"
      :disabled="disabled"
    >
      <option v-if="placeholder" value="" disabled selected hidden>
        {{ placeholder }}
      </option>
      <option
        v-for="option in normalizedOptions"
        :key="option.value"
        :value="option.value"
        class="select-option"
      >
        <span v-if="option.icon" class="mr-2">
          <i class="fas fa-{{ option.icon }}"></i>
        </span>
        {{ optionPrefix }}{{ option.label }}
      </option>
    </select>
    <div class="select-arrow">
      <i class="fas fa-chevron-down"></i>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: [String, Number],
  options: {
  type: Array,
  required: true,
  default: () => []
},
  optionLabel: {
    type: String,
    default: 'label'
  },
  optionValue: {
    type: String,
    default: 'value'
  },
  optionPrefix: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  error: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showIcon: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const normalizedOptions = computed(() => {
  const opts = Array.isArray(props.options) ? props.options : [];
  return opts.map(option => {
    if (typeof option === 'string') {
      return { 
        value: option, 
        label: option,
        icon: props.showIcon ? option.toLowerCase().replace(' ', '-') : null
      }
    }
    return {
      value: option[props.optionValue] ?? option.value,
      label: option[props.optionLabel] ?? option.label,
      icon: option.icon || (props.showIcon ? option[props.optionLabel]?.toLowerCase().replace(' ', '-') : null)
    }
  })
})

</script>

<style scoped>
.select-input {
  @apply w-full px-4 py-3 bg-white rounded-xl border-2 border-gray-200 focus:outline-none focus:ring-4 focus:ring-opacity-30 transition-all duration-200 appearance-none cursor-pointer shadow-sm hover:shadow-md pr-10;
}

.select-input:focus {
  @apply border-blue-400 focus:ring-blue-100 transform -translate-y-0.5;
}

.select-input-error {
  @apply border-red-300 bg-red-50/50 focus:border-red-400 focus:ring-red-100;
}

.select-input-disabled {
  @apply bg-gray-100 cursor-not-allowed opacity-75;
}

.select-arrow {
  @apply absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400;
}

.select-option {
  @apply py-2 px-3 hover:bg-blue-50;
}

/* Animation when opened */
select:focus + .select-arrow {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

/* Custom scrollbar for select dropdown */
select::-webkit-scrollbar {
  width: 8px;
}

select::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

select::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}
</style>