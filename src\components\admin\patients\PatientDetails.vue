<template>
  <div class="patient-details">
    <div class="modal-overlay" @click.self="$emit('close')">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Détails du Patient</h2>
          <button class="close-btn" @click="$emit('close')">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="details-content">
          <!-- Informations Personnelles -->
          <div class="details-section">
            <h3>Informations Personnelles</h3>
            <div class="info-grid">
              <div class="info-item">
                <label>Nom</label>
                <p>{{ patient.nom }}</p>
              </div>
              <div class="info-item">
                <label>Prénom</label>
                <p>{{ patient.prenom }}</p>
              </div>
              <div class="info-item">
                <label>Date de Naissance</label>
                <p>{{ formatDate(patient.date_naissance) }}</p>
              </div>
              <div class="info-item">
                <label>Email</label>
                <p>{{ patient.email }}</p>
              </div>
              <div class="info-item">
                <label>Téléphone</label>
                <p>{{ patient.telephone }}</p>
              </div>
              <div class="info-item">
                <label>Adresse</label>
                <p>{{ patient.adresse }}</p>
              </div>
            </div>
          </div>

          <!-- Informations Médicales -->
          <div class="details-section">
            <h3>Informations Médicales</h3>
            <div class="info-grid">
              <div class="info-item">
                <label>Groupe Sanguin</label>
                <p>{{ patient.groupe_sanguin || 'Non spécifié' }}</p>
              </div>
              <div class="info-item full-width">
                <label>Allergies</label>
                <p>{{ patient.allergies || 'Aucune allergie connue' }}</p>
              </div>
              <div class="info-item full-width">
                <label>Antécédents Médicaux</label>
                <p>{{ patient.antecedents || 'Aucun antécédent connu' }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="edit-btn" @click="$emit('edit', patient)">
            Modifier
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  patient: {
    type: [Object, null],
    required: true
  }
})

const formatDate = (date) => {
  if (!date) return 'Non spécifié'
  return new Date(date).toLocaleDateString('fr-FR')
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.modal-header h2 {
  font-size: 1.5rem;
  color: #1e293b;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #1e293b;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.details-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
}

.details-section h3 {
  color: #0ea5e9;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.info-item p {
  font-size: 1rem;
  color: #1e293b;
  margin: 0;
}

.modal-footer {
  margin-top: 2rem;
  display: flex;
  justify-content: flex-end;
}

.edit-btn {
  background: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.edit-btn:hover {
  background: #0284c7;
}

@media (max-width: 640px) {
  .modal-content {
    padding: 1rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style> 