<template>
  <span 
    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
    :class="roleClasses"
  >
    {{ roleLabel }}
  </span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  role: {
    type: String,
    required: true,
    validator: (value) => ['admin', 'doctor'].includes(value)
  }
})

const roleLabel = computed(() => {
  return {
    admin: 'Administrate<PERSON>',
    doctor: '<PERSON><PERSON><PERSON><PERSON>'
  }[props.role]
})

const roleClasses = computed(() => {
  return {
    admin: 'bg-purple-100 text-purple-800',
    doctor: 'bg-blue-100 text-blue-800'
  }[props.role]
})
</script>