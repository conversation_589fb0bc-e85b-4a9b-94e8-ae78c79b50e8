<?php

class DashboardController {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function getDashboardStats() {
        try {
            $stats = [
                'totalPatients' => $this->getCount('patients'),
                'totalDoctors' => $this->getCount('medecins'),
                'appointmentsToday' => $this->getAppointmentsToday(),
                'upcomingAppointments' => $this->getUpcomingAppointments()
            ];

            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }

    private function getCount($table) {
        $stmt = $this->db->query("SELECT COUNT(*) FROM $table");
        return $stmt->fetchColumn();
    }

    private function getAppointmentsToday() {
        $today = date('Y-m-d');
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM rendez_vous WHERE DATE(date_rendez_vous) = ?");
        $stmt->execute([$today]);
        return $stmt->fetchColumn();
    }

    private function getUpcomingAppointments() {
        $today = date('Y-m-d H:i:s');
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM rendez_vous WHERE date_rendez_vous > ?");
        $stmt->execute([$today]);
        return $stmt->fetchColumn();
    }

    public function getDoctorStatistics($doctorId) {
        try {
            $stats = [
                'totalPatients' => $this->getDoctorPatientCount($doctorId),
                'appointmentsToday' => $this->getDoctorAppointmentsToday($doctorId),
                'upcomingAppointments' => $this->getDoctorUpcomingAppointments($doctorId),
                'totalAppointments' => $this->getDoctorTotalAppointments($doctorId),
                'suggestedSlots' => $this->getDoctorSuggestedSlots($doctorId)
            ];

            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur getDoctorStatistics: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques du médecin'
            ]);
        }
    }

    private function getDoctorPatientCount($doctorId) {
        $stmt = $this->db->prepare("SELECT COUNT(DISTINCT id_patient) FROM rendez_vous WHERE id_medecin = ?");
        $stmt->execute([$doctorId]);
        return $stmt->fetchColumn();
    }

    private function getDoctorAppointmentsToday($doctorId) {
        $today = date('Y-m-d');
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM rendez_vous WHERE id_medecin = ? AND DATE(date_rendez_vous) = ?");
        $stmt->execute([$doctorId, $today]);
        return $stmt->fetchColumn();
    }

    private function getDoctorUpcomingAppointments($doctorId) {
        $today = date('Y-m-d H:i:s');
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM rendez_vous WHERE id_medecin = ? AND date_rendez_vous > ?");
        $stmt->execute([$doctorId, $today]);
        return $stmt->fetchColumn();
    }

    private function getDoctorTotalAppointments($doctorId) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM rendez_vous WHERE id_medecin = ?");
        $stmt->execute([$doctorId]);
        return $stmt->fetchColumn();
    }

    private function getDoctorSuggestedSlots($doctorId) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM creneaux_suggeres WHERE id_medecin = ?");
        $stmt->execute([$doctorId]);
        return $stmt->fetchColumn();
    }
}