<?php
require_once __DIR__ . '/../config/database.php';

class AuditController {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Récupère les journaux d'activité avec pagination
     */
    public function getLogs() {
        try {
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? max(1, (int)$_GET['limit']) : 10;
            $offset = ($page - 1) * $limit;
            
            // Compte total
            $countQuery = "SELECT COUNT(*) FROM journaux_activite";
            $countStmt = $this->db->prepare($countQuery);
            $countStmt->execute();
            $total = $countStmt->fetchColumn();
            
            // Récupère les logs
             $query = "SELECT j.*, 
                  d.nom AS docteur_nom, d.prenom AS docteur_prenom,
                  p.nom AS patient_nom, p.prenom AS patient_prenom
                  FROM journaux_activite j
                  LEFT JOIN medecins d ON j.docteur_id = d.id
                  LEFT JOIN patients p ON j.patient_id = p.id
                  ORDER BY j.date_creation DESC 
                  LIMIT :limit OFFSET :offset";
                  
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            $journaux = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $response = [
                'donnees' => $journaux,
                'pagination' => [
                    'total' => $total,
                    'page' => $page,
                    'limite' => $limit,
                    'pages_total' => ceil($total / $limit)
                ]
            ];
            
            http_response_code(200);
            echo json_encode($response);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['erreur' => $e->getMessage()]);
        }
    }

    /**
     * Ajoute une entrée de journal (méthode utilitaire)
     */
    public static function logAction($action, $userId = null, $details = null) {
        try {
            $db = Database::getConnection();
            
            $query = "INSERT INTO journaux_activite (action, utilisateur_id, details, adresse_ip, agent_utilisateur) 
                      VALUES (:action, :utilisateur_id, :details, :adresse_ip, :agent_utilisateur)";
            
            $stmt = $db->prepare($query);
            $stmt->execute([
                ':action' => $action,
                ':utilisateur_id' => $userId,
                ':details' => json_encode($details),
                ':adresse_ip' => $_SERVER['REMOTE_ADDR'] ?? 'inconnu',
                ':agent_utilisateur' => $_SERVER['HTTP_USER_AGENT'] ?? 'inconnu'
            ]);
            
            return true;
            
        } catch (PDOException $e) {
            error_log('Erreur de journalisation: ' . $e->getMessage());
            return false;
        }
    }
}
?>