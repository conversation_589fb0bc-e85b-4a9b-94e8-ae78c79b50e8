
<!-- components/ui/LoadingSpinner.vue -->
<template>
  <div class="loading-spinner" :class="sizeClass">
    <div class="spinner" :style="spinnerStyle">
      <div class="spinner-circle"></div>
    </div>
    <div v-if="text" class="loading-text">{{ text }}</div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'LoadingSpinner',
  props: {
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    color: {
      type: String,
      default: '#3b82f6'
    },
    text: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const sizeClass = computed(() => `loading-${props.size}`)
    
    const spinnerStyle = computed(() => ({
      '--spinner-color': props.color
    }))
    
    return {
      sizeClass,
      spinnerStyle
    }
  }
}
</script>

<style scoped>
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.spinner {
  --spinner-color: #3b82f6;
  width: var(--spinner-size);
  height: var(--spinner-size);
  position: relative;
}

.loading-small { --spinner-size: 1rem; }
.loading-medium { --spinner-size: 2rem; }
.loading-large { --spinner-size: 3rem; }

.spinner-circle {
  width: 100%;
  height: 100%;
  border: 2px solid #e5e7eb;
  border-top: 2px solid var(--spinner-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6b7280;
  font-size: 0.875rem;
  text-align: center;
}
</style>