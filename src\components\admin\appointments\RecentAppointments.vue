<template>
  <div class="recent-appointments">
    <h2>Rendez-vous récents</h2>
    <div v-if="isLoading" class="loading">Chargement...</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <ul v-else-if="appointments.length">
      <li v-for="appt in appointments" :key="appt.id" class="appointment-item">
        <div class="info">
          <span class="date">{{ formatDate(appt.date) }}</span>
          <span class="time">{{ appt.time }}</span>
        </div>
        <div class="details">
          <span class="patient">{{ appt.patient?.nom }} {{ appt.patient?.prenom }}</span>
          <span class="doctor">Dr. {{ appt.doctor?.nom }}</span>
        </div>
      </li>
    </ul>
    <div v-else class="empty">Aucun rendez-vous récent.</div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'

const appointmentStore = useAppointmentStore()

onMounted(() => {
  appointmentStore.fetchRecent() // À adapter selon ton store
})

const isLoading = computed(() => appointmentStore.isLoading)
const error = computed(() => appointmentStore.error)
const appointments = computed(() => appointmentStore.recentAppointments || [])

// Fonction utilitaire pour le formatage de la date
function formatDate(dateStr) {
  const d = new Date(dateStr)
  return d.toLocaleDateString('fr-FR', { weekday: 'short', day: '2-digit', month: 'short', year: 'numeric' })
}
</script>

<style scoped>
.recent-appointments {
  background: #f8fafc;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px #2563eb22;
  max-width: 500px;
  margin: 0 auto;
}
h2 {
  margin-bottom: 1rem;
  color: #2563eb;
  font-size: 1.3rem;
}
ul {
  list-style: none;
  padding: 0;
}
.appointment-item {
  padding: 0.75em 0;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 0.25em;
}
.appointment-item:last-child {
  border-bottom: none;
}
.info {
  font-weight: 500;
  color: #334155;
}
.date {
  margin-right: 1em;
}
.details {
  font-size: 0.95em;
  color: #64748b;
  display: flex;
  gap: 1em;
}
.loading, .empty, .error {
  text-align: center;
  color: #64748b;
  margin: 1.5em 0;
}
.error {
  color: #dc2626;
}
</style>
