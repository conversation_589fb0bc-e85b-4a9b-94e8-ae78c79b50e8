<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Statistiques - Dr. {{ doctor?.fullName }}</h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="stats-content">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ stats.totalAppointments }}</span>
              <span class="stat-label">Rendez-vous totaux</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-user-clock"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ stats.averagePerDay }}</span>
              <span class="stat-label">Moyenne par jour</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ stats.averageDuration }}min</span>
              <span class="stat-label">Durée moyenne</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-user-plus"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ stats.newPatients }}</span>
              <span class="stat-label">Nouveaux patients</span>
            </div>
          </div>
        </div>

        <div class="chart-section">
          <h3>Activité hebdomadaire</h3>
          <div class="activity-chart">
            <div 
              v-for="(value, day) in weeklyActivity" 
              :key="day"
              class="activity-bar"
            >
              <div 
                class="bar"
                :style="{ height: `${value}%` }"
                :class="getBarClass(value)"
              ></div>
              <span class="day-label">{{ day.charAt(0) }}</span>
            </div>
          </div>
        </div>

        <div class="chart-section">
          <h3>Répartition des consultations</h3>
          <div class="consultation-types">
            <div 
              v-for="(type, index) in consultationTypes" 
              :key="index"
              class="type-item"
            >
              <div class="type-info">
                <span class="type-name">{{ type.name }}</span>
                <span class="type-value">{{ type.value }}%</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress"
                  :style="{ width: `${type.value}%` }"
                  :class="getProgressClass(type.value)"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  doctor: {
    type: Object,
    required: true
  }
})

// Données simulées
const stats = {
  totalAppointments: 245,
  averagePerDay: 12,
  averageDuration: 25,
  newPatients: 18
}

const weeklyActivity = {
  'Lundi': 85,
  'Mardi': 65,
  'Mercredi': 90,
  'Jeudi': 75,
  'Vendredi': 70
}

const consultationTypes = [
  { name: 'Consultation standard', value: 60 },
  { name: 'Suivi', value: 25 },
  { name: 'Urgence', value: 10 },
  { name: 'Téléconsultation', value: 5 }
]

const getBarClass = (value) => {
  if (value > 80) return 'high'
  if (value > 50) return 'medium'
  return 'low'
}

const getProgressClass = (value) => {
  if (value > 50) return 'primary'
  if (value > 25) return 'secondary'
  return 'tertiary'
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--admin-dark);
}

.close-btn {
  background: none;
  border: none;
  color: var(--admin-gray);
  cursor: pointer;
  font-size: 1.25rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: var(--admin-blue);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-dark);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--admin-gray);
}

.chart-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.chart-section h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: var(--admin-dark);
}

.activity-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200px;
  padding: 1rem 0;
}

.activity-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 40px;
}

.bar {
  width: 100%;
  background: #e2e8f0;
  border-radius: 4px;
  transition: height 0.3s ease;
}

.bar.high {
  background: #34d399;
}

.bar.medium {
  background: #fbbf24;
}

.bar.low {
  background: #ef4444;
}

.day-label {
  font-size: 0.875rem;
  color: var(--admin-gray);
}

.consultation-types {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.type-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.type-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.type-name {
  font-size: 0.875rem;
  color: var(--admin-dark);
}

.type-value {
  font-size: 0.875rem;
  color: var(--admin-gray);
}

.progress-bar {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  transition: width 0.3s ease;
}

.progress.primary {
  background: var(--admin-blue);
}

.progress.secondary {
  background: #8b5cf6;
}

.progress.tertiary {
  background: #ec4899;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .activity-bar {
    width: 30px;
  }
}
</style> 