<template>
  <transition name="fade-slide">
    <div v-if="errors.length > 0" class="error-container">
      <div class="error-icon-container">
        <i class="fas fa-exclamation-circle error-icon"></i>
      </div>
      <div class="error-content">
        <h3 class="error-title">V<PERSON><PERSON>z corriger les erreurs suivantes :</h3>
        <ul class="error-list">
          <li v-for="(error, index) in errors" :key="index" class="error-item">
            <i class="fas fa-chevron-right error-arrow"></i>
            {{ error }}
          </li>
        </ul>
      </div>
    </div>
  </transition>
</template>

<script setup>
defineProps({
  errors: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>

<style scoped>
.error-container {
  @apply bg-red-50 border-l-4 border-red-400 p-4 rounded-lg flex items-start space-x-3;
  animation: bounce-in 0.5s ease-out;
}

.error-icon-container {
  @apply flex-shrink-0;
}

.error-icon {
  @apply text-red-400 text-xl mt-0.5;
}

.error-content {
  @apply flex-1;
}

.error-title {
  @apply text-sm font-medium text-red-800;
}

.error-list {
  @apply mt-2 text-sm text-red-700 space-y-1;
}

.error-item {
  @apply flex items-start space-x-2;
}

.error-arrow {
  @apply text-red-400 text-xs mt-0.5 flex-shrink-0;
}

/* Animations */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

@keyframes bounce-in {
  0% {
    transform: translateX(10px);
    opacity: 0;
  }
  50% {
    transform: translateX(-3px);
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>