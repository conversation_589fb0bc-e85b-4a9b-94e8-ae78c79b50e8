<script setup>
import { computed } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'

const store = useAppointmentStore()
const appointments = computed(() => {
  return store.todayAppointments.map(apt => ({
    ...apt,
    time: new Date(apt.date_rendez_vous || apt.date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    }),
    patient: apt.patient_nom || apt.patient || 'Patient non défini',
    status: apt.statut || apt.status || 'planifie'
  }))
})

function cancel(id) {
  store.cancelAppointment(id)
}

function confirm(id) {
  store.confirmAppointment(id)
}

function statusClass(status) {
  return {
    'planifie': 'status-planned',
    'planifié': 'status-planned',
    'confirme': 'status-confirmed',
    'confirmé': 'status-confirmed',
    'annule': 'status-cancelled',
    'annulé': 'status-cancelled',
    'termine': 'status-completed',
    'terminé': 'status-completed'
  }[status] || 'status-default'
}

function getStatusLabel(status) {
  return {
    'planifie': 'Planifié',
    'confirme': 'Confirmé',
    'annule': 'Annulé',
    'termine': 'Terminé'
  }[status] || status
}
</script>

<template>
  <div class="appointment-manager">
    <div class="header">
      <h3 class="title">
        <i class="fas fa-calendar-alt icon"></i>
        Gestion des Rendez-vous
      </h3>
      <div class="badge">
        {{ appointments.length }} RDV aujourd'hui
      </div>
    </div>

    <div class="table-container">
      <table class="appointment-table">
        <thead>
          <tr>
            <th class="time-col">Heure</th>
            <th class="patient-col">Patient</th>
            <th class="type-col">Type</th>
            <th class="status-col">Statut</th>
            <th class="actions-col">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="rdv in appointments" :key="rdv.id" class="appointment-row">
            <td class="time-cell">
              <i class="far fa-clock time-icon"></i>
              {{ rdv.time }}
            </td>
            <td class="patient-cell">
              <div class="patient-info">
                <i class="fas fa-user patient-icon"></i>
                <span>{{ rdv.patient }}</span>
              </div>
            </td>
            <td class="type-cell">
              <span class="appointment-type">{{ rdv.type }}</span>
            </td>
            <td class="status-cell">
              <span :class="['status-badge', statusClass(rdv.status)]">
                {{ getStatusLabel(rdv.status) }}
              </span>
            </td>
            <td class="actions-cell">
              <button 
                class="action-btn cancel-btn" 
                @click="cancel(rdv.id)"
                :disabled="rdv.status === 'annulé'"
              >
                <i class="fas fa-times"></i>
                Annuler
              </button>
              <button 
                class="action-btn confirm-btn" 
                @click="confirm(rdv.id)"
                :disabled="rdv.status === 'confirmé'"
              >
                <i class="fas fa-check"></i>
                Confirmer
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
/* Importations */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* Variables */
:root {
  --primary: #3b82f6;
  --primary-light: #93c5fd;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark: #1e293b;
  --light: #f8fafc;
  --gray: #64748b;
  --border: #e2e8f0;
}

/* Styles de base */
.appointment-manager {
  font-family: 'Poppins', sans-serif;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.icon {
  color: var(--primary);
}

.badge {
  background: var(--primary-light);
  color: var(--primary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Table */
.table-container {
  overflow-x: auto;
}

.appointment-table {
  width: 100%;
  border-collapse: collapse;
}

.appointment-table th {
  text-align: left;
  padding: 1rem;
  background: var(--light);
  color: var(--gray);
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
}

.appointment-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border);
}

.appointment-row:hover {
  background: rgba(59, 130, 246, 0.03);
}

/* Colonnes spécifiques */
.time-cell {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-icon {
  color: var(--gray);
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.patient-icon {
  color: var(--primary);
}

/* Badges de statut */
.status-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-planned {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-confirmed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.status-completed {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary);
}

.status-default {
  background: rgba(100, 116, 139, 0.1);
  color: var(--gray);
}

/* Boutons d'action */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.cancel-btn:hover:not(:disabled) {
  background: var(--danger);
  color: white;
}

.confirm-btn {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  margin-left: 0.5rem;
}

.confirm-btn:hover:not(:disabled) {
  background: var(--success);
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .appointment-table td, 
  .appointment-table th {
    padding: 0.75rem;
  }
  
  .action-btn {
    padding: 0.5rem;
    font-size: 0;
  }
  
  .action-btn i {
    font-size: 0.9rem;
  }
}
</style>