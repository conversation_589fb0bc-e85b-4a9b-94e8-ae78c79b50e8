<script setup>
import { computed } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'

const store = useAppointmentStore()
const appointments = computed(() => {
  return store.todayAppointments.map(apt => ({
    ...apt,
    time: new Date(apt.date_rendez_vous || apt.date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    }),
    patient: apt.patient_nom || apt.patient || 'Patient non défini',
    status: apt.statut || apt.status || 'planifie'
  }))
})

function cancel(id) {
  console.log('❌ Demande d\'annulation du rendez-vous:', id)

  // Demander confirmation avant annulation
  if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
    store.cancelAppointment(id)
    showNotification('Rendez-vous annulé avec succès', 'error')
  }
}

function confirm(id) {
  console.log('🔄 Confirmation du rendez-vous:', id)
  store.confirmAppointment(id)
  showNotification('Rendez-vous confirmé avec succès', 'success')
}

function viewDetails(appointment) {
  console.log('📋 Affichage des détails du rendez-vous:', appointment)
  // Créer une modal ou rediriger vers une page de détails
  showAppointmentDetails(appointment)
}

function showAppointmentDetails(appointment) {
  // Créer une notification avec les détails
  const details = `
    Patient: ${appointment.patient}
    Heure: ${appointment.time}
    Type: ${appointment.type || 'Consultation'}
    Statut: ${getStatusLabel(appointment.status)}
  `

  // Pour l'instant, on affiche dans une alerte (à remplacer par une vraie modal)
  alert(`Détails du rendez-vous:\n\n${details}`)
}

function showNotification(message, type = 'info') {
  // Créer une notification temporaire
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
    ${message}
  `

  // Styles inline pour la notification
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6',
    color: 'white',
    padding: '1rem 1.5rem',
    borderRadius: '12px',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
    zIndex: '9999',
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    fontSize: '0.9rem',
    fontWeight: '500',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease',
    maxWidth: '400px'
  })

  document.body.appendChild(notification)

  // Animation d'entrée
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // Suppression automatique
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 300)
  }, 4000)
}

function statusClass(status) {
  return {
    'planifie': 'status-planned',
    'planifié': 'status-planned',
    'confirme': 'status-confirmed',
    'confirmé': 'status-confirmed',
    'annule': 'status-cancelled',
    'annulé': 'status-cancelled',
    'termine': 'status-completed',
    'terminé': 'status-completed'
  }[status] || 'status-default'
}

function getStatusLabel(status) {
  return {
    'planifie': 'Planifié',
    'confirme': 'Confirmé',
    'annule': 'Annulé',
    'termine': 'Terminé'
  }[status] || status
}

function getStatusIcon(status) {
  return {
    'planifie': 'fas fa-clock',
    'confirme': 'fas fa-check-circle',
    'annule': 'fas fa-times-circle',
    'termine': 'fas fa-check-double'
  }[status] || 'fas fa-question-circle'
}

const confirmedCount = computed(() => {
  return appointments.value.filter(apt =>
    apt.status === 'confirmé' || apt.status === 'confirme'
  ).length
})
</script>

<template>
  <div class="appointment-manager">
    <!-- Header avec gradient -->
    <div class="header">
      <div class="header-content">
        <div class="title-section">
          <h3 class="title">
            <i class="fas fa-calendar-check"></i>
            Rendez-vous d'Aujourd'hui
          </h3>
          <p class="subtitle">Gérez vos consultations du jour</p>
        </div>
        <div class="stats-section">
          <div class="stat-badge total">
            <i class="fas fa-calendar-day"></i>
            <span>{{ appointments.length }} RDV</span>
          </div>
          <div class="stat-badge confirmed">
            <i class="fas fa-check-circle"></i>
            <span>{{ confirmedCount }} confirmés</span>
          </div>
        </div>
      </div>
    </div>

    <!-- État vide -->
    <div v-if="appointments.length === 0" class="empty-state">
      <i class="fas fa-calendar-times"></i>
      <h3>Aucun rendez-vous aujourd'hui</h3>
      <p>Profitez de cette journée plus calme !</p>
    </div>

    <!-- Grille de cartes RDV -->
    <div v-else class="appointments-grid">
      <div
        v-for="rdv in appointments"
        :key="rdv.id"
        :class="['appointment-card', statusClass(rdv.status)]"
      >
        <!-- Barre de statut colorée -->
        <div class="status-bar"></div>

        <!-- Header de la carte -->
        <div class="card-header">
          <div class="time-section">
            <div class="time-display">
              <i class="fas fa-clock"></i>
              <span class="time">{{ rdv.time }}</span>
            </div>
            <div class="duration">30 min</div>
          </div>
          <div class="status-section">
            <span :class="['status-badge', statusClass(rdv.status)]">
              <i :class="getStatusIcon(rdv.status)"></i>
              {{ getStatusLabel(rdv.status) }}
            </span>
          </div>
        </div>

        <!-- Contenu de la carte -->
        <div class="card-content">
          <div class="patient-section">
            <div class="patient-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="patient-info">
              <h4 class="patient-name">{{ rdv.patient }}</h4>
              <p class="appointment-type">{{ rdv.type || 'Consultation générale' }}</p>
            </div>
          </div>

          <div class="appointment-details">
            <div class="detail-item">
              <i class="fas fa-stethoscope"></i>
              <span>{{ rdv.type || 'Consultation' }}</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>Cabinet médical</span>
            </div>
          </div>
        </div>

        <!-- Actions de la carte -->
        <div class="card-actions">
          <button
            :class="['action-btn', 'cancel-btn']"
            @click="cancel(rdv.id)"
            :disabled="rdv.status === 'annulé' || rdv.status === 'terminé'"
          >
            <i class="fas fa-times"></i>
            <span>Annuler</span>
          </button>
          <button
            :class="['action-btn', 'confirm-btn']"
            @click="confirm(rdv.id)"
            :disabled="rdv.status === 'confirmé' || rdv.status === 'terminé'"
          >
            <i class="fas fa-check"></i>
            <span>Confirmer</span>
          </button>
          <button
            class="action-btn details-btn"
            @click="viewDetails(rdv)"
          >
            <i class="fas fa-info-circle"></i>
            <span>Détails</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Importations */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Variables - Couleurs de l'application */
:root {
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #93c5fd;
  --secondary: #10b981;
  --secondary-dark: #059669;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark: #1e293b;
  --gray: #64748b;
  --gray-light: #f8fafc;
  --border: #e2e8f0;
  --white: #ffffff;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Container principal */
.appointment-manager {
  font-family: 'Inter', sans-serif;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-bottom: 2rem;
}

/* Header avec gradient */
.header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--white);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.title-section {
  flex: 1;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--white);
}

.title i {
  font-size: 1.5rem;
  opacity: 0.9;
}

.subtitle {
  margin: 0;
  font-size: 1rem;
  opacity: 0.8;
  font-weight: 400;
}

.stats-section {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--white);
  transition: all 0.3s ease;
}

.stat-badge:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.stat-badge i {
  font-size: 1rem;
  opacity: 0.8;
}

.stat-badge.total {
  border-left: 3px solid #fbbf24;
}

.stat-badge.confirmed {
  border-left: 3px solid var(--success);
}

/* État vide */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--gray);
}

.empty-state i {
  font-size: 4rem;
  color: var(--primary-light);
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: var(--dark);
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.7;
}

/* Grille de cartes */
.appointments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

/* Cartes de rendez-vous */
.appointment-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid var(--border);
}

.appointment-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* Barre de statut colorée */
.status-bar {
  height: 4px;
  background: var(--primary);
  transition: all 0.3s ease;
}

.appointment-card.status-planned .status-bar {
  background: linear-gradient(90deg, var(--warning), #f59e0b);
}

.appointment-card.status-confirmed .status-bar {
  background: linear-gradient(90deg, var(--success), var(--secondary-dark));
}

.appointment-card.status-cancelled .status-bar {
  background: linear-gradient(90deg, var(--danger), #dc2626);
}

.appointment-card.status-completed .status-bar {
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
}

/* Header de la carte */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  background: var(--gray-light);
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--dark);
}

.time-display i {
  color: var(--primary);
  font-size: 1rem;
}

.duration {
  font-size: 0.8rem;
  color: var(--gray);
  font-weight: 500;
}

.status-section {
  display: flex;
  align-items: center;
}

/* Badges de statut */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
}

.status-badge i {
  font-size: 0.9rem;
}

.status-planned {
  background: rgba(245, 158, 11, 0.15);
  color: var(--warning);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.status-confirmed {
  background: rgba(16, 185, 129, 0.15);
  color: var(--success);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.15);
  color: var(--danger);
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.status-completed {
  background: rgba(59, 130, 246, 0.15);
  color: var(--primary);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.status-default {
  background: rgba(100, 116, 139, 0.15);
  color: var(--gray);
  border-color: rgba(100, 116, 139, 0.3);
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.2);
}

/* Contenu de la carte */
.card-content {
  padding: 1.5rem;
}

.patient-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.patient-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.patient-info {
  flex: 1;
}

.patient-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark);
}

.appointment-type {
  margin: 0;
  font-size: 0.9rem;
  color: var(--gray);
  font-weight: 500;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: var(--gray);
}

.detail-item i {
  width: 16px;
  color: var(--primary);
  flex-shrink: 0;
}

/* Actions de la carte */
.card-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1px solid var(--border);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

.action-btn:not(:disabled):hover {
  transform: translateY(-2px);
}

.cancel-btn {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  color: var(--danger);
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
}

.cancel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--danger), #dc2626);
  color: var(--white);
  border-color: var(--danger);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.confirm-btn {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  color: var(--success);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success), var(--secondary-dark));
  color: var(--white);
  border-color: var(--success);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.details-btn {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
  color: var(--primary);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.details-btn:hover {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--white);
  border-color: var(--primary);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .stats-section {
    width: 100%;
    justify-content: space-between;
  }

  .stat-badge {
    flex: 1;
    justify-content: center;
    min-width: 0;
  }

  .appointments-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }

  .card-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    flex: none;
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1.5rem;
  }

  .card-header {
    padding: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .card-actions {
    padding: 1rem;
  }

  .patient-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    text-align: center;
  }

  .patient-avatar {
    align-self: center;
  }
}
</style>