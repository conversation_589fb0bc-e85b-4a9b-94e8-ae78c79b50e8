<script setup>
import { computed } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'

const store = useAppointmentStore()
const appointments = computed(() => {
  return store.todayAppointments.map(apt => ({
    ...apt,
    time: new Date(apt.date_rendez_vous || apt.date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    }),
    patient: apt.patient_nom || apt.patient || 'Patient non défini',
    status: apt.statut || apt.status || 'planifie'
  }))
})

async function cancel(id) {
  console.log('❌ Demande d\'annulation du rendez-vous:', id)

  // Demander confirmation avant annulation
  if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
    try {
      const result = await store.cancelAppointment(id)
      if (result.success) {
        showNotification('Rendez-vous annulé avec succès', 'success')
      } else {
        showNotification('Erreur lors de l\'annulation: ' + result.error, 'error')
      }
    } catch (error) {
      console.error('Erreur lors de l\'annulation:', error)
      showNotification('Erreur lors de l\'annulation du rendez-vous', 'error')
    }
  }
}

async function confirm(id) {
  console.log('🔄 Confirmation du rendez-vous:', id)
  try {
    const result = await store.confirmAppointment(id)
    if (result.success) {
      showNotification('Rendez-vous confirmé avec succès', 'success')
    } else {
      showNotification('Erreur lors de la confirmation: ' + result.error, 'error')
    }
  } catch (error) {
    console.error('Erreur lors de la confirmation:', error)
    showNotification('Erreur lors de la confirmation du rendez-vous', 'error')
  }
}

function viewDetails(appointment) {
  console.log('📋 Affichage des détails du rendez-vous:', appointment)
  // Créer une modal ou rediriger vers une page de détails
  showAppointmentDetails(appointment)
}

function showAppointmentDetails(appointment) {
  // Créer une notification avec les détails
  const details = `
    Patient: ${appointment.patient}
    Heure: ${appointment.time}
    Type: ${appointment.type || 'Consultation'}
    Statut: ${getStatusLabel(appointment.status)}
  `

  // Pour l'instant, on affiche dans une alerte (à remplacer par une vraie modal)
  alert(`Détails du rendez-vous:\n\n${details}`)
}

function showNotification(message, type = 'info') {
  // Pour l'instant, utiliser une alerte simple
  // TODO: Remplacer par un système de notifications plus sophistiqué
  if (type === 'success') {
    alert('✅ ' + message)
  } else if (type === 'error') {
    alert('❌ ' + message)
  } else {
    alert('ℹ️ ' + message)
  }
}

function showNotification(message, type = 'info') {
  // Créer une notification temporaire
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
    ${message}
  `

  // Styles inline pour la notification - 3 couleurs max
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'success' ? '#3b82f6' : type === 'error' ? '#64748b' : '#3b82f6',
    color: 'white',
    padding: '1rem 1.5rem',
    borderRadius: '12px',
    boxShadow: '0 8px 25px rgba(59, 130, 246, 0.15)',
    zIndex: '9999',
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    fontSize: '0.9rem',
    fontWeight: '500',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease',
    maxWidth: '400px'
  })

  document.body.appendChild(notification)

  // Animation d'entrée
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // Suppression automatique
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 300)
  }, 4000)
}

function statusClass(status) {
  return {
    'planifie': 'status-planned',
    'planifié': 'status-planned',
    'confirme': 'status-confirmed',
    'confirmé': 'status-confirmed',
    'annule': 'status-cancelled',
    'annulé': 'status-cancelled',
    'termine': 'status-completed',
    'terminé': 'status-completed'
  }[status] || 'status-default'
}

function getStatusLabel(status) {
  return {
    'planifie': 'Planifié',
    'confirme': 'Confirmé',
    'annule': 'Annulé',
    'termine': 'Terminé'
  }[status] || status
}

function getStatusIcon(status) {
  return {
    'planifie': 'fas fa-clock',
    'confirme': 'fas fa-check-circle',
    'annule': 'fas fa-times-circle',
    'termine': 'fas fa-check-double'
  }[status] || 'fas fa-question-circle'
}

const confirmedCount = computed(() => {
  return appointments.value.filter(apt =>
    apt.status === 'confirmé' || apt.status === 'confirme'
  ).length
})
</script>

<template>
  <div class="appointment-manager">
    <!-- Header avec gradient -->
    <div class="header">
      <div class="header-content">
        <div class="title-section">
          <h3 class="title">
            <i class="fas fa-calendar-check"></i>
            Rendez-vous d'Aujourd'hui
          </h3>
          <p class="subtitle">Gérez vos consultations du jour</p>
        </div>
        <div class="stats-section">
          <div class="stat-badge total">
            <i class="fas fa-calendar-day"></i>
            <span>{{ appointments.length }} RDV</span>
          </div>
          <div class="stat-badge confirmed">
            <i class="fas fa-check-circle"></i>
            <span>{{ confirmedCount }} confirmés</span>
          </div>
        </div>
      </div>
    </div>

    <!-- État vide -->
    <div v-if="appointments.length === 0" class="empty-state">
      <i class="fas fa-calendar-times"></i>
      <h3>Aucun rendez-vous aujourd'hui</h3>
      <p>Profitez de cette journée plus calme !</p>
    </div>

    <!-- Grille de cartes RDV modernes -->
    <div v-else class="appointments-grid">
      <div
        v-for="(rdv, index) in appointments"
        :key="rdv.id"
        :class="['appointment-card', statusClass(rdv.status)]"
        :style="{ animationDelay: `${index * 100}ms` }"
        :data-status="rdv.status"
      >
        <!-- Barre de statut colorée avec animation -->
        <div class="status-bar">
          <div class="status-progress"></div>
        </div>

        <!-- Orbe décoratif animé -->
        <div class="card-decoration">
          <div class="decoration-orb"></div>
          <div class="decoration-pulse"></div>
        </div>

        <!-- Header de la carte avec icône de statut -->
        <div class="card-header">
          <div class="time-section">
            <div class="time-display">
              <div class="time-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="time-info">
                <span class="time">{{ rdv.time }}</span>
                <span class="duration">30 minutes</span>
              </div>
            </div>
          </div>
          <div class="status-section">
            <div class="status-indicator">
              <i :class="getStatusIcon(rdv.status)"></i>
            </div>
            <span :class="['status-badge', statusClass(rdv.status)]">
              {{ getStatusLabel(rdv.status) }}
            </span>
          </div>
        </div>

        <!-- Contenu de la carte avec animations -->
        <div class="card-content">
          <div class="patient-section">
            <div class="patient-avatar">
              <div class="avatar-ring"></div>
              <i class="fas fa-user"></i>
            </div>
            <div class="patient-info">
              <h4 class="patient-name">{{ rdv.patient }}</h4>
              <p class="appointment-type">{{ rdv.type || 'Consultation générale' }}</p>
            </div>
            <div class="priority-indicator">
              <div class="priority-dot"></div>
            </div>
          </div>

          <div class="appointment-details">
            <div class="detail-item">
              <div class="detail-icon">
                <i class="fas fa-stethoscope"></i>
              </div>
              <span>{{ rdv.type || 'Consultation' }}</span>
            </div>
            <div class="detail-item">
              <div class="detail-icon">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <span>Cabinet médical</span>
            </div>
            <div class="detail-item">
              <div class="detail-icon">
                <i class="fas fa-phone"></i>
              </div>
              <span>Contact disponible</span>
            </div>
          </div>
        </div>

        <!-- Actions de la carte avec effets modernes -->
        <div class="card-actions">
          <button
            :class="['action-btn', 'cancel-btn']"
            @click="cancel(rdv.id)"
            :disabled="rdv.status === 'annulé' || rdv.status === 'terminé'"
          >
            <div class="btn-background"></div>
            <i class="fas fa-times"></i>
            <span>Annuler</span>
          </button>
          <button
            :class="['action-btn', 'confirm-btn']"
            @click="confirm(rdv.id)"
            :disabled="rdv.status === 'confirmé' || rdv.status === 'terminé'"
          >
            <div class="btn-background"></div>
            <i class="fas fa-check"></i>
            <span>Confirmer</span>
          </button>
          <button
            class="action-btn details-btn"
            @click="viewDetails(rdv)"
          >
            <div class="btn-background"></div>
            <i class="fas fa-info-circle"></i>
            <span>Détails</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Importations */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Variables - 3 couleurs maximum pour l'harmonie */
:root {
  /* Couleur principale - Bleu */
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #93c5fd;
  --primary-lighter: #dbeafe;

  /* Couleur secondaire - Gris pour les neutres */
  --gray: #64748b;
  --gray-light: #f8fafc;
  --gray-dark: #1e293b;

  /* Couleur d'accent - Blanc */
  --white: #ffffff;
  --border: #e2e8f0;

  /* Ombres */
  --shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(59, 130, 246, 0.1);
}

/* Container principal */
.appointment-manager {
  font-family: 'Inter', sans-serif;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-bottom: 2rem;
}

/* Header avec gradient bleu */
.header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.title-section {
  flex: 1;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--white);
}

.title i {
  font-size: 1.5rem;
  opacity: 0.9;
}

.subtitle {
  margin: 0;
  font-size: 1rem;
  opacity: 0.8;
  font-weight: 400;
}

.stats-section {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--white);
  transition: all 0.3s ease;
}

.stat-badge:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.stat-badge i {
  font-size: 1rem;
  opacity: 0.8;
}

.stat-badge.total {
  border-left: 3px solid var(--primary-light);
}

.stat-badge.confirmed {
  border-left: 3px solid var(--primary);
}

/* État vide */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--gray);
}

.empty-state i {
  font-size: 4rem;
  color: var(--primary-light);
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: var(--dark);
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.7;
}

/* Grille de cartes */
.appointments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

/* Cartes de rendez-vous modernes */
.appointment-card {
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.08);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 1px solid rgba(59, 130, 246, 0.1);
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.appointment-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
}

/* Animation d'entrée */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Barre de statut colorée avec animation */
.status-bar {
  height: 6px;
  background: var(--primary);
  position: relative;
  overflow: hidden;
}

.status-progress {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.appointment-card.status-planned .status-bar {
  background: linear-gradient(90deg, #93c5fd, #3b82f6);
}

.appointment-card.status-confirmed .status-bar {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.appointment-card.status-cancelled .status-bar {
  background: linear-gradient(90deg, #64748b, #1e293b);
}

.appointment-card.status-completed .status-bar {
  background: linear-gradient(90deg, #2563eb, #1e293b);
}

/* Orbe décoratif animé */
.card-decoration {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 80px;
  height: 80px;
  pointer-events: none;
  z-index: 1;
}

.decoration-orb {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.05;
  transition: all 0.4s ease;
}

.decoration-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.1; }
}

.appointment-card.status-planned .decoration-orb {
  background: radial-gradient(circle, #93c5fd, transparent);
}

.appointment-card.status-planned .decoration-pulse {
  background: #93c5fd;
}

.appointment-card.status-confirmed .decoration-orb {
  background: radial-gradient(circle, #3b82f6, transparent);
}

.appointment-card.status-confirmed .decoration-pulse {
  background: #3b82f6;
}

.appointment-card.status-cancelled .decoration-orb {
  background: radial-gradient(circle, #64748b, transparent);
}

.appointment-card.status-cancelled .decoration-pulse {
  background: #64748b;
}

.appointment-card.status-completed .decoration-orb {
  background: radial-gradient(circle, #2563eb, transparent);
}

.appointment-card.status-completed .decoration-pulse {
  background: #2563eb;
}

.appointment-card:hover .decoration-orb {
  opacity: 0.1;
  transform: scale(1.2) rotate(10deg);
}

.appointment-card:hover .decoration-pulse {
  animation-duration: 1s;
}

/* Header de la carte moderne */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 2rem 1rem 2rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03), rgba(147, 197, 253, 0.03));
  position: relative;
  z-index: 2;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.time-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.appointment-card:hover .time-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.time {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--gray-dark);
  line-height: 1;
}

.duration {
  font-size: 0.85rem;
  color: var(--gray);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.status-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
  transition: all 0.3s ease;
}

.appointment-card.status-planned .status-indicator {
  background: linear-gradient(135deg, #93c5fd, #3b82f6);
  box-shadow: 0 3px 10px rgba(147, 197, 253, 0.3);
}

.appointment-card.status-confirmed .status-indicator {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
}

.appointment-card.status-cancelled .status-indicator {
  background: linear-gradient(135deg, #64748b, #1e293b);
  box-shadow: 0 3px 10px rgba(100, 116, 139, 0.3);
}

.appointment-card.status-completed .status-indicator {
  background: linear-gradient(135deg, #2563eb, #1e293b);
  box-shadow: 0 3px 10px rgba(37, 99, 235, 0.3);
}

.appointment-card:hover .status-indicator {
  transform: scale(1.1);
}

/* Badges de statut */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
}

.status-badge i {
  font-size: 0.9rem;
}

.status-planned {
  background: rgba(147, 197, 253, 0.15);
  color: var(--primary-light);
  border-color: rgba(147, 197, 253, 0.3);
  box-shadow: 0 2px 8px rgba(147, 197, 253, 0.2);
}

.status-confirmed {
  background: rgba(59, 130, 246, 0.15);
  color: var(--primary);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.status-cancelled {
  background: rgba(100, 116, 139, 0.15);
  color: var(--gray);
  border-color: rgba(100, 116, 139, 0.3);
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.2);
}

.status-completed {
  background: rgba(37, 99, 235, 0.15);
  color: var(--primary-dark);
  border-color: rgba(37, 99, 235, 0.3);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}

.status-default {
  background: rgba(100, 116, 139, 0.15);
  color: var(--gray);
  border-color: rgba(100, 116, 139, 0.3);
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.2);
}

/* Contenu de la carte moderne */
.card-content {
  padding: 0 2rem 1.5rem 2rem;
  position: relative;
  z-index: 2;
}

.patient-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.patient-avatar {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.75rem;
  flex-shrink: 0;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  position: relative;
  transition: all 0.3s ease;
}

.avatar-ring {
  position: absolute;
  inset: -3px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #93c5fd, #3b82f6);
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.appointment-card:hover .avatar-ring {
  opacity: 1;
}

.appointment-card:hover .patient-avatar {
  transform: scale(1.05);
}

.patient-info {
  flex: 1;
}

.patient-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--gray-dark);
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.appointment-type {
  margin: 0;
  font-size: 1rem;
  color: var(--gray);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.priority-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #34d399);
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.appointment-details {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 197, 253, 0.05));
  border-radius: 12px;
  font-size: 0.9rem;
  color: var(--gray);
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateX(4px);
}

.detail-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

/* Actions de la carte modernes */
.card-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem 2rem 2rem 2rem;
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.03), rgba(59, 130, 246, 0.03));
  border-top: 1px solid rgba(59, 130, 246, 0.08);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 700;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn-background {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover .btn-background {
  left: 100%;
}

.action-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none !important;
}

.action-btn:disabled .btn-background {
  display: none;
}

.action-btn:not(:disabled):hover {
  transform: translateY(-3px) scale(1.02);
}

.action-btn i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.action-btn:hover i {
  transform: scale(1.1);
}

.cancel-btn {
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.1), rgba(71, 85, 105, 0.1));
  color: var(--gray);
  border-color: rgba(100, 116, 139, 0.3);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.15);
}

.cancel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--gray), var(--gray-dark));
  color: var(--white);
  border-color: var(--gray);
  box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3);
}

.confirm-btn {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
  color: var(--primary);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--white);
  border-color: var(--primary);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.details-btn {
  background: linear-gradient(135deg, rgba(147, 197, 253, 0.1), rgba(59, 130, 246, 0.1));
  color: var(--primary-light);
  border-color: rgba(147, 197, 253, 0.3);
  box-shadow: 0 4px 12px rgba(147, 197, 253, 0.15);
}

.details-btn:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: var(--white);
  border-color: var(--primary-light);
  box-shadow: 0 8px 25px rgba(147, 197, 253, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .stats-section {
    width: 100%;
    justify-content: space-between;
  }

  .stat-badge {
    flex: 1;
    justify-content: center;
    min-width: 0;
  }

  .appointments-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }

  .card-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    flex: none;
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1.5rem;
  }

  .card-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .time-display {
    gap: 0.75rem;
  }

  .time-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .time {
    font-size: 1.25rem;
  }

  .status-section {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
  }

  .status-indicator {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .card-content {
    padding: 0 1.5rem 1rem 1.5rem;
  }

  .patient-section {
    gap: 1rem;
  }

  .patient-avatar {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .patient-name {
    font-size: 1.1rem;
  }

  .appointment-details {
    gap: 0.75rem;
  }

  .detail-item {
    padding: 0.5rem 0.75rem;
    gap: 0.75rem;
  }

  .detail-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .card-actions {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    padding: 0.875rem 1rem;
    font-size: 0.8rem;
  }

  .card-decoration {
    width: 60px;
    height: 60px;
    top: 0.75rem;
    right: 0.75rem;
  }

  .decoration-pulse {
    width: 15px;
    height: 15px;
  }
}
</style>