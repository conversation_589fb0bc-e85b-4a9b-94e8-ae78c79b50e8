<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>🕒 Démonstration des Types de Créneaux</h1>
      <p>Voici les fonctionnalités implémentées avec vos données réelles</p>
    </div>

    <!-- Section 1: Types disponibles -->
    <div class="demo-section">
      <h2>1. 📋 Types de Créneaux Configurés</h2>
      
      <div class="types-showcase">
        <div 
          v-for="type in slotTypes" 
          :key="type.id"
          class="type-showcase-card"
        >
          <div class="type-header">
            <div 
              class="type-color" 
              :style="{ backgroundColor: type.couleur }"
            ></div>
            <h3>{{ type.nom }}</h3>
            <span class="type-duration">{{ type.duree }} min</span>
          </div>
          
          <div class="type-details">
            <p>{{ type.description }}</p>
            <div class="type-price">{{ formatPriceXOF(type.prix_base) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section 2: Créneaux par médecin -->
    <div class="demo-section">
      <h2>2. 👨‍⚕️ Créneaux par Médecin</h2>
      
      <div class="doctor-selector">
        <label>Sélectionner un médecin :</label>
        <select v-model="selectedDoctorId" @change="loadDoctorSlots">
          <option value="">-- Choisir un médecin --</option>
          <option value="1">Dr. kacou junior (Cardiologue)</option>
          <option value="2">Dr. Ehounou julien (Cardiologue)</option>
          <option value="3">Dr. Ehounou baptiste (Dermatologue)</option>
        </select>
      </div>
      
      <div v-if="doctorSlots.length > 0" class="slots-display">
        <h3>Créneaux disponibles :</h3>
        
        <div class="slots-grid">
          <div 
            v-for="slot in doctorSlots.slice(0, 6)" 
            :key="slot.id"
            class="slot-card"
            :class="slot.statut"
          >
            <div class="slot-time">
              {{ formatDate(slot.date_heure_suggeree) }}
              <br>
              {{ formatTime(slot.date_heure_suggeree) }}
            </div>
            
            <div class="slot-type">
              <span 
                class="type-badge" 
                :style="{ backgroundColor: getTypeColor(slot.duree) }"
              >
                {{ getTypeName(slot.duree) }}
              </span>
              <span class="slot-duration">{{ slot.duree }} min</span>
            </div>
            
            <div class="slot-info">
              <p class="slot-reason">{{ slot.raison }}</p>
              <div class="slot-price">{{ getTypePriceXOF(slot.duree) }}</div>
            </div>
            
            <div class="slot-status">
              <span :class="['status-badge', slot.statut]">
                {{ slot.statut === 'disponible' ? 'Disponible' : 'Réservé' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section 3: Sélecteur patient -->
    <div class="demo-section">
      <h2>3. 👤 Sélection Patient (Simulation)</h2>
      
      <div class="patient-simulation">
        <h3>Choisissez votre type de consultation :</h3>
        
        <div class="patient-types-grid">
          <div 
            v-for="type in slotTypes" 
            :key="type.id"
            :class="['patient-type-card', { selected: selectedPatientType?.id === type.id }]"
            @click="selectPatientType(type)"
          >
            <div class="patient-type-header">
              <div 
                class="patient-type-color" 
                :style="{ backgroundColor: type.couleur }"
              ></div>
              <h4>{{ type.nom }}</h4>
              <div class="patient-type-duration">{{ type.duree }} min</div>
            </div>
            
            <div class="patient-type-content">
              <p>{{ type.description }}</p>
              <div class="patient-type-price">{{ formatPriceXOF(type.prix_base) }}</div>
            </div>
            
            <div class="patient-type-examples">
              <strong>Idéal pour :</strong>
              <ul>
                <li v-if="type.nom === 'Court'">Renouvellement d'ordonnance</li>
                <li v-if="type.nom === 'Court'">Résultats d'analyses</li>
                <li v-if="type.nom === 'Standard'">Consultation générale</li>
                <li v-if="type.nom === 'Standard'">Symptômes nouveaux</li>
                <li v-if="type.nom === 'Long'">Premier rendez-vous</li>
                <li v-if="type.nom === 'Long'">Bilan complet</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div v-if="selectedPatientType" class="patient-selection-summary">
          <h4>✅ Vous avez sélectionné :</h4>
          <p><strong>{{ selectedPatientType.nom }}</strong> ({{ selectedPatientType.duree }} minutes - {{ formatPriceXOF(selectedPatientType.prix_base) }})</p>
          <p>{{ selectedPatientType.description }}</p>
        </div>
      </div>
    </div>

    <!-- Section 4: Statistiques -->
    <div class="demo-section">
      <h2>4. 📊 Statistiques d'Utilisation</h2>
      
      <div v-if="stats.length > 0" class="stats-grid">
        <div 
          v-for="stat in stats" 
          :key="stat.id"
          class="stat-card"
        >
          <div class="stat-header">
            <h4>{{ stat.nom }}</h4>
            <div 
              class="stat-color" 
              :style="{ backgroundColor: stat.couleur }"
            ></div>
          </div>
          
          <div class="stat-content">
            <div class="stat-item">
              <span class="stat-label">Créneaux créés :</span>
              <span class="stat-value">{{ stat.nb_creneaux_utilises }}</span>
            </div>
            
            <div class="stat-item">
              <span class="stat-label">Créneaux acceptés :</span>
              <span class="stat-value">{{ stat.nb_creneaux_acceptes }}</span>
            </div>
            
            <div class="stat-item">
              <span class="stat-label">Taux d'acceptation :</span>
              <span class="stat-value">{{ getAcceptanceRate(stat) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section 5: Instructions -->
    <div class="demo-section">
      <h2>5. 🚀 Comment utiliser dans l'application</h2>
      
      <div class="instructions-grid">
        <div class="instruction-card">
          <div class="instruction-icon">👨‍⚕️</div>
          <h3>Pour les Médecins</h3>
          <ol>
            <li>Aller dans <strong>DoctorView</strong></li>
            <li>Cliquer sur l'onglet <strong>"Types de Créneaux"</strong></li>
            <li>Gérer les types (créer, modifier, activer/désactiver)</li>
            <li>Créer des créneaux avec types spécifiques</li>
            <li>Voir les statistiques d'utilisation</li>
          </ol>
        </div>
        
        <div class="instruction-card">
          <div class="instruction-icon">👤</div>
          <h3>Pour les Patients</h3>
          <ol>
            <li>Aller dans <strong>PatientView</strong></li>
            <li>Section <strong>"Prendre un rendez-vous"</strong></li>
            <li>Sélectionner un médecin</li>
            <li>Choisir le type de consultation</li>
            <li>Voir les créneaux filtrés par type</li>
            <li>Réserver avec tarif automatique</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { slotTypeService } from '@/services/slotTypeService'
import api from '@/services/api'

export default {
  name: 'SlotTypesDemo',
  
  setup() {
    const slotTypes = ref([])
    const doctorSlots = ref([])
    const selectedDoctorId = ref('')
    const selectedPatientType = ref(null)
    const stats = ref([])
    
    const loadSlotTypes = async () => {
      try {
        const response = await slotTypeService.getActive()
        slotTypes.value = response.data
      } catch (error) {
        console.error('Erreur chargement types:', error)
      }
    }
    
    const loadStats = async () => {
      try {
        const response = await slotTypeService.getStats()
        stats.value = response.data
      } catch (error) {
        console.error('Erreur chargement stats:', error)
      }
    }
    
    const loadDoctorSlots = async () => {
      if (!selectedDoctorId.value) {
        doctorSlots.value = []
        return
      }
      
      try {
        const response = await api.get(`/suggested-slots?medecin_id=${selectedDoctorId.value}`)
        doctorSlots.value = response.data.data || []
      } catch (error) {
        console.error('Erreur chargement créneaux:', error)
      }
    }
    
    const selectPatientType = (type) => {
      selectedPatientType.value = type
    }
    
    const formatDate = (dateStr) => {
      return new Date(dateStr).toLocaleDateString('fr-FR')
    }
    
    const formatTime = (dateStr) => {
      return new Date(dateStr).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
    }
    
    const getTypeName = (duration) => {
      if (duration <= 15) return 'Court'
      if (duration <= 30) return 'Standard'
      return 'Long'
    }
    
    const getTypeColor = (duration) => {
      if (duration <= 15) return '#10b981'
      if (duration <= 30) return '#3b82f6'
      return '#f59e0b'
    }
    
    const getTypePriceXOF = (duration) => {
      if (duration <= 15) return '16 375 XOF'
      if (duration <= 30) return '32 750 XOF'
      return '52 400 XOF'
    }

    const formatPriceXOF = (price) => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price) + ' XOF'
    }
    
    const getAcceptanceRate = (stat) => {
      if (stat.nb_creneaux_utilises === 0) return 0
      return Math.round((stat.nb_creneaux_acceptes / stat.nb_creneaux_utilises) * 100)
    }
    
    onMounted(() => {
      loadSlotTypes()
      loadStats()
    })
    
    return {
      slotTypes,
      doctorSlots,
      selectedDoctorId,
      selectedPatientType,
      stats,
      loadDoctorSlots,
      selectPatientType,
      formatDate,
      formatTime,
      getTypeName,
      getTypeColor,
      getTypePriceXOF,
      formatPriceXOF,
      getAcceptanceRate
    }
  }
}
</script>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
}

.demo-header h1 {
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  color: #1f2937;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.types-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.type-showcase-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.type-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.type-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.type-duration {
  background: #f3f4f6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

.type-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #059669;
}

.doctor-selector {
  margin-bottom: 2rem;
}

.doctor-selector select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  margin-left: 1rem;
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.slot-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: white;
}

.slot-card.disponible {
  border-left: 4px solid #10b981;
}

.slot-card.accepte {
  border-left: 4px solid #3b82f6;
  opacity: 0.7;
}

.slot-time {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.type-badge {
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.slot-reason {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.5rem 0;
}

.slot-price {
  font-weight: 600;
  color: #059669;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.disponible {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.accepte {
  background: #dbeafe;
  color: #1e40af;
}

.patient-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.patient-type-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.patient-type-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.patient-type-card.selected {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.patient-type-examples ul {
  margin: 0.5rem 0 0 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.patient-selection-summary {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6b7280;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.instruction-card {
  text-align: center;
  padding: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.instruction-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.instruction-card ol {
  text-align: left;
  margin: 1rem 0;
}

.instruction-card li {
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }
  
  .types-showcase,
  .slots-grid,
  .patient-types-grid,
  .stats-grid,
  .instructions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
