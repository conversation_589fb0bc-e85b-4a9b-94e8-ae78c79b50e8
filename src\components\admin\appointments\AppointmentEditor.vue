<template>

  <ConfirmationModal
    :show="true"
    @close="$emit('close')"
    @confirm="saveAppointment"
    class="appointment-editor-modal"
  >
    <template #title>
      <div class="modal-title">
        <div class="title-icon">
          <i class="fas fa-calendar-plus"></i>
        </div>
        <span class="title-text">
          {{ appointment.id ? 'Modifier Rendez-vous' : 'Nouveau Rendez-vous' }}
        </span>
      </div>
    </template>

    <div class="form-container">
      <!-- Section Patient -->
      <div class="form-section patient-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-user"></i>
          </div>
          <h3 class="section-title">Informations Patient</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-user-circle"></i>
              Patient
            </label>
            <select-input 
              v-model="form.patientId"
              :options="patients"
              option-label="fullName"
              :error="!form.patientId"
              placeholder="Sélectionner un patient"
              class="modern-select"
            />
          </div>
        </div>
      </div>

      <!-- Section Médecin -->
      <div class="form-section doctor-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-user-md"></i>
          </div>
          <h3 class="section-title">Médecin Traitant</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-stethoscope"></i>
              Médecin
            </label>
            <select-input 
              v-model="form.doctorId"
              :options="doctors"
              option-label="fullName"
              option-prefix="Dr. "
              :error="!form.doctorId"
              placeholder="Sélectionner un médecin"
              class="modern-select"
            />
          </div>
        </div>
      </div>

      <!-- Section Date et Heure -->
      <div class="form-section datetime-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-clock"></i>
          </div>
          <h3 class="section-title">Programmation</h3>
        </div>
        
        <div class="section-content">
          <div class="datetime-grid">
            <div class="form-field">
              <label class="field-label">
                <i class="fas fa-calendar"></i>
                Date
              </label>
              <input 
                v-model="form.date"
                type="date"
                class="modern-input"
                :class="{ 'error': !form.date }"
              >
            </div>
            <div class="form-field">
              <label class="field-label">
                <i class="fas fa-clock"></i>
                Heure
              </label>
              <input 
                v-model="form.time"
                type="time"
                class="modern-input"
                :class="{ 'error': !form.time }"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Section Motif -->
      <div class="form-section reason-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-notes-medical"></i>
          </div>
          <h3 class="section-title">Type de Consultation</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-tag"></i>
              Motif
            </label>
            <select-input 
              v-model="form.reason"
              :options="[
                { value: 'consultation', label: 'Consultation générale', icon: 'clipboard-check' },
                { value: 'follow-up', label: 'Suivi médical', icon: 'redo' },
                { value: 'emergency', label: 'Consultation d\'urgence', icon: 'ambulance' },
                { value: 'other', label: 'Autre motif', icon: 'ellipsis-h' }
              ]"
              option-label="label"
              show-icon
              class="modern-select"
            />
          </div>
        </div>
      </div>

      <!-- Section Notes -->
      <div class="form-section notes-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-edit"></i>
          </div>
          <h3 class="section-title">Notes Additionnelles</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-sticky-note"></i>
              Remarques (optionnelles)
            </label>
            <textarea 
              v-model="form.notes"
              rows="4"
              placeholder="Ajoutez des notes ou commentaires particuliers..."
              class="modern-textarea"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Validation des erreurs -->
      <error-display :errors="errorMessages" v-if="hasErrors" class="error-display" />
    </div>

    <!-- Boutons d'action -->
    <template #actions>
      <div class="actions-container">
        <button
          @click="$emit('close')"
          class="btn btn-secondary"
        >
          <i class="fas fa-times"></i>
          Annuler
        </button>
        <button
          @click="saveAppointment"
          :disabled="hasErrors"
          class="btn btn-primary"
        >
          <i class="fas fa-save"></i>
          {{ appointment.id ? 'Modifier' : 'Créer' }}
        </button>
      </div>
    </template>
  </ConfirmationModal>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { usePatientStore } from '@/stores/patientStore'
import { useDoctorStore } from '@/stores/doctorStore'
import ConfirmationModal from '../shared/ConfirmationModal.vue'
import SelectInput from '../shared/SelectInput.vue'
import ErrorDisplay from '../shared/ErrorDisplay.vue'

const props = defineProps({
  appointment: {
    type: Object,
    default: () => ({})
  },
  patient: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'save'])

const patientStore = usePatientStore()
const doctorStore = useDoctorStore()

const form = ref({
  id: props.appointment?.id || null,
  patientId: props.patient?.id || props.appointment?.patientId || '',
  doctorId: props.appointment?.doctorId || '',
  date: props.appointment?.date || '',
  time: props.appointment?.time || '',
  reason: props.appointment?.reason || 'consultation',
  notes: props.appointment?.notes || ''
})

const patients = computed(() => {
  console.log('Patients dans le store:', patientStore.patients)
  return patientStore.patients.map(p => ({
    value: p.id,
    label: `${p.nom} ${p.prenom}`,
    fullName: `${p.nom} ${p.prenom}`
  }))
})

const doctors = computed(() => doctorStore.doctors.filter(d => d.role === 'doctor').map(d => ({
  value: d.id,
  label: `Dr. ${d.nom} ${d.prenom}`,
  fullName: `${d.nom} ${d.prenom}`
})))

const errorMessages = computed(() => {
  const errors = []
  if (!form.value.patientId) errors.push('Sélectionner un patient')
  if (!form.value.doctorId) errors.push('Sélectionner un médecin')
  if (!form.value.date) errors.push('Définir une date')
  if (!form.value.time) errors.push('Définir une heure')
  return errors
})

const hasErrors = computed(() => errorMessages.value.length > 0)

function saveAppointment() {
  if (!hasErrors.value) {
    // Transformer les données pour correspondre à l'API
    const appointmentData = {
      patient_id: form.value.patientId,
      doctor_id: form.value.doctorId,
      date: `${form.value.date} ${form.value.time}:00`,
      type: form.value.reason,
      notes: form.value.notes,
      duration: 30
    }

    console.log('Données du formulaire:', appointmentData)
    emit('save', appointmentData)
  }
}

onMounted(async () => {
  try {
    console.log('Chargement des patients et médecins...')
    await Promise.all([
      patientStore.fetchAll(), // 
      doctorStore.fetchDoctors()
    ])
    console.log('Patients chargés:', patientStore.patients)
    console.log('Médecins chargés:', doctorStore.doctors)
  } catch (error) {
    console.error('Erreur lors du chargement des données:', error)
  }
})

</script>

<style scoped>
.appointment-editor-modal {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.title-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border-radius: 10px;
  font-size: 1.25rem;
}

.title-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.form-container {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.header-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border-radius: 8px;
  font-size: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
  font-size: 0.95rem;
  font-weight: 500;
}

.field-label i {
  color: #6b7280;
}

.modern-input,
.modern-select,
.modern-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.95rem;
  color: #1f2937;
  background: white;
  transition: all 0.2s ease;
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-input.error,
.modern-select.error,
.modern-textarea.error {
  border-color: #ef4444;
}

.modern-input.error:focus,
.modern-select.error:focus,
.modern-textarea.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.datetime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.error-display {
  background: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  color: #991b1b;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
}

.btn i {
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: white;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

/* Animations */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 640px) {
  .appointment-editor-modal {
    margin: 1rem;
  }

  .form-container {
    padding: 1rem;
  }

  .datetime-grid {
    grid-template-columns: 1fr;
  }

  .actions-container {
    flex-direction: column-reverse;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>