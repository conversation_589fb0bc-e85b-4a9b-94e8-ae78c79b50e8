<template>
  <!-- Modal overlay moderne -->
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- Header du modal -->
      <div class="modal-header">
        <div class="header-content">
          <div class="title-section">
            <div class="title-icon">
              <i class="fas fa-calendar-plus"></i>
            </div>
            <div class="title-info">
              <h2 class="modal-title">
                {{ appointment.id ? 'Modifier Rendez-vous' : 'Nouveau Rendez-vous' }}
              </h2>
              <p class="modal-subtitle">
                {{ appointment.id ? 'Modifiez les informations du rendez-vous' : 'Planifiez un nouveau rendez-vous médical' }}
              </p>
            </div>
          </div>
          <button @click="$emit('close')" class="close-button">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Barre de progression -->
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>
      </div>

      <!-- Corps du modal -->
      <div class="modal-body">

    <div class="form-container">
      <!-- Section Patient -->
      <div class="form-section patient-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-user"></i>
          </div>
          <h3 class="section-title">Informations Patient</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-user-circle"></i>
              Patient
            </label>
            <select-input 
              v-model="form.patientId"
              :options="patients"
              option-label="fullName"
              :error="!form.patientId"
              placeholder="Sélectionner un patient"
              class="modern-select"
            />
          </div>
        </div>
      </div>

      <!-- Section Médecin -->
      <div class="form-section doctor-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-user-md"></i>
          </div>
          <h3 class="section-title">Médecin Traitant</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-stethoscope"></i>
              Médecin
            </label>
            <select-input 
              v-model="form.doctorId"
              :options="doctors"
              option-label="fullName"
              option-prefix="Dr. "
              :error="!form.doctorId"
              placeholder="Sélectionner un médecin"
              class="modern-select"
            />
          </div>
        </div>
      </div>

      <!-- Section Date et Heure -->
      <div class="form-section datetime-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-clock"></i>
          </div>
          <h3 class="section-title">Programmation</h3>
        </div>
        
        <div class="section-content">
          <div class="datetime-grid">
            <div class="form-field">
              <label class="field-label">
                <i class="fas fa-calendar"></i>
                Date
              </label>
              <input 
                v-model="form.date"
                type="date"
                class="modern-input"
                :class="{ 'error': !form.date }"
              >
            </div>
            <div class="form-field">
              <label class="field-label">
                <i class="fas fa-clock"></i>
                Heure
              </label>
              <input 
                v-model="form.time"
                type="time"
                class="modern-input"
                :class="{ 'error': !form.time }"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Section Motif -->
      <div class="form-section reason-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-notes-medical"></i>
          </div>
          <h3 class="section-title">Type de Consultation</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-tag"></i>
              Motif
            </label>
            <select-input 
              v-model="form.reason"
              :options="[
                { value: 'consultation', label: 'Consultation générale', icon: 'clipboard-check' },
                { value: 'follow-up', label: 'Suivi médical', icon: 'redo' },
                { value: 'emergency', label: 'Consultation d\'urgence', icon: 'ambulance' },
                { value: 'other', label: 'Autre motif', icon: 'ellipsis-h' }
              ]"
              option-label="label"
              show-icon
              class="modern-select"
            />
          </div>
        </div>
      </div>

      <!-- Section Notes -->
      <div class="form-section notes-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="fas fa-edit"></i>
          </div>
          <h3 class="section-title">Notes Additionnelles</h3>
        </div>
        
        <div class="section-content">
          <div class="form-field">
            <label class="field-label">
              <i class="fas fa-sticky-note"></i>
              Remarques (optionnelles)
            </label>
            <textarea 
              v-model="form.notes"
              rows="4"
              placeholder="Ajoutez des notes ou commentaires particuliers..."
              class="modern-textarea"
            ></textarea>
          </div>
        </div>
      </div>

        <!-- Validation des erreurs -->
        <error-display :errors="errorMessages" v-if="hasErrors" class="error-display" />
      </div>
      </div>

      <!-- Footer du modal -->
      <div class="modal-footer">
        <div class="footer-content">
          <div class="form-summary" v-if="!hasErrors">
            <div class="summary-item">
              <i class="fas fa-check-circle"></i>
              <span>Formulaire valide</span>
            </div>
          </div>

          <div class="action-buttons">
            <button
              @click="$emit('close')"
              class="btn btn-secondary"
            >
              <i class="fas fa-times"></i>
              <span>Annuler</span>
            </button>
            <button
              @click="saveAppointment"
              :disabled="hasErrors"
              class="btn btn-primary"
              :class="{ 'loading': isLoading }"
            >
              <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-save"></i>
              <span>{{ appointment.id ? 'Modifier' : 'Créer le RDV' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { usePatientStore } from '@/stores/patientStore'
import { useDoctorStore } from '@/stores/doctorStore'
import SelectInput from '../shared/SelectInput.vue'
import ErrorDisplay from '../shared/ErrorDisplay.vue'

const props = defineProps({
  appointment: {
    type: Object,
    default: () => ({})
  },
  patient: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'save'])

// Variables réactives pour le modal
const isLoading = ref(false)
const patientStore = usePatientStore()
const doctorStore = useDoctorStore()

const form = ref({
  id: props.appointment?.id || null,
  patientId: props.patient?.id || props.appointment?.patientId || '',
  doctorId: props.appointment?.doctorId || '',
  date: props.appointment?.date || '',
  time: props.appointment?.time || '',
  reason: props.appointment?.reason || 'consultation',
  notes: props.appointment?.notes || ''
})

const patients = computed(() => {
  console.log('Patients dans le store:', patientStore.patients)
  return patientStore.patients.map(p => ({
    value: p.id,
    label: `${p.nom} ${p.prenom}`,
    fullName: `${p.nom} ${p.prenom}`
  }))
})

const doctors = computed(() => doctorStore.doctors.filter(d => d.role === 'doctor').map(d => ({
  value: d.id,
  label: `Dr. ${d.nom} ${d.prenom}`,
  fullName: `${d.nom} ${d.prenom}`
})))

const errorMessages = computed(() => {
  const errors = []
  if (!form.value.patientId) errors.push('Sélectionner un patient')
  if (!form.value.doctorId) errors.push('Sélectionner un médecin')
  if (!form.value.date) errors.push('Définir une date')
  if (!form.value.time) errors.push('Définir une heure')
  return errors
})

const hasErrors = computed(() => errorMessages.value.length > 0)

// Calcul de la progression du formulaire
const progressPercentage = computed(() => {
  const fields = ['patientId', 'doctorId', 'date', 'time']
  const filledFields = fields.filter(field => form.value[field])
  return (filledFields.length / fields.length) * 100
})

// Fonction pour gérer le clic sur l'overlay
function handleOverlayClick() {
  emit('close')
}

async function saveAppointment() {
  if (!hasErrors.value) {
    isLoading.value = true

    try {
      // Transformer les données pour correspondre à l'API
      const appointmentData = {
        patient_id: form.value.patientId,
        doctor_id: form.value.doctorId,
        date: `${form.value.date} ${form.value.time}:00`,
        type: form.value.reason,
        notes: form.value.notes,
        duration: 30
      }

      console.log('Données du formulaire:', appointmentData)
      emit('save', appointmentData)
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error)
    } finally {
      isLoading.value = false
    }
  }
}

onMounted(async () => {
  try {
    console.log('Chargement des patients et médecins...')
    await Promise.all([
      patientStore.fetchAll(), // 
      doctorStore.fetchDoctors()
    ])
    console.log('Patients chargés:', patientStore.patients)
    console.log('Médecins chargés:', doctorStore.doctors)
  } catch (error) {
    console.error('Erreur lors du chargement des données:', error)
  }
})

</script>

<style scoped>
/* Modal overlay moderne */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header du modal */
.modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  color: white;
  padding: 0;
  position: relative;
  overflow: hidden;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.title-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.title-info {
  flex: 1;
}

.modal-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
}

.modal-subtitle {
  margin: 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.close-button {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

/* Barre de progression */
.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Corps du modal */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  background: #f8fafc;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: none;
}

.form-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.header-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border-radius: 8px;
  font-size: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
  font-size: 0.95rem;
  font-weight: 500;
}

.field-label i {
  color: #6b7280;
}

.modern-input,
.modern-select,
.modern-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.95rem;
  color: #1f2937;
  background: white;
  transition: all 0.2s ease;
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-input.error,
.modern-select.error,
.modern-textarea.error {
  border-color: #ef4444;
}

.modern-input.error:focus,
.modern-select.error:focus,
.modern-textarea.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.datetime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.error-display {
  background: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  color: #991b1b;
}

/* Footer du modal */
.modal-footer {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  gap: 1rem;
}

.form-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #10b981;
  font-size: 0.9rem;
  font-weight: 500;
}

.form-summary i {
  font-size: 1rem;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.btn:hover i {
  transform: scale(1.1);
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary.loading {
  pointer-events: none;
}

.btn-primary.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.btn-secondary {
  background: white;
  color: #64748b;
  border-color: #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Animations */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-container {
    max-height: 95vh;
  }

  .header-content {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .title-section {
    gap: 1rem;
  }

  .title-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .modal-subtitle {
    font-size: 0.9rem;
  }

  .close-button {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    align-self: flex-end;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .form-section {
    padding: 1.5rem;
  }

  .datetime-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    padding: 1.5rem;
  }

  .action-buttons {
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .btn {
    width: 100%;
    padding: 1rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0;
  }

  .modal-container {
    border-radius: 0;
    max-height: 100vh;
    height: 100vh;
  }

  .header-content {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .form-section {
    padding: 1rem;
    border-radius: 12px;
  }

  .footer-content {
    padding: 1rem;
  }
}
</style>