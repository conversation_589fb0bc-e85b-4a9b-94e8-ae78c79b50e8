<?php
class Doctor {
    private $pdo;
    
    public function __construct() {
        $this->pdo = Database::getInstance()->getConnection();
    }
    
    public function findAll() {
        $stmt = $this->pdo->query("SELECT * FROM medecins ORDER BY nom ASC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getById($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM medecins WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getAvailableSlots($medecinId, $date) {
        // Récupérer les informations du médecin
        $medecin = $this->getById($medecinId);
        if (!$medecin) {
            throw new Exception("Médecin non trouvé");
        }
        
        // Récupérer les horaires de travail (JSON)
        $horairsTravail = json_decode($medecin['horaires_travail'] ?? '{}', true);
        $jourSemaine = strtolower(date('l', strtotime($date))); // Jour en anglais
        
        // Mapping jour français
        $joursMapping = [
            'monday' => 'lundi',
            'tuesday' => 'mardi', 
            'wednesday' => 'mercredi',
            'thursday' => 'jeudi',
            'friday' => 'vendredi',
            'saturday' => 'samedi',
            'sunday' => 'dimanche'
        ];
        
        $jourFr = $joursMapping[$jourSemaine] ?? $jourSemaine;
        
        // Vérifier si le médecin travaille ce jour
        if (!isset($horairsTravail[$jourFr])) {
            return []; // Pas de créneaux disponibles
        }
        
        $horaireJour = $horairsTravail[$jourFr];
        
        // Récupérer les rendez-vous existants
        $stmt = $this->pdo->prepare("
            SELECT date_rendez_vous, duree 
            FROM rendez_vous 
            WHERE id_medecin = ? AND DATE(date_rendez_vous) = ? 
            AND statut IN ('planifie', 'confirme')
        ");
        $stmt->execute([$medecinId, $date]);
        $rendezvousExistants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Générer les créneaux disponibles
        $creneauxDisponibles = [];
        $heureDebut = $horaireJour['debut'] ?? '09:00';
        $heureFin = $horaireJour['fin'] ?? '17:00';
        $dureeRdv = 30; // minutes par défaut
        $dureePause = $medecin['duree_pause'] ?? 15;
        
        $currentTime = new DateTime("$date $heureDebut");
        $endTime = new DateTime("$date $heureFin");
        
        while ($currentTime < $endTime) {
            $timeSlot = $currentTime->format('H:i');
            
            // Vérifier si le créneau est libre
            $estLibre = true;
            foreach ($rendezvousExistants as $rdv) {
                $rdvDebut = new DateTime($rdv['date_rendez_vous']);
                $rdvFin = clone $rdvDebut;
                $rdvFin->add(new DateInterval('PT' . $rdv['duree'] . 'M'));
                
                if ($currentTime >= $rdvDebut && $currentTime < $rdvFin) {
                    $estLibre = false;
                    break;
                }
            }
            
            if ($estLibre) {
                $creneauxDisponibles[] = [
                    'time' => $timeSlot,
                    'duree' => $dureeRdv
                ];
            }
            
            // Passer au créneau suivant (durée RDV + pause)
            $currentTime->add(new DateInterval('PT' . ($dureeRdv + $dureePause) . 'M'));
        }
        
        return $creneauxDisponibles;
    }

    public function create($data) {
        try {
            $this->pdo->beginTransaction();

            // 1. Créer l'utilisateur
            $stmt = $this->pdo->prepare("
                INSERT INTO utilisateur (nom, prenom, email, password, role) 
                VALUES (?, ?, ?, ?, 'doctor')
            ");
            
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            $stmt->execute([
                $data['nom'],
                $data['prenom'],
                $data['email'],
                $hashedPassword
            ]);
            
            $userId = $this->pdo->lastInsertId();

            // 2. Créer le médecin
            $stmt = $this->pdo->prepare("
                INSERT INTO medecins (
                    user_id, specialite, nom, prenom, email, 
                    telephone, horaires_travail, duree_pause
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $horairesTravail = json_encode([
                'lundi' => ['debut' => '09:00', 'fin' => '17:00'],
                'mardi' => ['debut' => '09:00', 'fin' => '17:00'],
                'mercredi' => ['debut' => '09:00', 'fin' => '17:00'],
                'jeudi' => ['debut' => '09:00', 'fin' => '17:00'],
                'vendredi' => ['debut' => '09:00', 'fin' => '17:00']
            ]);

            $stmt->execute([
                $userId,
                $data['specialite'],
                $data['nom'],
                $data['prenom'],
                $data['email'],
                $data['telephone'] ?? null,
                $horairesTravail,
                $data['duree_pause'] ?? 15
            ]);

            $medecinId = $this->pdo->lastInsertId();
            
            $this->pdo->commit();

            return [
                'id' => $medecinId,
                'user_id' => $userId,
                'nom' => $data['nom'],
                'prenom' => $data['prenom'],
                'email' => $data['email'],
                'specialite' => $data['specialite'],
                'telephone' => $data['telephone'] ?? null
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log('Erreur création médecin: ' . $e->getMessage());
            throw $e;
        }
    }

    public function emailExists($email) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM utilisateur WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetchColumn() > 0;
    }
}
?>