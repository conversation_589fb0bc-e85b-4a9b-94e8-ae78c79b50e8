import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import AppointmentEditor from '../AppointmentEditor.vue'
import { createPinia, setActivePinia } from 'pinia'

describe('AppointmentEditor', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('devrait afficher le formulaire de création de rendez-vous', () => {
    const wrapper = mount(AppointmentEditor)
    
    // Vérifier que le titre est correct pour un nouveau rendez-vous
    expect(wrapper.find('.title-text').text()).toBe('Nouveau Rendez-vous')
    
    // Vérifier la présence des sections principales
    expect(wrapper.find('.patient-section').exists()).toBe(true)
    expect(wrapper.find('.doctor-section').exists()).toBe(true)
    expect(wrapper.find('.datetime-section').exists()).toBe(true)
    expect(wrapper.find('.reason-section').exists()).toBe(true)
    expect(wrapper.find('.notes-section').exists()).toBe(true)
  })

  it('devrait afficher les erreurs de validation', async () => {
    const wrapper = mount(AppointmentEditor)
    
    // Essayer de sauvegarder sans remplir les champs requis
    await wrapper.find('.btn-primary').trigger('click')
    
    // Vérifier que les erreurs sont affichées
    const errorDisplay = wrapper.find('.error-display')
    expect(errorDisplay.exists()).toBe(true)
    expect(errorDisplay.text()).toContain('Sélectionner un patient')
    expect(errorDisplay.text()).toContain('Sélectionner un médecin')
    expect(errorDisplay.text()).toContain('Définir une date')
    expect(errorDisplay.text()).toContain('Définir une heure')
  })

  it('devrait émettre l\'événement save avec les données correctes', async () => {
    const wrapper = mount(AppointmentEditor)
    
    // Remplir le formulaire
    await wrapper.find('select[v-model="form.patientId"]').setValue('1')
    await wrapper.find('select[v-model="form.doctorId"]').setValue('1')
    await wrapper.find('input[type="date"]').setValue('2025-06-05')
    await wrapper.find('input[type="time"]').setValue('10:00')
    await wrapper.find('select[v-model="form.reason"]').setValue('consultation')
    await wrapper.find('textarea').setValue('Test notes')
    
    // Sauvegarder le formulaire
    await wrapper.find('.btn-primary').trigger('click')
    
    // Vérifier l'émission de l'événement save
    expect(wrapper.emitted('save')).toBeTruthy()
    expect(wrapper.emitted('save')[0][0]).toEqual({
      id: null,
      patientId: '1',
      doctorId: '1',
      date: '2025-06-05',
      time: '10:00',
      reason: 'consultation',
      notes: 'Test notes'
    })
  })
}) 