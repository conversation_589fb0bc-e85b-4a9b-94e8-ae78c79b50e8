<script setup>
import { ref, computed } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'

const props = defineProps({
  selectedDate: {
    type: Date,
    default: () => new Date()
  }
})

const emit = defineEmits(['date-selected'])

const store = useAppointmentStore()
const currentDate = ref(new Date())
const currentView = ref('month')

// Configuration du calendrier
const weekDays = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam']
const workingHours = Array.from({ length: 12 }, (_, i) => i + 8) // 8h à 19h

// Navigation dans le calendrier
const currentMonthName = computed(() => {
  return currentDate.value.toLocaleString('fr-FR', { month: 'long' })
})

const currentYear = computed(() => {
  return currentDate.value.getFullYear()
})

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  const days = []
  
  // Jours du mois précédent
  const prevMonthDays = firstDay.getDay()
  const prevMonth = new Date(year, month, 0)
  for (let i = prevMonthDays - 1; i >= 0; i--) {
    days.push({
      date: new Date(year, month - 1, prevMonth.getDate() - i),
      dayNumber: prevMonth.getDate() - i,
      currentMonth: false
    })
  }
  
  // Jours du mois en cours
  for (let i = 1; i <= lastDay.getDate(); i++) {
    days.push({
      date: new Date(year, month, i),
      dayNumber: i,
      currentMonth: true
    })
  }
  
  // Jours du mois suivant
  const remainingDays = 42 - days.length
  for (let i = 1; i <= remainingDays; i++) {
    days.push({
      date: new Date(year, month + 1, i),
      dayNumber: i,
      currentMonth: false
    })
  }
  
  return days
})

const currentWeekDays = computed(() => {
  const date = new Date(currentDate.value)
  const day = date.getDay()
  const diff = date.getDate() - day
  
  return Array.from({ length: 7 }, (_, i) => {
    const current = new Date(date)
    current.setDate(diff + i)
    return {
      date: current,
      dayNumber: current.getDate()
    }
  })
})

// Navigation
function previousMonth() {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() - 1,
    1
  )
}

function nextMonth() {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() + 1,
    1
  )
}

function previousDay() {
  const newDate = new Date(currentDate.value)
  newDate.setDate(newDate.getDate() - 1)
  currentDate.value = newDate
}

function nextDay() {
  const newDate = new Date(currentDate.value)
  newDate.setDate(newDate.getDate() + 1)
  currentDate.value = newDate
}

function goToToday() {
  currentDate.value = new Date()
}

// Sélection de date
function selectDate(date) {
  currentDate.value = date
  if (currentView.value !== 'day') {
    currentView.value = 'day'
  }
  emit('date-selected', date)
}

// Utilitaires
function isToday(date) {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

function hasAppointments(date) {
  return store.getAppointmentsByDate(date).length > 0
}

function getAppointments(date) {
  return store.getAppointmentsByDate(date)
}

function getAppointmentsByHour(date, hour) {
  return getAppointments(date).filter(apt => {
    const aptHour = new Date(apt.date).getHours()
    return aptHour === hour
  })
}

function formatHour(hour) {
  return `${hour}:00`
}

function formatWeekDay(date) {
  return date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' })
}

function formatAppointmentTime(appointment) {
  const date = new Date(appointment.date)
  return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
}

function getAppointmentColor(status) {
  const colors = {
    pending: '#3498db',
    confirmed: '#2ecc71',
    cancelled: '#e74c3c',
    completed: '#34495e'
  }
  return colors[status] || colors.pending
}

function getAppointmentStyle(appointment) {
  const startTime = new Date(appointment.date)
  const duration = appointment.duration || 30 // durée par défaut : 30 minutes

  const startHour = startTime.getHours()
  const startMinutes = startTime.getMinutes()

  const top = (startHour - 8) * 60 + startMinutes
  const height = duration

  return {
    top: `${top}px`,
    height: `${height}px`,
    backgroundColor: getAppointmentColor(appointment.status)
  }
}
</script>

<template>
  <div class="calendar-container">
    <div class="calendar-header">
      <div class="calendar-nav">
        <button @click="previousMonth" class="nav-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <h2>{{ currentMonthName }} {{ currentYear }}</h2>
        <button @click="nextMonth" class="nav-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      <div class="view-options">
        <button 
          v-for="view in ['month', 'week', 'day']" 
          :key="view"
          :class="['view-btn', { active: currentView === view }]"
          @click="currentView = view"
        >
          {{ view.charAt(0).toUpperCase() + view.slice(1) }}
        </button>
      </div>
    </div>

    <!-- Vue par mois -->
    <div v-if="currentView === 'month'" class="calendar-grid">
      <div class="weekday-header" v-for="day in weekDays" :key="day">
        {{ day }}
      </div>
      <div
        v-for="(day, index) in calendarDays"
        :key="index"
        :class="[
          'calendar-day',
          { 'current-month': day.currentMonth },
          { 'today': isToday(day.date) },
          { 'has-appointments': hasAppointments(day.date) }
        ]"
        @click="selectDate(day.date)"
      >
        <span class="day-number">{{ day.dayNumber }}</span>
        <div class="appointment-indicators" v-if="hasAppointments(day.date)">
          <span 
            v-for="(apt, i) in getAppointments(day.date)"
            :key="i"
            class="indicator"
            :style="{ backgroundColor: getAppointmentColor(apt.status) }"
            :title="apt.patientName"
          ></span>
        </div>
      </div>
    </div>

    <!-- Vue par semaine -->
    <div v-else-if="currentView === 'week'" class="week-view">
      <div class="time-slots">
        <div 
          v-for="hour in workingHours" 
          :key="hour"
          class="time-slot"
        >
          {{ formatHour(hour) }}
        </div>
      </div>
      <div class="week-grid">
        <div 
          v-for="day in currentWeekDays" 
          :key="day.date"
          class="week-day"
        >
          <div class="week-day-header">
            {{ formatWeekDay(day.date) }}
          </div>
          <div class="week-day-appointments">
            <div 
              v-for="apt in getAppointments(day.date)"
              :key="apt.id"
              :class="['appointment-block', apt.status]"
              :style="getAppointmentStyle(apt)"
            >
              {{ apt.patientName }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Vue par jour -->
    <div v-else class="day-view">
      <div class="day-header">
        <h3>{{ currentDate.toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) }}</h3>
        <div class="day-navigation">
          <button @click="previousDay" class="nav-btn">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button @click="goToToday" class="today-btn">Aujourd'hui</button>
          <button @click="nextDay" class="nav-btn">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
      <div class="time-slots-container">
        <div
          v-for="hour in workingHours"
          :key="hour"
          class="time-slot"
        >
          <div class="time-label">{{ formatHour(hour) }}</div>
          <div class="slot-content">
            <div
              v-for="apt in getAppointmentsByHour(currentDate, hour)"
              :key="apt.id"
              :class="['appointment-block', apt.status]"
              :style="{ backgroundColor: getAppointmentColor(apt.status) }"
            >
              <div class="appointment-time">
                {{ formatAppointmentTime(apt) }}
              </div>
              <div class="appointment-patient">
                {{ apt.patientName }}
              </div>
              <div class="appointment-type">
                {{ apt.type }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.calendar-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #64748b;
}

.nav-btn:hover {
  color: #3b82f6;
}

.view-options {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background: none;
  border-radius: 4px;
  cursor: pointer;
}

.view-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e2e8f0;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.weekday-header {
  background: #f8fafc;
  padding: 0.75rem;
  text-align: center;
  font-weight: 500;
  color: #64748b;
}

.calendar-day {
  background: white;
  min-height: 100px;
  padding: 0.5rem;
  position: relative;
  cursor: pointer;
}

.calendar-day:hover {
  background: #f1f5f9;
}

.day-number {
  font-weight: 500;
  color: #1e293b;
}

.current-month .day-number {
  color: #1e293b;
}

.today {
  background: #eff6ff;
}

.today .day-number {
  color: #3b82f6;
  font-weight: 600;
}

.appointment-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 0.5rem;
}

.indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

/* Vue par semaine */
.week-view {
  display: flex;
  height: 600px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.time-slots {
  width: 60px;
  border-right: 1px solid #e2e8f0;
}

.time-slot {
  height: 60px;
  padding: 0.25rem;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
  color: #64748b;
}

.week-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.week-day {
  border-right: 1px solid #e2e8f0;
  position: relative;
}

.week-day-header {
  padding: 0.5rem;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 500;
}

.appointment-block {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 2px;
  padding: 0.25rem;
  border-radius: 4px;
  font-size: 0.75rem;
  color: white;
  overflow: hidden;
}

/* Vue par jour */
.day-view {
  max-height: 600px;
  overflow-y: auto;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.day-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
}

.day-navigation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.today-btn {
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.today-btn:hover {
  background: #2563eb;
}

.time-slots-container {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.day-view .time-slot {
  display: flex;
  min-height: 60px;
  border-bottom: 1px solid #e2e8f0;
}

.day-view .time-slot:last-child {
  border-bottom: none;
}

.time-label {
  width: 80px;
  padding: 0.75rem;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  font-weight: 500;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slot-content {
  flex: 1;
  padding: 0.5rem;
  position: relative;
  background: white;
}

.day-view .appointment-block {
  position: relative;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.day-view .appointment-block:last-child {
  margin-bottom: 0;
}

.appointment-time {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.appointment-patient {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.appointment-type {
  font-size: 0.75rem;
  opacity: 0.9;
  font-style: italic;
}

@media (max-width: 768px) {
  .calendar-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .calendar-day {
    min-height: 80px;
  }
  
  .week-view {
    height: 500px;
  }
}
</style>
