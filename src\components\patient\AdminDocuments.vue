<template>
  <div class="admin-documents">
    <h2 class="section-title">
      <i class="fas fa-file-alt"></i>
      Documents Administratifs
    </h2>
    <div class="documents-content">
      <div class="documents-actions">
        <button class="upload-btn" @click="triggerFileUpload">
          <i class="fas fa-upload"></i>
          Téléverser un document
        </button>
        <input
          type="file"
          ref="fileInput"
          @change="handleFileUpload"
          style="display: none"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
        >
      </div>

      <div v-if="loading" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Chargement des documents...
      </div>
      
      <div v-else-if="documents.length === 0" class="empty-state">
        <i class="fas fa-folder-open"></i>
        <p>Aucun document disponible</p>
      </div>
      
      <div v-else class="documents-list">
        <div v-for="doc in documents" :key="doc.id" class="document-item">
          <div class="doc-icon">
            <i :class="getDocumentIcon(doc.type)"></i>
          </div>
          <div class="doc-info">
            <h3>{{ doc.name }}</h3>
            <p>{{ doc.date }}</p>
          </div>
          <div class="doc-actions">
            <button class="action-btn download" @click="downloadDocument(doc)">
              <i class="fas fa-download"></i>
            </button>
            <button class="action-btn delete" @click="deleteDocument(doc)">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loading = ref(true)
const documents = ref([])
const fileInput = ref(null)

const getDocumentIcon = (type) => {
  const icons = {
    pdf: 'fas fa-file-pdf',
    doc: 'fas fa-file-word',
    image: 'fas fa-file-image',
    default: 'fas fa-file'
  }
  return icons[type] || icons.default
}

const triggerFileUpload = () => {
  fileInput.value.click()
}

const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  // Simulation d'upload
  const formData = new FormData()
  formData.append('file', file)
  
  try {
    // Appel API à implémenter
    console.log('Fichier à uploader:', file.name)
  } catch (error) {
    console.error('Erreur lors de l\'upload:', error)
  }
}

const downloadDocument = async (doc) => {
  try {
    // Appel API à implémenter
    console.log('Téléchargement du document:', doc.name)
  } catch (error) {
    console.error('Erreur lors du téléchargement:', error)
  }
}

const deleteDocument = async (doc) => {
  if (!confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return
  
  try {
    // Appel API à implémenter
    console.log('Suppression du document:', doc.name)
    documents.value = documents.value.filter(d => d.id !== doc.id)
  } catch (error) {
    console.error('Erreur lors de la suppression:', error)
  }
}

onMounted(async () => {
  try {
    // Simulation d'appel API
    await new Promise(resolve => setTimeout(resolve, 1000))
    documents.value = [
      {
        id: 1,
        name: 'Ordonnance.pdf',
        type: 'pdf',
        date: '01/06/2025'
      },
      {
        id: 2,
        name: 'Radiographie.jpg',
        type: 'image',
        date: '01/06/2025'
      }
    ]
  } catch (error) {
    console.error('Erreur lors du chargement des documents:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.admin-documents {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e293b;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.section-title i {
  color: #3b82f6;
}

.documents-actions {
  margin-bottom: 1.5rem;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.upload-btn:hover {
  background: #2563eb;
}

.loading, .empty-state {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.loading i, .empty-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: transform 0.2s;
}

.document-item:hover {
  transform: translateY(-2px);
}

.doc-icon {
  font-size: 1.5rem;
  color: #3b82f6;
  margin-right: 1rem;
}

.doc-info {
  flex: 1;
}

.doc-info h3 {
  font-size: 1rem;
  color: #1e293b;
  margin: 0;
}

.doc-info p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0.25rem 0 0;
}

.doc-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn.download {
  background: #dcfce7;
  color: #166534;
}

.action-btn.download:hover {
  background: #bbf7d0;
}

.action-btn.delete {
  background: #fee2e2;
  color: #991b1b;
}

.action-btn.delete:hover {
  background: #fecaca;
}

@media (max-width: 768px) {
  .admin-documents {
    padding: 1rem;
  }
  
  .document-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .doc-actions {
    width: 100%;
    justify-content: center;
    margin-top: 0.5rem;
  }
}
</style> 