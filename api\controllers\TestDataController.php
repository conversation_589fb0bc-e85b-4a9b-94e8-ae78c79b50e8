<?php
require_once __DIR__ . '/../models/Appointment.php';
require_once __DIR__ . '/../models/SuggestedSlot.php';

class TestDataController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Ajouter des données de test
     */
    public function addTestData() {
        try {
            $results = [
                'doctors_added' => 0,
                'patients_added' => 0,
                'appointments_added' => 0,
                'slots_added' => 0,
                'messages' => []
            ];
            
            // Vérifier si des médecins existent
            $stmt = $this->db->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'medecin'");
            $doctorCount = $stmt->fetchColumn();
            
            if ($doctorCount == 0) {
                $results['messages'][] = "👨‍⚕️ Ajout de médecins de test...";
                
                $doctors = [
                    ['nom' => '<PERSON><PERSON>', 'prenom' => '<PERSON>', 'email' => '<EMAIL>', 'specialite' => 'Cardiologie'],
                    ['nom' => 'Martin', 'prenom' => 'Marie', 'email' => '<EMAIL>', 'specialite' => 'Dermatologie'],
                    ['nom' => 'Bernard', 'prenom' => 'Pierre', 'email' => '<EMAIL>', 'specialite' => 'Médecine générale']
                ];
                
                foreach ($doctors as $doctor) {
                    $stmt = $this->db->prepare("
                        INSERT INTO utilisateur (nom, prenom, email, mot_de_passe, role, specialite, telephone, adresse, date_creation)
                        VALUES (?, ?, ?, ?, 'medecin', ?, '0123456789', '123 Rue de l\'Hôpital', NOW())
                    ");
                    $stmt->execute([
                        $doctor['nom'],
                        $doctor['prenom'],
                        $doctor['email'],
                        password_hash('password123', PASSWORD_DEFAULT),
                        $doctor['specialite']
                    ]);
                    $results['doctors_added']++;
                    $results['messages'][] = "  ✅ Dr. {$doctor['prenom']} {$doctor['nom']} ajouté";
                }
            } else {
                $results['messages'][] = "👨‍⚕️ $doctorCount médecin(s) déjà présent(s)";
            }

            // Vérifier si des patients existent
            $stmt = $this->db->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'patient'");
            $patientCount = $stmt->fetchColumn();
            
            if ($patientCount == 0) {
                $results['messages'][] = "👤 Ajout de patients de test...";
                
                $patients = [
                    ['nom' => 'Durand', 'prenom' => 'Sophie', 'email' => '<EMAIL>'],
                    ['nom' => 'Moreau', 'prenom' => 'Paul', 'email' => '<EMAIL>'],
                    ['nom' => 'Petit', 'prenom' => 'Claire', 'email' => '<EMAIL>'],
                    ['nom' => 'Roux', 'prenom' => 'Michel', 'email' => '<EMAIL>']
                ];
                
                foreach ($patients as $patient) {
                    $stmt = $this->db->prepare("
                        INSERT INTO utilisateur (nom, prenom, email, mot_de_passe, role, telephone, adresse, date_creation)
                        VALUES (?, ?, ?, ?, 'patient', '**********', '456 Rue des Patients', NOW())
                    ");
                    $stmt->execute([
                        $patient['nom'],
                        $patient['prenom'],
                        $patient['email'],
                        password_hash('password123', PASSWORD_DEFAULT)
                    ]);
                    $results['patients_added']++;
                    $results['messages'][] = "  ✅ {$patient['prenom']} {$patient['nom']} ajouté";
                }
            } else {
                $results['messages'][] = "👤 $patientCount patient(s) déjà présent(s)";
            }

            // Récupérer les IDs des médecins et patients
            $stmt = $this->db->query("SELECT id, nom, prenom FROM utilisateur WHERE role = 'medecin' LIMIT 3");
            $doctors = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $stmt = $this->db->query("SELECT id, nom, prenom FROM utilisateur WHERE role = 'patient' LIMIT 4");
            $patients = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Vérifier si des rendez-vous existent
            $stmt = $this->db->query("SELECT COUNT(*) FROM rendez_vous");
            $appointmentCount = $stmt->fetchColumn();
            
            if ($appointmentCount == 0 && !empty($doctors) && !empty($patients)) {
                $results['messages'][] = "📅 Ajout de rendez-vous de test...";
                
                $appointments = [
                    [
                        'doctor_id' => $doctors[0]['id'],
                        'patient_id' => $patients[0]['id'],
                        'date' => date('Y-m-d H:i:s', strtotime('+1 day 09:00')),
                        'type' => 'consultation',
                        'statut' => 'confirme'
                    ],
                    [
                        'doctor_id' => $doctors[0]['id'],
                        'patient_id' => $patients[1]['id'],
                        'date' => date('Y-m-d H:i:s', strtotime('+1 day 10:30')),
                        'type' => 'controle',
                        'statut' => 'confirme'
                    ],
                    [
                        'doctor_id' => $doctors[1]['id'],
                        'patient_id' => $patients[2]['id'],
                        'date' => date('Y-m-d H:i:s', strtotime('+2 days 14:00')),
                        'type' => 'consultation',
                        'statut' => 'en_attente'
                    ],
                    [
                        'doctor_id' => $doctors[2]['id'],
                        'patient_id' => $patients[3]['id'],
                        'date' => date('Y-m-d H:i:s', strtotime('+3 days 16:00')),
                        'type' => 'urgence',
                        'statut' => 'confirme'
                    ]
                ];
                
                foreach ($appointments as $apt) {
                    $stmt = $this->db->prepare("
                        INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, type, statut, duree, notes, cree_le)
                        VALUES (?, ?, ?, ?, ?, 30, 'Rendez-vous de test', NOW())
                    ");
                    $stmt->execute([
                        $apt['doctor_id'],
                        $apt['patient_id'],
                        $apt['date'],
                        $apt['type'],
                        $apt['statut']
                    ]);
                    $results['appointments_added']++;
                    $results['messages'][] = "  ✅ RDV {$apt['date']} ajouté";
                }
            } else {
                $results['messages'][] = "📅 $appointmentCount rendez-vous déjà présent(s)";
            }

            // Vérifier si la table creneaux_suggeres existe et ajouter des données
            $stmt = $this->db->query("SHOW TABLES LIKE 'creneaux_suggeres'");
            $tableExists = $stmt->fetch();
            
            if ($tableExists) {
                $stmt = $this->db->query("SELECT COUNT(*) FROM creneaux_suggeres");
                $slotCount = $stmt->fetchColumn();
                
                if ($slotCount == 0 && !empty($doctors) && !empty($patients)) {
                    $results['messages'][] = "🕒 Ajout de créneaux suggérés de test...";
                    
                    $slots = [
                        [
                            'doctor_id' => $doctors[0]['id'],
                            'patient_id' => $patients[0]['id'],
                            'date' => date('Y-m-d H:i:s', strtotime('+4 days 09:00')),
                            'duree' => 30,
                            'raison' => 'Créneau disponible'
                        ],
                        [
                            'doctor_id' => $doctors[0]['id'],
                            'patient_id' => $patients[1]['id'],
                            'date' => date('Y-m-d H:i:s', strtotime('+4 days 11:00')),
                            'duree' => 45,
                            'raison' => 'Consultation de suivi'
                        ],
                        [
                            'doctor_id' => $doctors[1]['id'],
                            'patient_id' => $patients[2]['id'],
                            'date' => date('Y-m-d H:i:s', strtotime('+5 days 15:00')),
                            'duree' => 30,
                            'raison' => 'Nouveau patient'
                        ]
                    ];
                    
                    foreach ($slots as $slot) {
                        $stmt = $this->db->prepare("
                            INSERT INTO creneaux_suggeres (id_medecin, id_patient, date_heure_suggeree, duree, score_confiance, raison, accepte, cree_le, expire_le)
                            VALUES (?, ?, ?, ?, 0.8, ?, NULL, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))
                        ");
                        $stmt->execute([
                            $slot['doctor_id'],
                            $slot['patient_id'],
                            $slot['date'],
                            $slot['duree'],
                            $slot['raison']
                        ]);
                        $results['slots_added']++;
                        $results['messages'][] = "  ✅ Créneau {$slot['date']} ajouté";
                    }
                } else {
                    $results['messages'][] = "🕒 $slotCount créneau(x) suggéré(s) déjà présent(s)";
                }
            } else {
                $results['messages'][] = "🕒 Table 'creneaux_suggeres' n'existe pas";
            }

            $results['messages'][] = "✅ Données de test ajoutées avec succès!";
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $results,
                'message' => 'Données de test ajoutées avec succès'
            ]);
            
        } catch (Exception $e) {
            error_log("Erreur dans TestDataController->addTestData: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ajout des données de test: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Vérifier les données existantes
     */
    public function checkData() {
        try {
            $results = [];
            
            // Compter les utilisateurs
            $stmt = $this->db->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'medecin'");
            $results['doctors_count'] = $stmt->fetchColumn();
            
            $stmt = $this->db->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'patient'");
            $results['patients_count'] = $stmt->fetchColumn();
            
            // Compter les rendez-vous
            $stmt = $this->db->query("SELECT COUNT(*) FROM rendez_vous");
            $results['appointments_count'] = $stmt->fetchColumn();
            
            // Compter les créneaux suggérés
            $stmt = $this->db->query("SHOW TABLES LIKE 'creneaux_suggeres'");
            if ($stmt->fetch()) {
                $stmt = $this->db->query("SELECT COUNT(*) FROM creneaux_suggeres");
                $results['slots_count'] = $stmt->fetchColumn();
            } else {
                $results['slots_count'] = 0;
                $results['slots_table_exists'] = false;
            }
            
            // Récupérer quelques exemples
            $stmt = $this->db->query("
                SELECT rv.id, rv.date_rendez_vous, rv.statut, 
                       p.nom as patient_nom, p.prenom as patient_prenom,
                       m.nom as medecin_nom, m.prenom as medecin_prenom
                FROM rendez_vous rv
                LEFT JOIN utilisateur p ON rv.id_patient = p.id
                LEFT JOIN utilisateur m ON rv.id_medecin = m.id
                ORDER BY rv.date_rendez_vous DESC
                LIMIT 5
            ");
            $results['sample_appointments'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $results
            ]);
            
        } catch (Exception $e) {
            error_log("Erreur dans TestDataController->checkData: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la vérification des données: ' . $e->getMessage()
            ]);
        }
    }
}
?>
