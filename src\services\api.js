import axios from 'axios'

// Configuration de l'instance Axios
const api = axios.create({
  baseURL: 'http://localhost:8000', // URL directe vers l'API PHP
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  response => response,
  async error => {
    console.error('Erreur de réponse:', error.response?.status, error.response?.data)

    if (error.response?.status === 401) {
      // Vérifier si c'est une vérification automatique ou un login réel
      const isAuthVerification = error.config?.url?.includes('/auth/verify')
      const isLoginAttempt = error.config?.url?.includes('/auth/login')

      if (isAuthVerification) {
        // Nettoyage silencieux pour la vérification automatique
        console.log('API: Token invalide lors de la vérification automatique, nettoyage silencieux')
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')
        return Promise.reject(new Error('Token invalide'))
      } else if (isLoginAttempt) {
        // Erreur de login - ne pas nettoyer ni rediriger
        console.log('API: Échec de connexion - identifiants invalides')
        return Promise.reject(new Error('Identifiants invalides'))
      } else {
        // Erreur 401 sur une autre action - nettoyage silencieux
        console.log('API: Token expiré lors d\'une action, nettoyage silencieux')
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')

        // Pas de redirection automatique ni de message d'erreur
        return Promise.reject(new Error('Non autorisé'))
      }
    }

    if (error.response?.status === 404) {
      return Promise.reject(new Error('Route non trouvée'))
    }

    if (error.response?.status === 500) {
      return Promise.reject(new Error('Erreur serveur. Veuillez réessayer plus tard.'))
    }

    return Promise.reject(error)
  }
)

// Service pour les patients
export const patientService = {
  // Historique du patient
  getPatientHistory: async (patientId) => {
    try {
      console.log('🔍 Récupération de l\'historique pour le patient:', patientId)

      // Essayer d'abord l'API
      try {
        const response = await api.get(`/patients/${patientId}/history`)
        console.log('✅ Historique récupéré via API:', response.data)
        return response.data.data
      } catch (apiError) {
        console.warn('⚠️ API non disponible, utilisation de données de démonstration:', apiError.message)

        // Fallback avec des données de démonstration réalistes
        const mockHistory = [
          {
            id: 1,
            type: 'consultation',
            title: 'Consultation générale',
            description: 'Contrôle de routine annuel - Tension artérielle normale, poids stable',
            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Il y a 30 jours
            doctor: 'Dr. Martin',
            status: 'terminé'
          },
          {
            id: 2,
            type: 'prescription',
            title: 'Prescription médicamenteuse',
            description: 'Prescription d\'antibiotiques pour infection respiratoire',
            date: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // Il y a 45 jours
            doctor: 'Dr. Martin',
            status: 'terminé'
          },
          {
            id: 3,
            type: 'examen',
            title: 'Analyses sanguines',
            description: 'Bilan sanguin complet - Résultats dans les normes',
            date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // Il y a 60 jours
            doctor: 'Dr. Martin',
            status: 'terminé'
          },
          {
            id: 4,
            type: 'consultation',
            title: 'Consultation de suivi',
            description: 'Suivi post-traitement - Évolution favorable',
            date: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Il y a 90 jours
            doctor: 'Dr. Martin',
            status: 'terminé'
          },
          {
            id: 5,
            type: 'vaccination',
            title: 'Vaccination',
            description: 'Rappel vaccin tétanos - Aucune réaction adverse',
            date: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000), // Il y a 120 jours
            doctor: 'Dr. Martin',
            status: 'terminé'
          }
        ]

        console.log('📝 Historique de démonstration généré:', mockHistory.length, 'événements')
        return mockHistory
      }
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de l\'historique:', error)
      throw error
    }
  },

  // Documents du patient
  getPatientDocuments: async (patientId) => {
    try {
      const response = await api.get(`/patients/${patientId}/documents`)
      return response.data
    } catch (error) {
      console.error('Erreur lors de la récupération des documents:', error)
      throw error
    }
  },

  // Upload d'un document
  uploadDocument: async (patientId, formData) => {
    try {
      const response = await api.post(`/patients/${patientId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('Erreur lors de l\'upload du document:', error)
      throw error
    }
  },

  // Prévisualisation d'un document
  previewDocument: async (documentId) => {
    try {
      const response = await api.get(`/documents/${documentId}/preview`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Erreur lors de la prévisualisation du document:', error)
      throw error
    }
  },

  // Téléchargement d'un document
  downloadDocument: async (documentId) => {
    try {
      const response = await api.get(`/documents/${documentId}/download`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Erreur lors du téléchargement du document:', error)
      throw error
    }
  },

  // Suppression d'un document
  deleteDocument: async (documentId) => {
    try {
      const response = await api.delete(`/documents/${documentId}`)
      return response.data
    } catch (error) {
      console.error('Erreur lors de la suppression du document:', error)
      throw error
    }
  }
}

// Service pour les médecins
export const doctorService = {
  // Récupérer tous les médecins
  getAllDoctors: async () => {
    try {
      const response = await api.get('/doctors');
      if (!response.data || !response.data.status) {
        throw new Error('Format de réponse invalide');
      }
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Erreur lors de la récupération des médecins');
      }
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des médecins:', error);
      throw error;
    }
  }
}
// Service pour les créneaux horaires
export const slotService = {
  // Récupérer les créneaux suggérés
  getSuggestedSlots: async (doctorId, date) => {
    try {
      const response = await api.get(`/doctors/${doctorId}/slots/suggested`, {
        params: { date }
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des créneaux:', error);
      throw error;
    }
  },

  // Vérifier la disponibilité d'un créneau
  checkAvailability: async (doctorId, slotId) => {
    try {
      const response = await api.get(`/doctors/${doctorId}/slots/${slotId}/availability`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la vérification du créneau:', error);
      throw error;
    }
  },

  // Réserver un créneau
  bookSlot: async (doctorId, slotId, appointmentData) => {
    try {
      const response = await api.post(
        `/doctors/${doctorId}/slots/${slotId}/book`,
        appointmentData
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la réservation:', error);
      throw error;
    }
  }
};

export default api