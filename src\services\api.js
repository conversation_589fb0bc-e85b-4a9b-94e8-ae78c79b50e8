import axios from 'axios'

// Configuration de l'instance Axios
const api = axios.create({
  baseURL: 'http://localhost:8000', // URL directe vers l'API PHP
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  response => response,
  async error => {
    console.error('Erreur de réponse:', error.response?.status, error.response?.data)

    if (error.response?.status === 401) {
      // Vérifier si c'est une vérification automatique ou un login réel
      const isAuthVerification = error.config?.url?.includes('/auth/verify')
      const isLoginAttempt = error.config?.url?.includes('/auth/login')

      if (isAuthVerification) {
        // Nettoyage silencieux pour la vérification automatique
        console.log('API: Token invalide lors de la vérification automatique, nettoyage silencieux')
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')
        return Promise.reject(new Error('Token invalide'))
      } else if (isLoginAttempt) {
        // Erreur de login - ne pas nettoyer ni rediriger
        console.log('API: Échec de connexion - identifiants invalides')
        return Promise.reject(new Error('Identifiants invalides'))
      } else {
        // Erreur 401 sur une autre action - nettoyage silencieux
        console.log('API: Token expiré lors d\'une action, nettoyage silencieux')
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')

        // Pas de redirection automatique ni de message d'erreur
        return Promise.reject(new Error('Non autorisé'))
      }
    }

    if (error.response?.status === 404) {
      return Promise.reject(new Error('Route non trouvée'))
    }

    if (error.response?.status === 500) {
      return Promise.reject(new Error('Erreur serveur. Veuillez réessayer plus tard.'))
    }

    return Promise.reject(error)
  }
)

// Service pour les patients
export const patientService = {
  // Historique du patient
  getPatientHistory: async (patientId) => {
    try {
      const response = await api.get(`/patients/${patientId}/history`)
      return response.data.data
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error)
      throw error
    }
  },

  // Documents du patient
  getPatientDocuments: async (patientId) => {
    try {
      const response = await api.get(`/patients/${patientId}/documents`)
      return response.data
    } catch (error) {
      console.error('Erreur lors de la récupération des documents:', error)
      throw error
    }
  },

  // Upload d'un document
  uploadDocument: async (patientId, formData) => {
    try {
      const response = await api.post(`/patients/${patientId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('Erreur lors de l\'upload du document:', error)
      throw error
    }
  },

  // Prévisualisation d'un document
  previewDocument: async (documentId) => {
    try {
      const response = await api.get(`/documents/${documentId}/preview`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Erreur lors de la prévisualisation du document:', error)
      throw error
    }
  },

  // Téléchargement d'un document
  downloadDocument: async (documentId) => {
    try {
      const response = await api.get(`/documents/${documentId}/download`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Erreur lors du téléchargement du document:', error)
      throw error
    }
  },

  // Suppression d'un document
  deleteDocument: async (documentId) => {
    try {
      const response = await api.delete(`/documents/${documentId}`)
      return response.data
    } catch (error) {
      console.error('Erreur lors de la suppression du document:', error)
      throw error
    }
  }
}

// Service pour les médecins
export const doctorService = {
  // Récupérer tous les médecins
  getAllDoctors: async () => {
    try {
      const response = await api.get('/doctors');
      if (!response.data || !response.data.status) {
        throw new Error('Format de réponse invalide');
      }
      if (response.data.status === 'error') {
        throw new Error(response.data.message || 'Erreur lors de la récupération des médecins');
      }
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des médecins:', error);
      throw error;
    }
  }
}
// Service pour les créneaux horaires
export const slotService = {
  // Récupérer les créneaux suggérés
  getSuggestedSlots: async (doctorId, date) => {
    try {
      const response = await api.get(`/doctors/${doctorId}/slots/suggested`, {
        params: { date }
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des créneaux:', error);
      throw error;
    }
  },

  // Vérifier la disponibilité d'un créneau
  checkAvailability: async (doctorId, slotId) => {
    try {
      const response = await api.get(`/doctors/${doctorId}/slots/${slotId}/availability`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la vérification du créneau:', error);
      throw error;
    }
  },

  // Réserver un créneau
  bookSlot: async (doctorId, slotId, appointmentData) => {
    try {
      const response = await api.post(
        `/doctors/${doctorId}/slots/${slotId}/book`,
        appointmentData
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la réservation:', error);
      throw error;
    }
  }
};

export default api