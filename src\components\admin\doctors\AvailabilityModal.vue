<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Disponibilités - Dr. {{ doctor?.fullName }}</h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="availability-content">
        <div class="days-grid">
          <div v-for="day in weekDays" :key="day" class="day-settings">
            <div class="day-header">
              <h3>{{ day }}</h3>
              <label class="toggle">
                <input 
                  type="checkbox"
                  v-model="availability[day].enabled"
                  @change="updateAvailability(day)"
                >
                <span class="slider"></span>
              </label>
            </div>

            <div v-if="availability[day].enabled" class="time-range">
              <div class="time-input">
                <label>Début</label>
                <input 
                  type="time"
                  v-model="availability[day].start"
                  @change="updateAvailability(day)"
                >
              </div>
              <div class="time-input">
                <label>Fin</label>
                <input 
                  type="time"
                  v-model="availability[day].end"
                  @change="updateAvailability(day)"
                >
              </div>
              <div class="break-time">
                <label>Pause déjeuner</label>
                <div class="break-inputs">
                  <input 
                    type="time"
                    v-model="availability[day].breakStart"
                    @change="updateAvailability(day)"
                  >
                  <span>à</span>
                  <input 
                    type="time"
                    v-model="availability[day].breakEnd"
                    @change="updateAvailability(day)"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h3>Paramètres généraux</h3>
          <div class="setting-item">
            <label>Durée des consultations</label>
            <select v-model="defaultDuration" @change="updateSettings">
              <option value="15">15 minutes</option>
              <option value="20">20 minutes</option>
              <option value="30">30 minutes</option>
              <option value="45">45 minutes</option>
              <option value="60">1 heure</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Intervalle entre les RDV</label>
            <select v-model="breakBetween" @change="updateSettings">
              <option value="0">Aucun</option>
              <option value="5">5 minutes</option>
              <option value="10">10 minutes</option>
              <option value="15">15 minutes</option>
            </select>
          </div>
        </div>

        <div class="actions">
          <button class="btn-secondary" @click="$emit('close')">Annuler</button>
          <button class="btn-primary" @click="saveAvailability">Enregistrer</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  doctor: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])

const weekDays = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi']
const defaultDuration = ref('30')
const breakBetween = ref('5')

const availability = ref(
  weekDays.reduce((acc, day) => {
    acc[day] = {
      enabled: true,
      start: '09:00',
      end: '17:00',
      breakStart: '12:00',
      breakEnd: '13:00'
    }
    return acc
  }, {})
)

const updateAvailability = (day) => {
  console.log('Mise à jour disponibilité:', day, availability.value[day])
}

const updateSettings = () => {
  console.log('Mise à jour paramètres:', {
    duration: defaultDuration.value,
    break: breakBetween.value
  })
}

const saveAvailability = () => {
  emit('save', {
    availability: availability.value,
    settings: {
      defaultDuration: defaultDuration.value,
      breakBetween: breakBetween.value
    }
  })
}

onMounted(() => {
  // Charger les disponibilités existantes si nécessaire
  if (props.doctor.availability) {
    availability.value = { ...props.doctor.availability }
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--admin-dark);
}

.close-btn {
  background: none;
  border: none;
  color: var(--admin-gray);
  cursor: pointer;
  font-size: 1.25rem;
}

.days-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.day-settings {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.day-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--admin-dark);
}

.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--admin-blue);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.time-range {
  display: grid;
  gap: 1rem;
}

.time-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.time-input label {
  font-size: 0.875rem;
  color: var(--admin-gray);
}

.time-input input {
  padding: 0.5rem;
  border: 1px solid var(--admin-border);
  border-radius: 6px;
}

.break-time {
  margin-top: 0.5rem;
}

.break-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.break-inputs span {
  color: var(--admin-gray);
}

.settings-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.settings-section h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: var(--admin-dark);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.setting-item label {
  color: var(--admin-gray);
}

.setting-item select {
  padding: 0.5rem;
  border: 1px solid var(--admin-border);
  border-radius: 6px;
  min-width: 120px;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

.btn-primary {
  background: var(--admin-blue);
  color: white;
  border: none;
}

.btn-secondary {
  background: white;
  color: var(--admin-dark);
  border: 1px solid var(--admin-border);
}

@media (max-width: 768px) {
  .time-range {
    grid-template-columns: 1fr;
  }

  .break-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}
</style> 