<?php

class SlotType {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Récupérer tous les types de créneaux
     */
    public function getAll() {
        try {
            $query = "
                SELECT * FROM types_creneaux 
                ORDER BY duree ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des types de créneaux: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer un type de créneau par ID
     */
    public function getById($id) {
        try {
            $query = "
                SELECT * FROM types_creneaux 
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération du type de créneau: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Créer un nouveau type de créneau
     */
    public function create($typeData) {
        try {
            $query = "
                INSERT INTO types_creneaux 
                (nom, duree, description, couleur, prix_base, actif)
                VALUES (?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $typeData['nom'],
                $typeData['duree'],
                $typeData['description'] ?? null,
                $typeData['couleur'] ?? '#3b82f6',
                $typeData['prix_base'] ?? 0,
                $typeData['actif'] ?? 1
            ]);
            
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log("Erreur lors de la création du type de créneau: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Mettre à jour un type de créneau
     */
    public function update($id, $typeData) {
        try {
            $query = "
                UPDATE types_creneaux 
                SET nom = ?, duree = ?, description = ?, couleur = ?, prix_base = ?, actif = ?
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $typeData['nom'],
                $typeData['duree'],
                $typeData['description'],
                $typeData['couleur'],
                $typeData['prix_base'],
                $typeData['actif'],
                $id
            ]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la mise à jour du type de créneau: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Supprimer un type de créneau
     */
    public function delete($id) {
        try {
            // Vérifier s'il y a des créneaux utilisant ce type
            $checkQuery = "
                SELECT COUNT(*) as count 
                FROM creneaux_suggeres 
                WHERE type_creneau_id = ?
            ";
            
            $stmt = $this->db->prepare($checkQuery);
            $stmt->execute([$id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] > 0) {
                throw new Exception("Impossible de supprimer ce type : des créneaux l'utilisent encore");
            }
            
            $query = "DELETE FROM types_creneaux WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression du type de créneau: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Récupérer les types de créneaux actifs
     */
    public function getActive() {
        try {
            $query = "
                SELECT * FROM types_creneaux 
                WHERE actif = 1 
                ORDER BY duree ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des types actifs: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les statistiques d'utilisation des types
     */
    public function getUsageStats() {
        try {
            $query = "
                SELECT 
                    tc.*,
                    COUNT(cs.id) as nb_creneaux_utilises,
                    COUNT(CASE WHEN cs.accepte = 1 THEN 1 END) as nb_creneaux_acceptes
                FROM types_creneaux tc
                LEFT JOIN creneaux_suggeres cs ON tc.id = cs.type_creneau_id
                GROUP BY tc.id
                ORDER BY tc.duree ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Initialiser les types de créneaux par défaut
     */
    public function initializeDefaultTypes() {
        try {
            // Vérifier si des types existent déjà
            $checkQuery = "SELECT COUNT(*) as count FROM types_creneaux";
            $stmt = $this->db->prepare($checkQuery);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] > 0) {
                return false; // Types déjà initialisés
            }
            
            $defaultTypes = [
                [
                    'nom' => 'Court',
                    'duree' => 15,
                    'description' => 'Consultation rapide, suivi simple',
                    'couleur' => '#10b981',
                    'prix_base' => 25,
                    'actif' => 1
                ],
                [
                    'nom' => 'Standard',
                    'duree' => 30,
                    'description' => 'Consultation standard, examen général',
                    'couleur' => '#3b82f6',
                    'prix_base' => 50,
                    'actif' => 1
                ],
                [
                    'nom' => 'Long',
                    'duree' => 60,
                    'description' => 'Consultation approfondie, premier rendez-vous',
                    'couleur' => '#f59e0b',
                    'prix_base' => 80,
                    'actif' => 1
                ]
            ];
            
            foreach ($defaultTypes as $type) {
                $this->create($type);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Erreur lors de l'initialisation des types par défaut: " . $e->getMessage());
            throw $e;
        }
    }
}
?>
