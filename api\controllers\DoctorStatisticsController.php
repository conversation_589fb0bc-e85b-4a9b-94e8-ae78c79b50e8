<?php

class DoctorStatisticsController {
    private $pdo;

    public function __construct() {
        $this->pdo = Database::getInstance()->getConnection();
    }

    public function getStatistics($userId) {
        try {
            // Récupérer l'ID du médecin depuis l'ID utilisateur
            $doctorId = $this->getDoctorIdFromUserId($userId);

            if (!$doctorId) {
                return [
                    'totalPatients' => 0,
                    'monthlyAppointments' => 0,
                    'completionRate' => 0,
                    'averageConsultationTime' => 30,
                    'trends' => []
                ];
            }

            // Nombre total de patients uniques
            $totalPatients = $this->getTotalPatients($doctorId);

            // Rendez-vous mensuels
            $monthlyAppointments = $this->getMonthlyAppointments($doctorId);

            // Taux de présence
            $completionRate = $this->getCompletionRate($doctorId);

            // Durée moyenne des consultations
            $averageTime = $this->getAverageConsultationTime($doctorId);

            // Tendances et évolution
            $trends = $this->getAppointmentTrends($doctorId);

            return [
                'totalPatients' => $totalPatients,
                'monthlyAppointments' => $monthlyAppointments,
                'completionRate' => $completionRate,
                'averageConsultationTime' => $averageTime,
                'trends' => $trends
            ];
        } catch (Exception $e) {
            throw new Exception("Erreur lors de la récupération des statistiques: " . $e->getMessage());
        }
    }

    private function getDoctorIdFromUserId($userId) {
        $stmt = $this->pdo->prepare("
            SELECT id FROM medecins WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        return $result ? $result['id'] : null;
    }

    private function getTotalPatients($doctorId) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(DISTINCT id_patient) as total 
            FROM rendez_vous 
            WHERE id_medecin = ?
        ");
        $stmt->execute([$doctorId]);
        return $stmt->fetch()['total'];
    }

    private function getMonthlyAppointments($doctorId) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as total 
            FROM rendez_vous 
            WHERE id_medecin = ? 
            AND MONTH(date_rendez_vous) = MONTH(CURRENT_DATE())
            AND YEAR(date_rendez_vous) = YEAR(CURRENT_DATE())
        ");
        $stmt->execute([$doctorId]);
        return $stmt->fetch()['total'];
    }

    private function getCompletionRate($doctorId) {
        $stmt = $this->pdo->prepare("
            SELECT
                COUNT(CASE WHEN statut = 'termine' THEN 1 END) * 100.0 / COUNT(*) as rate
            FROM rendez_vous
            WHERE id_medecin = ?
            AND date_rendez_vous < CURRENT_TIMESTAMP
        ");
        $stmt->execute([$doctorId]);
        $result = $stmt->fetch();
        return $result && $result['rate'] ? round($result['rate'], 1) : 0;
    }

    private function getAverageConsultationTime($doctorId) {
        $stmt = $this->pdo->prepare("
            SELECT AVG(duree) as avg_time
            FROM rendez_vous
            WHERE id_medecin = ?
            AND statut = 'termine'
        ");
        $stmt->execute([$doctorId]);
        $result = $stmt->fetch();
        return $result && $result['avg_time'] ? round($result['avg_time']) : 30;
    }

    private function getAppointmentTrends($doctorId) {
        // Tendance sur les 6 derniers mois
        $stmt = $this->pdo->prepare("
            SELECT 
                DATE_FORMAT(date_rendez_vous, '%Y-%m') as month,
                COUNT(*) as total
            FROM rendez_vous 
            WHERE id_medecin = ?
            AND date_rendez_vous >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(date_rendez_vous, '%Y-%m')
            ORDER BY month ASC
        ");
        $stmt->execute([$doctorId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getSpecialityStats($doctorId) {
        $stmt = $this->pdo->prepare("
            SELECT 
                type_consultation,
                COUNT(*) as total,
                AVG(duree) as avg_duration
            FROM rendez_vous 
            WHERE id_medecin = ?
            GROUP BY type_consultation
        ");
        $stmt->execute([$doctorId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getTimeSlotAnalysis($doctorId) {
        $stmt = $this->pdo->prepare("
            SELECT 
                HOUR(date_rendez_vous) as hour,
                COUNT(*) as total
            FROM rendez_vous 
            WHERE id_medecin = ?
            GROUP BY HOUR(date_rendez_vous)
            ORDER BY hour
        ");
        $stmt->execute([$doctorId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} 