import api from './api'

export const notificationService = {
  /**
   * <PERSON><PERSON><PERSON><PERSON>rer les statistiques des notifications
   */
  async getStats() {
    try {
      const response = await api.get('/notifications/stats')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des stats notifications:', error)
      throw error
    }
  },

  /**
   * Traiter les notifications en attente
   */
  async processNotifications() {
    try {
      const response = await api.post('/notifications/process')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors du traitement des notifications:', error)
      throw error
    }
  },

  /**
   * Créer les rappels automatiques
   */
  async createReminders() {
    try {
      const response = await api.post('/notifications/reminders')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la création des rappels:', error)
      throw error
    }
  },

  /**
   * Nettoyer les anciennes notifications
   */
  async cleanOldNotifications() {
    try {
      const response = await api.post('/notifications/clean')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage:', error)
      throw error
    }
  },

  /**
   * Créer les notifications pour un RDV spécifique
   */
  async createAppointmentNotifications(rdvId) {
    try {
      const response = await api.post(`/notifications/appointment/${rdvId}`)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la création des notifications RDV:', error)
      throw error
    }
  }
}
