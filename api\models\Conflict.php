<?php

class Conflict {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Enregistrer un nouveau conflit en base de données
     */
    public function create($conflictData) {
        try {
            $query = "
                INSERT INTO conflits 
                (rdv_1_id, rdv_2_id, medecin_id, type_conflit, severite, description, ecart_minutes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    severite = VALUES(severite),
                    description = VALUES(description),
                    ecart_minutes = VALUES(ecart_minutes),
                    detecte_le = CURRENT_TIMESTAMP
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $conflictData['rdv_1_id'],
                $conflictData['rdv_2_id'],
                $conflictData['medecin_id'],
                $conflictData['type_conflit'],
                $conflictData['severite'],
                $conflictData['description'],
                $conflictData['ecart_minutes']
            ]);
            
            return $this->db->lastInsertId() ?: $this->getConflictId($conflictData);
        } catch (PDOException $e) {
            error_log("Erreur lors de la création du conflit: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Récupérer l'ID d'un conflit existant
     */
    private function getConflictId($conflictData) {
        $query = "
            SELECT id FROM conflits 
            WHERE rdv_1_id = ? AND rdv_2_id = ? AND type_conflit = ?
        ";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            $conflictData['rdv_1_id'],
            $conflictData['rdv_2_id'],
            $conflictData['type_conflit']
        ]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }
    
    /**
     * Récupérer tous les conflits actifs
     */
    public function getActiveConflicts($medecinId = null) {
        try {
            // Vérifier si la table conflits existe
            $stmt = $this->db->query("SHOW TABLES LIKE 'conflits'");
            if (!$stmt->fetch()) {
                // Table n'existe pas, retourner un tableau vide
                return [];
            }

            $query = "
                SELECT c.*, rv.id_medecin, rv.date_rendez_vous,
                       CONCAT(p.prenom, ' ', p.nom) as patient_nom,
                       CONCAT('Dr. ', m.prenom, ' ', m.nom) as medecin_nom
                FROM conflits c
                INNER JOIN rendez_vous rv ON c.id_rendez_vous = rv.id
                LEFT JOIN patients p ON rv.id_patient = p.id
                LEFT JOIN medecins m ON rv.id_medecin = m.id
                WHERE c.resolu = 0
            ";
            $params = [];

            if ($medecinId) {
                $query .= " AND rv.id_medecin = ?";
                $params[] = $medecinId;
            }

            $query .= " ORDER BY c.gravite DESC, c.detecte_le ASC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des conflits: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les statistiques des conflits
     */
    public function getConflictStats($medecinId = null) {
        try {
            // Vérifier si la table conflits existe
            $stmt = $this->db->query("SHOW TABLES LIKE 'conflits'");
            if (!$stmt->fetch()) {
                // Table n'existe pas, retourner des stats vides
                return [
                    'total' => 0,
                    'actifs' => 0,
                    'resolus' => 0,
                    'expires' => 0,
                    'critiques' => 0,
                    'eleves' => 0,
                    'temps_resolution_moyen' => 0
                ];
            }

            $query = "
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN c.resolu = 0 THEN 1 ELSE 0 END) as actifs,
                    SUM(CASE WHEN c.resolu = 1 THEN 1 ELSE 0 END) as resolus,
                    0 as expires,
                    SUM(CASE WHEN c.gravite = 'critique' AND c.resolu = 0 THEN 1 ELSE 0 END) as critiques,
                    SUM(CASE WHEN c.gravite = 'elevee' AND c.resolu = 0 THEN 1 ELSE 0 END) as eleves,
                    AVG(CASE WHEN c.resolu = 1 AND c.resolu_le IS NOT NULL
                        THEN TIMESTAMPDIFF(MINUTE, c.detecte_le, c.resolu_le)
                        ELSE NULL END) as temps_resolution_moyen
                FROM conflits c
            ";
            $params = [];

            if ($medecinId) {
                $query .= " INNER JOIN rendez_vous rv ON c.id_rendez_vous = rv.id WHERE rv.id_medecin = ?";
                $params = [$medecinId];
            }

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);

            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des stats: " . $e->getMessage());
            return [
                'total' => 0,
                'actifs' => 0,
                'resolus' => 0,
                'expires' => 0,
                'critiques' => 0,
                'eleves' => 0,
                'temps_resolution_moyen' => 0
            ];
        }
    }
    
    /**
     * Résoudre un conflit
     */
    public function resolve($conflictId, $resolutionData) {
        try {
            $query = "
                UPDATE conflits
                SET resolu = 1,
                    notes_resolution = ?,
                    resolu_le = CURRENT_TIMESTAMP
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $resolutionData['notes'] ?? 'Conflit résolu',
                $conflictId
            ]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la résolution du conflit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Marquer un conflit comme ignoré
     */
    public function ignore($conflictId, $userId = null) {
        try {
            $query = "
                UPDATE conflits
                SET resolu = 1,
                    notes_resolution = 'Conflit ignoré',
                    resolu_le = CURRENT_TIMESTAMP
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$conflictId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de l'ignorance du conflit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Supprimer les conflits liés à un rendez-vous
     */
    public function deleteByAppointment($appointmentId) {
        try {
            $query = "
                DELETE FROM conflits
                WHERE id_rendez_vous = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$appointmentId]);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression des conflits: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Détecter et enregistrer les conflits pour un médecin à une date donnée
     */
    public function detectAndSaveConflicts($medecinId, $date = null) {
        try {
            $date = $date ?: date('Y-m-d');
            
            // Récupérer les rendez-vous du médecin pour la date
            $query = "
                SELECT
                    rdv.*,
                    CONCAT(p.prenom, ' ', p.nom) as patient_nom
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                WHERE rdv.id_medecin = ?
                AND DATE(rdv.date_rendez_vous) = ?
                AND rdv.statut != 'annule'
                ORDER BY rdv.date_rendez_vous ASC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$medecinId, $date]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $conflictsCreated = 0;

            // Détecter les conflits entre rendez-vous consécutifs
            for ($i = 0; $i < count($appointments) - 1; $i++) {
                $current = $appointments[$i];
                $next = $appointments[$i + 1];
                
                $currentEnd = new DateTime($current['date_rendez_vous']);
                $currentEnd->add(new DateInterval('PT' . ($current['duree'] ?? 30) . 'M'));
                
                $nextStart = new DateTime($next['date_rendez_vous']);
                
                if ($currentEnd > $nextStart) {
                    $gapMinutes = ($currentEnd->getTimestamp() - $nextStart->getTimestamp()) / 60;
                    
                    // Déterminer la sévérité
                    $severite = 'moyenne';
                    if ($gapMinutes > 15) {
                        $severite = 'critique';
                    } elseif ($gapMinutes > 5) {
                        $severite = 'elevee';
                    }
                    
                    // Créer le conflit
                    $conflictData = [
                        'rdv_1_id' => $current['id'],
                        'rdv_2_id' => $next['id'],
                        'medecin_id' => $medecinId,
                        'type_conflit' => 'chevauchement',
                        'severite' => $severite,
                        'description' => sprintf(
                            'Chevauchement de %.0f minutes entre %s (%s) et %s (%s)',
                            $gapMinutes,
                            $current['patient_nom'],
                            $current['type'],
                            $next['patient_nom'],
                            $next['type']
                        ),
                        'ecart_minutes' => -$gapMinutes
                    ];
                    
                    $this->create($conflictData);
                    $conflictsCreated++;
                }
            }
            
            return $conflictsCreated;
        } catch (Exception $e) {
            error_log("Erreur lors de la détection des conflits: " . $e->getMessage());
            return 0;
        }
    }
}
