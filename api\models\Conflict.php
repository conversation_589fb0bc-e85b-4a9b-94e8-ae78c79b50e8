<?php

class Conflict {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Enregistrer un nouveau conflit en base de données
     */
    public function create($conflictData) {
        try {
            $query = "
                INSERT INTO conflits 
                (rdv_1_id, rdv_2_id, medecin_id, type_conflit, severite, description, ecart_minutes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    severite = VALUES(severite),
                    description = VALUES(description),
                    ecart_minutes = VALUES(ecart_minutes),
                    detecte_le = CURRENT_TIMESTAMP
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $conflictData['rdv_1_id'],
                $conflictData['rdv_2_id'],
                $conflictData['medecin_id'],
                $conflictData['type_conflit'],
                $conflictData['severite'],
                $conflictData['description'],
                $conflictData['ecart_minutes']
            ]);
            
            return $this->db->lastInsertId() ?: $this->getConflictId($conflictData);
        } catch (PDOException $e) {
            error_log("Erreur lors de la création du conflit: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Récupérer l'ID d'un conflit existant
     */
    private function getConflictId($conflictData) {
        $query = "
            SELECT id FROM conflits 
            WHERE rdv_1_id = ? AND rdv_2_id = ? AND type_conflit = ?
        ";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            $conflictData['rdv_1_id'],
            $conflictData['rdv_2_id'],
            $conflictData['type_conflit']
        ]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }
    
    /**
     * Récupérer tous les conflits actifs
     */
    public function getActiveConflicts($medecinId = null) {
        try {
            $query = "SELECT * FROM v_conflits_actifs";
            $params = [];
            
            if ($medecinId) {
                $query .= " WHERE medecin_id = ?";
                $params[] = $medecinId;
            }
            
            $query .= " ORDER BY severite DESC, detecte_le ASC";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des conflits: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les statistiques des conflits
     */
    public function getConflictStats($medecinId = null) {
        try {
            $whereClause = $medecinId ? "WHERE medecin_id = ?" : "";
            $params = $medecinId ? [$medecinId] : [];
            
            $query = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN statut = 'actif' THEN 1 ELSE 0 END) as actifs,
                    SUM(CASE WHEN statut = 'resolu' THEN 1 ELSE 0 END) as resolus,
                    SUM(CASE WHEN statut = 'expire' THEN 1 ELSE 0 END) as expires,
                    SUM(CASE WHEN severite = 'critique' AND statut = 'actif' THEN 1 ELSE 0 END) as critiques,
                    SUM(CASE WHEN severite = 'elevee' AND statut = 'actif' THEN 1 ELSE 0 END) as eleves,
                    AVG(CASE WHEN statut = 'resolu' AND resolu_le IS NOT NULL 
                        THEN TIMESTAMPDIFF(MINUTE, detecte_le, resolu_le) 
                        ELSE NULL END) as temps_resolution_moyen
                FROM conflits 
                $whereClause
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des stats: " . $e->getMessage());
            return [
                'total' => 0,
                'actifs' => 0,
                'resolus' => 0,
                'expires' => 0,
                'critiques' => 0,
                'eleves' => 0,
                'temps_resolution_moyen' => 0
            ];
        }
    }
    
    /**
     * Résoudre un conflit
     */
    public function resolve($conflictId, $resolutionData) {
        try {
            $query = "
                UPDATE conflits 
                SET statut = 'resolu',
                    methode_resolution = ?,
                    resolu_par = ?,
                    resolu_le = CURRENT_TIMESTAMP,
                    notes_resolution = ?
                WHERE id = ? AND statut = 'actif'
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $resolutionData['methode'],
                $resolutionData['resolu_par'] ?? null,
                $resolutionData['notes'] ?? null,
                $conflictId
            ]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la résolution du conflit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Marquer un conflit comme ignoré
     */
    public function ignore($conflictId, $userId = null) {
        try {
            $query = "
                UPDATE conflits 
                SET statut = 'ignore',
                    methode_resolution = 'ignore',
                    resolu_par = ?,
                    resolu_le = CURRENT_TIMESTAMP
                WHERE id = ? AND statut = 'actif'
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$userId, $conflictId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de l'ignorance du conflit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Supprimer les conflits liés à un rendez-vous
     */
    public function deleteByAppointment($appointmentId) {
        try {
            $query = "
                DELETE FROM conflits 
                WHERE rdv_1_id = ? OR rdv_2_id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$appointmentId, $appointmentId]);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression des conflits: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Détecter et enregistrer les conflits pour un médecin à une date donnée
     */
    public function detectAndSaveConflicts($medecinId, $date = null) {
        try {
            $date = $date ?: date('Y-m-d');
            
            // Récupérer les rendez-vous du médecin pour la date
            $query = "
                SELECT
                    rdv.*,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                WHERE rdv.id_medecin = ? 
                AND DATE(rdv.date_rendez_vous) = ?
                AND rdv.statut != 'annule'
                ORDER BY rdv.date_rendez_vous ASC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$medecinId, $date]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $conflictsCreated = 0;

            // Détecter les conflits entre rendez-vous consécutifs
            for ($i = 0; $i < count($appointments) - 1; $i++) {
                $current = $appointments[$i];
                $next = $appointments[$i + 1];
                
                $currentEnd = new DateTime($current['date_rendez_vous']);
                $currentEnd->add(new DateInterval('PT' . ($current['duree'] ?? 30) . 'M'));
                
                $nextStart = new DateTime($next['date_rendez_vous']);
                
                if ($currentEnd > $nextStart) {
                    $gapMinutes = ($currentEnd->getTimestamp() - $nextStart->getTimestamp()) / 60;
                    
                    // Déterminer la sévérité
                    $severite = 'moyenne';
                    if ($gapMinutes > 15) {
                        $severite = 'critique';
                    } elseif ($gapMinutes > 5) {
                        $severite = 'elevee';
                    }
                    
                    // Créer le conflit
                    $conflictData = [
                        'rdv_1_id' => $current['id'],
                        'rdv_2_id' => $next['id'],
                        'medecin_id' => $medecinId,
                        'type_conflit' => 'chevauchement',
                        'severite' => $severite,
                        'description' => sprintf(
                            'Chevauchement de %.0f minutes entre %s (%s) et %s (%s)',
                            $gapMinutes,
                            $current['patient_nom'],
                            $current['type'],
                            $next['patient_nom'],
                            $next['type']
                        ),
                        'ecart_minutes' => -$gapMinutes
                    ];
                    
                    $this->create($conflictData);
                    $conflictsCreated++;
                }
            }
            
            return $conflictsCreated;
        } catch (Exception $e) {
            error_log("Erreur lors de la détection des conflits: " . $e->getMessage());
            return 0;
        }
    }
}
