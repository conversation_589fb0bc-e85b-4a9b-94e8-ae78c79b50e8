<template>
  <div class="calendar-container">
    <div class="calendar-header">
      <div class="header-content">
        <div class="header-title">
          <div class="calendar-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div>
            <h1>Calendrier des Rendez-vous</h1>
            <p>Gérez vos rendez-vous en toute simplicité</p>
          </div>
        </div>
        
        <div class="month-navigation">
          <button @click="previousMonth" class="nav-button">
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <div class="month-display">
            <div class="month-name">{{ currentMonthName }}</div>
            <div class="year">{{ currentYear }}</div>
          </div>
          
          <button @click="nextMonth" class="nav-button">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="calendar-grid">
      <div class="day-names">
        <div v-for="day in dayNames" :key="day" class="day-name">
          {{ day }}
        </div>
      </div>
      
      <div class="days-grid">
        <div 
          v-for="day in calendarDays"
          :key="day.date"
          class="day-cell"
          :class="{
            'current-month': day.isCurrentMonth,
            'today': day.isToday,
            'other-month': !day.isCurrentMonth
          }"
          @click="day.isCurrentMonth && openDayView(day)"
        >
          <div class="day-header">
            <span class="day-number" :class="{ 'today-number': day.isToday }">
              {{ day.dayNumber }}
            </span>
            <span v-if="day.hasAppointments" class="appointment-count">
              {{ day.appointments.length }}
            </span>
          </div>
          
          <div class="appointments">
            <div 
              v-for="appointment in day.appointments.slice(0, 2)"
              :key="appointment.id"
              class="appointment"
              @click.stop="editAppointment(appointment)"
            >
              <div class="appointment-time">{{ appointment.time }}</div>
              <div class="appointment-name">{{ appointment.patientName }}</div>
            </div>
            
            <div 
              v-if="day.appointments.length > 2"
              class="more-appointments"
            >
              +{{ day.appointments.length - 2 }} autres
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="calendar-legend">
      <div class="legend-item">
        <div class="legend-marker today-marker"></div>
        <span>Aujourd'hui</span>
      </div>
      <div class="legend-item">
        <div class="legend-marker appointment-marker"></div>
        <span>Rendez-vous</span>
      </div>
      <div class="legend-item">
        <div class="legend-marker consultation-marker"></div>
        <span>Consultation</span>
      </div>
    </div>

    <button class="add-button" @click="openNewAppointment" title="Nouveau rendez-vous">
      <i class="fas fa-plus"></i>
    </button>

    <!-- Modal -->
    <transition name="modal">
      <div v-if="selectedAppointment" class="modal-overlay" @click.self="selectedAppointment = null">
        <div class="modal-content">
          <div class="modal-header">
            <h3>Détails du rendez-vous</h3>
            <button @click="selectedAppointment = null" class="close-button">✕</button>
          </div>
          
          <div class="modal-body">
            <div class="info-item">
              <i class="fas fa-user"></i>
              <span>{{ selectedAppointment.patientName }}</span>
            </div>
            
            <div class="info-item">
              <i class="fas fa-clock"></i>
              <span>{{ selectedAppointment.time }}</span>
            </div>
            
            <div class="info-item">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(selectedAppointment.date) }}</span>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="primary-button">Modifier</button>
            <button class="secondary-button">Supprimer</button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Modal d'ajout de rendez-vous -->
    <AppointmentEditor
      v-if="showAppointmentEditor"
      :appointment="{}"
      @close="showAppointmentEditor = false"
      @save="handleSaveAppointment"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import AppointmentEditor from './AppointmentEditor.vue'

const appointmentStore = useAppointmentStore()
const currentDate = ref(new Date())
const selectedAppointment = ref(null)
const showAppointmentEditor = ref(false)
const dayNames = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim']

// Charger les rendez-vous au montage du composant
onMounted(async () => {
  await appointmentStore.fetchAppointments()
})

// Obtenir les rendez-vous pour la date sélectionnée
const currentDayAppointments = computed(() => {
  return appointmentStore.getAppointmentsByDate(currentDate.value)
})

const currentMonthName = computed(() => {
  return currentDate.value.toLocaleString('fr-FR', { month: 'long' })
    .charAt(0).toUpperCase() + currentDate.value.toLocaleString('fr-FR', { month: 'long' }).slice(1)
})

const currentYear = computed(() => {
  return currentDate.value.getFullYear()
})

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const today = new Date()
  
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  // Calcul pour commencer le lundi
  const startDay = firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1
  const startDate = new Date(firstDay)
  startDate.setDate(firstDay.getDate() - startDay)
  
  const days = []
  const currentDateIter = new Date(startDate)
  
  // Générer 42 jours (6 semaines)
  for (let i = 0; i < 42; i++) {
    const isCurrentMonth = currentDateIter.getMonth() === month
    const isToday = 
      currentDateIter.getDate() === today.getDate() &&
      currentDateIter.getMonth() === today.getMonth() &&
      currentDateIter.getFullYear() === today.getFullYear()

    const dayAppointments = currentDayAppointments.value.filter(apt => 
      apt.date.getDate() === currentDateIter.getDate() &&
      apt.date.getMonth() === currentDateIter.getMonth() &&
      apt.date.getFullYear() === currentDateIter.getFullYear()
    )

    days.push({
      date: new Date(currentDateIter),
      dayNumber: currentDateIter.getDate(),
      isCurrentMonth,
      isToday,
      appointments: dayAppointments,
      hasAppointments: dayAppointments.length > 0
    })

    currentDateIter.setDate(currentDateIter.getDate() + 1)
  }

  return days
})

function previousMonth() {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() - 1,
    1
  )
}

function nextMonth() {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() + 1,
    1
  )
}

function openDayView(day) {
  console.log('Ouvrir la vue du jour:', day.date)
}

function editAppointment(appointment) {
  selectedAppointment.value = { ...appointment }
}

function formatDate(date) {
  return date.toLocaleDateString('fr-FR', { weekday: 'long', day: 'numeric', month: 'long' })
}

const openNewAppointment = () => {
  showAppointmentEditor.value = true
}

const handleSaveAppointment = async (appointmentData) => {
  try {
    console.log('Données reçues dans AdminCalendar:', appointmentData)

    const result = await appointmentStore.createAppointment(appointmentData)

    if (result.success) {
      showAppointmentEditor.value = false
      // Notification de succès
      window.$notify?.success('Rendez-vous créé avec succès')
      // Recharger les rendez-vous
      await appointmentStore.fetchAppointments()
    } else {
      window.$notify?.error('Erreur: ' + (result.error || 'Erreur inconnue'))
    }
  } catch (error) {
    // Notification d'erreur
    window.$notify?.error('Erreur lors de la création du rendez-vous')
    console.error('Erreur lors de la sauvegarde du rendez-vous:', error)
  }
}
</script>

<style scoped>
/* Styles de base */
.calendar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* En-tête */
.calendar-header {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-title p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.calendar-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

/* Navigation du mois */
.month-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.month-display {
  text-align: center;
}

.month-name {
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.year {
  font-size: 16px;
  color: #666;
}

.nav-button {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: white;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #4b5563;
}

.nav-button:hover {
  background: #f3f4f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Grille du calendrier */
.calendar-grid {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.day-names {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 1px solid #e5e7eb;
}

.day-name {
  padding: 12px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  text-transform: uppercase;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.day-cell {
  min-height: 120px;
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px;
  transition: all 0.2s ease;
}

.day-cell.current-month {
  background-color: white;
  cursor: pointer;
}

.day-cell.current-month:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.day-cell.other-month {
  background-color: #f9fafb;
  color: #9ca3af;
}

.day-cell.today {
  background: linear-gradient(135deg, #e0f2fe, #f0f9ff);
  border-color: #bfdbfe;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.day-number {
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.today-number {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  font-weight: 600;
}

.appointment-count {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #10b981, #06b6d4);
  color: white;
  font-size: 12px;
  font-weight: 600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.appointments {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.appointment {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border-radius: 6px;
  padding: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.appointment:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.appointment-time {
  font-weight: 600;
  font-size: 11px;
}

.appointment-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-appointments {
  font-size: 11px;
  color: #6b7280;
  text-align: center;
  padding: 2px;
}

/* Légende */
.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4b5563;
}

.legend-marker {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.today-marker {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
}

.appointment-marker {
  background: linear-gradient(135deg, #10b981, #06b6d4);
  border-radius: 50%;
}

.consultation-marker {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
}

/* Bouton ajouter */
.add-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
  z-index: 10;
}

.add-button:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.modal-content {
  background-color: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.close-button:hover {
  color: #111827;
}

.modal-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-item i {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.primary-button {
  flex: 1;
  padding: 10px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.secondary-button {
  flex: 1;
  padding: 10px;
  background: white;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-button:hover {
  background: #f9fafb;
  transform: translateY(-1px);
}

/* Animations */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive */
@media (min-width: 768px) {
  .header-content {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .day-cell {
    min-height: 140px;
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .calendar-legend {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
  
  .day-name {
    font-size: 12px;
    padding: 8px 4px;
  }
  
  .day-number {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }
  
  .appointment {
    padding: 4px;
    font-size: 11px;
  }
}
</style>