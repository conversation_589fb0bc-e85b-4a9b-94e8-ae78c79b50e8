<template>
  <div class="patient-management">
    <!-- En-tête avec boutons -->
    <div class="section-header">
      <h2>Gestion des Patients</h2>
      <div class="actions">
        <button class="btn-primary" @click="showAddPatientModal = true">
          <i class="fas fa-user-plus"></i> Nouveau Patient
        </button>
        <button class="btn-secondary" @click="exportPatients" :disabled="isExporting">
          <i class="fas fa-file-export"></i>
          <svg v-if="!isExporting" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <svg v-else class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isExporting ? 'Export en cours...' : 'Exporter CSV' }}
        </button>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="filters">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Rechercher un patient..."
        />
      </div>
      <div class="filter-options">
        <select v-model="filterStatus">
          <option value="">Tous les patients</option>
          <option value="active">Actifs</option>
          <option value="archived">Archivés</option>
          <option value="new">Nouveaux</option>
        </select>
      </div>
    </div>

    <!-- Message de chargement ou d'erreur -->
    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      Chargement des patients...
    </div>
    <div v-else-if="error" class="error-state">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <!-- Liste des patients -->
    <div v-else class="patients-grid">
      <div v-for="patient in filteredPatients" :key="patient.id" class="patient-card">
        <div class="patient-header">
          <div class="patient-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="patient-info">
            <h3>{{ patient.nom }} {{ patient.prenom }}</h3>
            <span class="contact-info">
              <i class="fas fa-envelope"></i> {{ patient.email || 'Non renseigné' }}
            </span>
            <span class="contact-info">
              <i class="fas fa-phone"></i> {{ patient.telephone || 'Non renseigné' }}
            </span>
          </div>
        </div>

        <div class="patient-details">
          <div class="medical-info">
            <h4>Informations médicales</h4>
            <div class="tags">
              <span v-if="patient.allergies" class="tag warning">
                <i class="fas fa-exclamation-triangle"></i>
                {{ patient.allergies.length }} allergie(s)
              </span>
              <span v-if="patient.medicaments" class="tag info">
                <i class="fas fa-pills"></i>
                {{ patient.medicaments.length }} traitement(s)
              </span>
            </div>
          </div>

          <div class="appointment-info">
            <h4>Rendez-vous</h4>
            <div class="appointment-stats">
              <div class="stat">
                <span class="stat-value">{{ patient.lastAppointment || 'Aucun' }}</span>
                <span class="stat-label">Dernier RDV</span>
              </div>
              <div class="stat">
                <span class="stat-value">{{ patient.nextAppointment || 'Aucun' }}</span>
                <span class="stat-label">Prochain RDV</span>
              </div>
            </div>
          </div>
        </div>

        <div class="patient-actions">
          <button @click="viewDetails(patient)" title="Détails">
            <i class="fas fa-eye"></i>
          </button>
          <button @click="editPatient(patient)" title="Modifier">
            <i class="fas fa-edit"></i>
          </button>
          <button @click="viewHistory(patient)" title="Historique">
            <i class="fas fa-history"></i>
          </button>
          <button @click="manageDocuments(patient)" title="Documents">
            <i class="fas fa-file-alt"></i>
          </button>
          <button @click="archivePatient(patient)" :title="patient.archived ? 'Restaurer' : 'Archiver'">
            <i :class="patient.archived ? 'fas fa-box-open' : 'fas fa-archive'"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Modales -->
    <PatientFormModal
      v-if="showAddPatientModal"
      :patient="selectedPatient"
      @save="savePatient"
      @close="closeAddPatientModal"
    />

    <PatientDetails
      v-if="showDetailsModal"
      :patient="selectedPatient"
      @close="closeDetailsModal"
      @edit="editPatient"
    />

    <HistoryModal
      v-if="showHistoryModal"
      :patient="selectedPatient"
      @close="closeHistoryModal"
    />

    <DocumentsModal
      v-if="showDocumentsModal"
      :patient="selectedPatient"
      @close="closeDocumentsModal"
    />

    <div class="flex justify-end space-x-4 mb-6 mt-4 px-4">
      <button 
        @click="exportPatients" 
        class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-md"
        :disabled="isExporting"
      >
        <svg v-if="!isExporting" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <svg v-else class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ isExporting ? 'Export en cours...' : 'Exporter CSV' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePatientStore } from '@/stores/patientStore'
import { useAuthStore } from '@/stores/authStore'
import PatientFormModal from './PatientFormModal.vue'
import HistoryModal from './HistoryModal.vue'
import DocumentsModal from './DocumentsModal.vue'
import PatientDetails from './PatientDetails.vue'
import { useApi } from '@/composables/useApi'
import { patientService } from '@/services/api'

// État
const searchQuery = ref('')
const filterStatus = ref('')
const showAddPatientModal = ref(false)
const showHistoryModal = ref(false)
const showDocumentsModal = ref(false)
const showDetailsModal = ref(false)
const selectedPatient = ref(null)
const loading = ref(false)
const error = ref(null)
const patients = ref([])
const isExporting = ref(false)

const patientStore = usePatientStore()
const authStore = useAuthStore()
const api = useApi()

// Computed
const filteredPatients = computed(() => {
  return patients.value.filter(patient => {
    const matchesSearch = (patient.nom + ' ' + patient.prenom).toLowerCase()
      .includes(searchQuery.value.toLowerCase())
    const matchesStatus = !filterStatus.value || 
      (filterStatus.value === 'archived' && patient.archived) ||
      (filterStatus.value === 'active' && !patient.archived) ||
      (filterStatus.value === 'new' && isNewPatient(patient))
    return matchesSearch && matchesStatus
  })
})

// Méthodes
const loadPatients = async () => {
  if (!authStore.isLoggedIn) {
    console.error('Utilisateur non connecté')
    return
  }

  loading.value = true
  error.value = null
  try {
    await patientStore.fetchAll()
    patients.value = patientStore.patients
  } catch (err) {
    console.error('Erreur chargement patients:', err)
    error.value = 'Erreur lors du chargement des patients: ' + err.message
    if (err.response?.status === 401) {
      await authStore.checkAuth()
    }
  } finally {
    loading.value = false
  }
}

// Watch pour l'état d'authentification
watch(() => authStore.isLoggedIn, (newValue) => {
  if (newValue) {
    loadPatients()
  }
})

const viewDetails = (patient) => {
  selectedPatient.value = { ...patient }
  showDetailsModal.value = true
}

const editPatient = (patient) => {
  selectedPatient.value = { ...patient }
  showAddPatientModal.value = true
}

const viewHistory = async (patient) => {
  try {
    selectedPatient.value = { ...patient }
    showHistoryModal.value = true
  } catch (err) {
    console.error('Erreur lors de l\'affichage de l\'historique:', err)
    error.value = 'Erreur lors de l\'affichage de l\'historique: ' + err.message
  }
}

const manageDocuments = async (patient) => {
  try {
    selectedPatient.value = { ...patient }
    showDocumentsModal.value = true
  } catch (err) {
    console.error('Erreur lors de l\'affichage des documents:', err)
    error.value = 'Erreur lors de l\'affichage des documents: ' + err.message
  }
}

const archivePatient = async (patient) => {
  if (!authStore.isLoggedIn) return

  try {
    await patientStore.archive(patient.id, !patient.archived)
    await loadPatients()
  } catch (err) {
    console.error('Erreur lors de l\'archivage:', err)
    error.value = `Erreur lors de ${patient.archived ? 'la restauration' : "l'archivage"}: ` + err.message
    if (err.response?.status === 401) {
      await authStore.checkAuth()
    }
  }
}

const savePatient = async (patientData) => {
  if (!authStore.isLoggedIn) return

  loading.value = true
  error.value = null
  try {
    if (patientData.id) {
      await patientStore.update(patientData.id, patientData)
    } else {
      await patientStore.create(patientData)
    }
    await loadPatients()
    showAddPatientModal.value = false
  } catch (err) {
    console.error('Erreur lors de la sauvegarde:', err)
    error.value = 'Erreur lors de la sauvegarde: ' + err.message
    if (err.response?.status === 401) {
      await authStore.checkAuth()
    }
  } finally {
    loading.value = false
  }
}

const exportPatients = async () => {
  if (!authStore.isLoggedIn || isExporting.value) return
  
  isExporting.value = true
  try {
    const response = await api.get('/patients/export', {
      responseType: 'blob',
      headers: {
        'Accept': 'text/csv'
      }
    })
    const url = window.URL.createObjectURL(new Blob([response.data], { type: 'text/csv' }))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `patients_${new Date().toISOString().split('T')[0]}.csv`)
    document.body.appendChild(link)
    link.click()
    window.URL.revokeObjectURL(url)
    link.remove()
  } catch (err) {
    console.error('Erreur export CSV:', err)
    error.value = 'Erreur lors de l\'export CSV des patients'
    if (err.response?.status === 401) {
      await authStore.checkAuth()
    }
  } finally {
    isExporting.value = false
  }
}

const isNewPatient = (patient) => {
  const oneMonthAgo = new Date()
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
  return new Date(patient.created_at) > oneMonthAgo
}

const downloadDocument = async (document) => {
  try {
    const response = await api.get(`/documents/${document.id}/download`, {
      responseType: 'blob'
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', document.nom_fichier)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  } catch (err) {
    error.value = 'Erreur lors du téléchargement du document: ' + err.message
  }
}

const uploadDocument = async (event, patient) => {
  try {
    const file = event.target.files[0]
    if (!file) return

    const formData = new FormData()
    formData.append('file', file)
    formData.append('name', file.name)
    formData.append('type', file.type)
    formData.append('description', 'Document patient')

    await api.post(`/patients/${patient.id}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    // Recharger les documents
    await loadPatientDocuments(patient.id)
  } catch (err) {
    error.value = 'Erreur lors de l\'upload du document: ' + err.message
  }
}

const deleteDocument = async (document) => {
  try {
    await api.delete(`/documents/${document.id}`)
    // Recharger les documents
    await loadPatientDocuments(document.patient_id)
  } catch (err) {
    error.value = 'Erreur lors de la suppression du document: ' + err.message
  }
}

const closeAddPatientModal = () => {
  showAddPatientModal.value = false
  selectedPatient.value = null
}

const closeHistoryModal = () => {
  showHistoryModal.value = false
  selectedPatient.value = null
}

const closeDocumentsModal = () => {
  showDocumentsModal.value = false
  selectedPatient.value = null
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedPatient.value = null
}

// Chargement initial
onMounted(async () => {
  if (authStore.isLoggedIn) {
    await loadPatients()
  }
})
</script>

<style scoped>
.patient-management {
  padding: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.actions {
  display: flex;
  gap: 1rem;
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background:rgb(34, 145, 40);
  color: white;
  border: none;
}

.btn-secondary {
  background:rgb(35, 153, 47);
  color: #ffffff;
  border: 1px solidrgb(90, 219, 73);
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

.filter-options select {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
}

.patients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.patient-card {
  background:rgb(196, 229, 200);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.patient-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.patient-avatar {
  width: 48px;
  height: 48px;
  background:rgb(57, 228, 66);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
}

.patient-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.contact-info {
  display: block;
  font-size: 0.9rem;
  color:rgb(120, 136, 158);
}

.contact-info i {
  margin-right: 0.5rem;
  width: 16px;
}

.medical-info, .appointment-info {
  margin-bottom: 1.5rem;
}

.medical-info h4, .appointment-info h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #64748b;
}

.tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.8rem;
}

.tag.warning {
  background: #fef3c7;
  color: #d97706;
}

.tag.info {
  background: #e0f2fe;
  color: #0284c7;
}

.appointment-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-weight: 500;
  font-size: 0.9rem;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: #64748b;
}

.patient-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.patient-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 8px;
  background-color: #f3f4f6;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
}

.patient-actions button:hover {
  background-color: #e5e7eb;
  color: #1f2937;
  transform: translateY(-1px);
}

.patient-actions button:active {
  transform: translateY(0);
}

.patient-actions button i {
  font-size: 1rem;
}

.patient-actions button[title="Modifier"]:hover {
  background-color: #dbeafe;
  color: #2563eb;
}

.patient-actions button[title="Historique"]:hover {
  background-color: #fef3c7;
  color: #d97706;
}

.patient-actions button[title="Documents"]:hover {
  background-color: #dcfce7;
  color: #16a34a;
}

.patient-actions button[title="Archiver"]:hover,
.patient-actions button[title="Restaurer"]:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.error-message {
  color: #dc2626;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.loading-spinner {
  color: #2563eb;
  font-size: 2rem;
}

.loading-state, .error-state {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.loading-state i, .error-state i {
  margin-right: 0.5rem;
}

.error-state {
  color: #ef4444;
}

@media (max-width: 640px) {
  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .actions {
    flex-direction: column;
  }

  .filters {
    flex-direction: column;
  }

  .patients-grid {
    grid-template-columns: 1fr;
  }
}
</style>