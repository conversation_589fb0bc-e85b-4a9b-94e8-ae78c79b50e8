<template>
  <div class="stat-card" :class="`border-${color}-500`">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500 uppercase">{{ title }}</p>
        <p class="text-3xl font-bold text-gray-800">{{ formattedValue }}</p>
      </div>
      <div class="icon-wrapper" :class="`bg-${color}-100 text-${color}-600`">
        <i class="fas" :class="icon"></i>
      </div>
    </div>
    <div v-if="trend" class="mt-4 flex items-center text-sm">
      <span class="trend-badge" :class="trendClasses">
        <i class="fas" :class="trendIcon"></i>
        {{ Math.abs(trend.value) }}%
      </span>
      <span class="ml-2 text-gray-500">{{ trend.period }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: { type: String, required: true },
  value: { type: [String, Number], required: true },
  icon: { type: String, required: true },
  color: { type: String, default: 'gray' },
  trend: { type: Object, default: null }
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString('fr-FR')
  }
  return props.value
})

const trendClasses = computed(() => {
  if (!props.trend) return ''
  if (props.trend.value > 0) return 'bg-green-100 text-green-800'
  if (props.trend.value < 0) return 'bg-red-100 text-red-800'
  return 'bg-gray-100 text-gray-800'
})

const trendIcon = computed(() => {
  if (!props.trend) return ''
  if (props.trend.value > 0) return 'fa-arrow-up'
  if (props.trend.value < 0) return 'fa-arrow-down'
  return 'fa-minus'
})
</script>

<style scoped>

.stat-card {
  background-color:rgb(23, 169, 52);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 10px;
  box-shadow: 0 4px 6px -1px rgba(17, 127, 13, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  border-left-width: 4px;
  transition: all 0.3s ease-in-out;
}
.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
}
.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
}
.icon-wrapper i {
  font-size: 1.5rem;
}
.trend-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-weight: 500;
}
.trend-badge i {
  margin-right: 0.25rem;
  font-size: 0.75rem;
}
/* Dans AdminStatCard.vue */
.card {
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  background: white;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Couleurs pour les différentes cartes */
.card-blue {
  border-left: 4px solid #3b82f6;
}

.card-green {
  border-left: 4px solid #10b981;
}

.card-yellow {
  border-left: 4px solid #f59e0b;
}

.card-indigo {
  border-left: 4px solid #6366f1;
}

</style>