<template>
  <div class="patient-form">
    <div class="form-header">
      <h2>{{ isEdit ? 'Modifier le Patient' : 'Nouveau Patient' }}</h2>
      <button @click="handleClose" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <div class="form-section">
        <h3>Informations Personnelles</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="firstName">Prénom</label>
            <input 
              id="firstName"
              v-model="formData.prenom"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label for="lastName">Nom</label>
            <input 
              id="lastName"
              v-model="formData.nom"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label for="birthDate">Date de Naissance</label>
            <input 
              id="birthDate"
              v-model="formData.date_naissance"
              type="date"
              required
            />
          </div>
          <div class="form-group">
            <label for="socialSecurityNumber">Numéro de Sécurité Sociale</label>
            <input 
              id="socialSecurityNumber"
              v-model="formData.numero_securite_sociale"
              type="text"
              required
              pattern="[0-9]{15}"
              title="Le numéro de sécurité sociale doit contenir 15 chiffres"
            />
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3>Contact</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="email">Email</label>
            <input 
              id="email"
              v-model="formData.email"
              type="email"
              required
            />
          </div>
          <div class="form-group">
            <label for="phone">Téléphone</label>
            <input 
              id="phone"
              v-model="formData.telephone"
              type="tel"
              required
              pattern="[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}"
              title="Le numéro de téléphone doit être au format XX XX XX XX XX"
            />
          </div>
          <div class="form-group full-width">
            <label for="address">Adresse</label>
            <textarea 
              id="address"
              v-model="formData.adresse"
              required
            ></textarea>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3>Informations Médicales</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="allergies">Allergies</label>
            <div class="tag-input">
              <input 
                id="allergies"
                v-model="newAllergy"
                type="text"
                @keydown.enter.prevent="addAllergy"
                placeholder="Appuyez sur Entrée pour ajouter"
              />
              <div class="tags">
                <span 
                  v-for="(allergy, index) in formData.allergies" 
                  :key="index"
                  class="tag"
                >
                  {{ allergy }}
                  <button type="button" @click="removeAllergy(index)">&times;</button>
                </span>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="medications">Médicaments</label>
            <div class="tag-input">
              <input 
                id="medications"
                v-model="newMedication"
                type="text"
                @keydown.enter.prevent="addMedication"
                placeholder="Appuyez sur Entrée pour ajouter"
              />
              <div class="tags">
                <span 
                  v-for="(medication, index) in formData.medicaments" 
                  :key="index"
                  class="tag"
                >
                  {{ medication }}
                  <button type="button" @click="removeMedication(index)">&times;</button>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3>Contact d'Urgence</h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="emergencyName">Nom</label>
            <input 
              id="emergencyName"
              v-model="formData.contact_urgence.nom"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label for="emergencyRelationship">Relation</label>
            <input 
              id="emergencyRelationship"
              v-model="formData.contact_urgence.relation"
              type="text"
              required
            />
          </div>
          <div class="form-group">
            <label for="emergencyPhone">Téléphone</label>
            <input 
              id="emergencyPhone"
              v-model="formData.contact_urgence.telephone"
              type="tel"
              required
              pattern="[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}"
              title="Le numéro de téléphone doit être au format XX XX XX XX XX"
            />
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i>
          {{ isEdit ? 'Mettre à jour' : 'Enregistrer' }}
        </button>
        <button type="button" @click="handleClose" class="btn btn-secondary">
          <i class="fas fa-times"></i>
          Annuler
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed,  watch } from 'vue'

const props = defineProps({
  patient: {
    type: Object,
    default: () => ({
      id: null,
      nom: '',
      prenom: '',
      date_naissance: '',
      numero_securite_sociale: '',
      email: '',
      telephone: '',
      adresse: '',
      allergies: [],
      medicaments: [],
      contact_urgence: {
        nom: '',
        relation: '',
        telephone: ''
      }
    })
  }
})

const emit = defineEmits(['save', 'close'])
const formData = ref({
  id: props.patient?.id ?? null,
  nom: props.patient?.nom ?? '',
  prenom: props.patient?.prenom ?? '',
  date_naissance: props.patient?.date_naissance ?? '',
  numero_securite_sociale: props.patient?.numero_securite_sociale ?? '',
  email: props.patient?.email ?? '',
  telephone: props.patient?.telephone ?? '',
  adresse: props.patient?.adresse ?? '',
  allergies: Array.isArray(props.patient?.allergies) ? [...props.patient.allergies] : [],
  medicaments: Array.isArray(props.patient?.medicaments) ? [...props.patient.medicaments] : [],
  contact_urgence: {
    nom: props.patient?.contact_urgence?.nom ?? '',
    relation: props.patient?.contact_urgence?.relation ?? '',
    telephone: props.patient?.contact_urgence?.telephone ?? ''
  }
})

const newAllergy = ref('')
const newMedication = ref('')

const isEdit = computed(() => !!formData.value.id)

const addAllergy = () => {
  if (newAllergy.value.trim()) {
    formData.value.allergies.push(newAllergy.value.trim())
    newAllergy.value = ''
  }
}

const removeAllergy = (index) => {
  formData.value.allergies.splice(index, 1)
}

const addMedication = () => {
  if (newMedication.value.trim()) {
    formData.value.medicaments.push(newMedication.value.trim())
    newMedication.value = ''
  }
}

const removeMedication = (index) => {
  formData.value.medicaments.splice(index, 1)
}
const handleSubmit = () => {
  const dataToSend = {
    ...formData.value,
    // S'assurer que ce sont bien des tableaux
    allergies: Array.isArray(formData.value.allergies) ? formData.value.allergies : [],
    medicaments: Array.isArray(formData.value.medicaments) ? formData.value.medicaments : [],
    // Contact urgence - vérifier la structure
    contact_urgence: {
      nom: formData.value.contact_urgence.nom || '',
      relation: formData.value.contact_urgence.relation || '',
      telephone: formData.value.contact_urgence.telephone || ''
    }
  };
  
  console.log('Données envoyées:', JSON.stringify(dataToSend, null, 2));
  emit('save', dataToSend);
}

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.patient-form {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.form-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #718096;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f7fafc;
  color: #e53e3e;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: #f7fafc;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.form-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

input,
textarea {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 1rem;
  color: #2d3748;
  transition: all 0.3s ease;
}

input:focus,
textarea:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

.tag-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #e2e8f0;
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tag button {
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 0;
  font-size: 1.25rem;
  line-height: 1;
}

.tag button:hover {
  color: #e53e3e;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #4299e1;
  color: white;
}

.btn-primary:hover {
  background: #3182ce;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}
</style> 