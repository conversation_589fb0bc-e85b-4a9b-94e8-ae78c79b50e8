import { defineStore } from 'pinia'
import { ref } from 'vue'
import apiClient from '@/services/api' // Ton instance Axios

export const usePatientStore = defineStore('patient', () => {
  // État
  const patients = ref([])
  const patient = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Actions
  async function fetchAll() {
    isLoading.value = true
    error.value = null
    try {
      console.log('Fetching patients...')
      const response = await apiClient.get('/patients')
      console.log('Response from API:', response)

      // Gérer le nouveau format de réponse {status: 'success', data: [...]}
      let patientsData = []
      if (response.data) {
        if (response.data.status === 'success' && Array.isArray(response.data.data)) {
          // Nouveau format avec status
          patientsData = response.data.data
        } else if (Array.isArray(response.data)) {
          // Ancien format direct
          patientsData = response.data
        } else {
          console.warn('Format de réponse inattendu:', response.data)
          throw new Error('Format de données invalide reçu de l\'API')
        }

        patients.value = patientsData.map(p => ({
          ...p,
          id: p.id.toString(),
          nom: p.nom || '',
          prenom: p.prenom || '',
          fullName: `${p.nom || ''} ${p.prenom || ''}`.trim(),
          email: p.email || '',
          telephone: p.telephone || '',
          date_naissance: p.date_naissance || '',
          adresse: p.adresse || '',
          allergies: Array.isArray(p.allergies) ? p.allergies : [],
          medicaments: Array.isArray(p.medicaments) ? p.medicaments : [],
          archived: p.archived || false
        }))
        console.log('Patients loaded:', patients.value.length, 'patients')
      } else {
        throw new Error('Aucune donnée reçue de l\'API')
      }
    } catch (err) {
      console.error('Error fetching patients:', err)
      error.value = err.response?.data?.message || err.message || 'Erreur lors du chargement des patients'
      patients.value = []
    } finally {
      isLoading.value = false
    }
  }

  async function fetchById(id) {
    isLoading.value = true
    error.value = null
    try {
      console.log('Fetching patient by ID:', id)
      const response = await apiClient.get(`/patients/${id}`)
      console.log('Patient response:', response.data)

      // Gérer le format de réponse
      let patientData = response.data
      if (response.data.status === 'success' && response.data.data) {
        patientData = response.data.data
      }

      patient.value = {
        ...patientData,
        id: patientData.id.toString(),
        nom: patientData.nom || '',
        prenom: patientData.prenom || '',
        fullName: `${patientData.nom || ''} ${patientData.prenom || ''}`.trim(),
        email: patientData.email || '',
        telephone: patientData.telephone || '',
        adresse: patientData.adresse || '',
        allergies: Array.isArray(patientData.allergies) ? patientData.allergies : [],
        medicaments: Array.isArray(patientData.medicaments) ? patientData.medicaments : [],
        archived: patientData.archived || false
      }
      console.log('Patient loaded:', patient.value)
    } catch (err) {
      console.error('Error fetching patient by ID:', err)
      error.value = err.response?.data?.message || err.message || 'Erreur lors du chargement du patient'
      patient.value = null
    } finally {
      isLoading.value = false
    }
  }

  async function create(newPatient) {
    isLoading.value = true
    error.value = null
    try {
      console.log('Envoi des données patient:', newPatient)

      // Valider les données avant envoi
      if (!newPatient.nom || !newPatient.email) {
        throw new Error('Le nom et l\'email sont requis')
      }

      const response = await apiClient.post('/patients', newPatient)
      console.log('Réponse du serveur:', response)

      // Gérer le format de réponse
      let patientData = response.data
      if (response.data.status === 'success' && response.data.data) {
        patientData = response.data.data
      }

      if (patientData) {
        const createdPatient = {
          ...patientData,
          id: patientData.id.toString(),
          nom: patientData.nom || '',
          prenom: patientData.prenom || '',
          fullName: `${patientData.nom || ''} ${patientData.prenom || ''}`.trim(),
          email: patientData.email || '',
          telephone: patientData.telephone || '',
          adresse: patientData.adresse || '',
          allergies: Array.isArray(patientData.allergies) ? patientData.allergies : [],
          medicaments: Array.isArray(patientData.medicaments) ? patientData.medicaments : [],
          archived: patientData.archived || false
        }
        patients.value.push(createdPatient)
        console.log('Patient créé avec succès:', createdPatient)
        return createdPatient
      } else {
        throw new Error('Aucune donnée reçue après création')
      }
    } catch (err) {
      console.error('Erreur détaillée lors de la création:', err)
      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.message ||
                          err.message ||
                          'Erreur lors de la création du patient'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }
  
async function update(id, updatedPatient) {
  isLoading.value = true;
  error.value = null;
  try {
    console.log('Mise à jour du patient ID:', id, 'avec données:', updatedPatient)

    // Valider les données
    if (!id) {
      throw new Error('ID patient requis pour la mise à jour')
    }

    // Formater correctement les données avant envoi
    const dataToSend = {
      ...updatedPatient,
      allergies: Array.isArray(updatedPatient.allergies) ? updatedPatient.allergies : [],
      medicaments: Array.isArray(updatedPatient.medicaments) ? updatedPatient.medicaments : [],
      contact_urgence: updatedPatient.contact_urgence || {
        nom: '',
        relation: '',
        telephone: ''
      }
    };

    const response = await apiClient.put(`/patients/${id}`, dataToSend);
    console.log('Réponse mise à jour:', response.data)

    // Gérer le format de réponse
    let patientData = response.data
    if (response.data.status === 'success' && response.data.data) {
      patientData = response.data.data
    }

    // Mise à jour du store
    const idx = patients.value.findIndex(p => p.id === id.toString());
    if (idx !== -1) {
      patients.value[idx] = {
        ...patientData,
        id: patientData.id.toString(),
        nom: patientData.nom || '',
        prenom: patientData.prenom || '',
        fullName: `${patientData.nom || ''} ${patientData.prenom || ''}`.trim(),
        email: patientData.email || '',
        telephone: patientData.telephone || '',
        adresse: patientData.adresse || '',
        allergies: Array.isArray(patientData.allergies) ? patientData.allergies : [],
        medicaments: Array.isArray(patientData.medicaments) ? patientData.medicaments : [],
        archived: patientData.archived || false
      };
      console.log('Patient mis à jour dans le store:', patients.value[idx])
    }
    return patientData;
  } catch (err) {
    console.error('Erreur lors de la mise à jour:', err)
    const errorMessage = err.response?.data?.error ||
                        err.response?.data?.message ||
                        err.message ||
                        'Erreur lors de la modification'
    error.value = errorMessage;
    throw new Error(errorMessage);
  } finally {
    isLoading.value = false;
  }
}

  async function archive(id, status) {
    isLoading.value = true
    error.value = null
    try {
      console.log('Archivage du patient ID:', id, 'status:', status)
      const response = await apiClient.patch(`/patients/${id}/archive`, { archived: status })

      // Gérer le format de réponse
      let responseData = response.data
      if (response.data.status === 'success' && response.data.data) {
        responseData = response.data.data
      }

      const idx = patients.value.findIndex(p => p.id === id.toString())
      if (idx !== -1) {
        patients.value[idx] = {
          ...patients.value[idx],
          archived: status
        }
        console.log('Patient archivé dans le store')
      }
      return responseData
    } catch (err) {
      console.error('Erreur lors de l\'archivage:', err)
      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.message ||
                          err.message ||
                          'Erreur lors de l\'archivage'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  async function remove(id) {
    isLoading.value = true
    error.value = null
    try {
      console.log('Suppression du patient ID:', id)
      await apiClient.delete(`/patients/${id}`)
      patients.value = patients.value.filter(p => p.id !== id.toString())
      console.log('Patient supprimé du store')
    } catch (err) {
      console.error('Erreur lors de la suppression:', err)
      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.message ||
                          err.message ||
                          'Erreur lors de la suppression'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  return {
    patients,
    patient,
    isLoading,
    error,
    fetchAll,
    fetchById,
    create,
    update,
    archive,
    remove
  }
})
