<?php
class CleanupController {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Supprimer toutes les données de test
     */
    public function removeTestData() {
        try {
            $results = [
                'appointments_deleted' => 0,
                'slots_deleted' => 0,
                'users_deleted' => 0,
                'conflicts_deleted' => 0,
                'messages' => []
            ];
            
            $results['messages'][] = "🧹 Suppression des données de test...";
            
            // Commencer une transaction
            $this->db->beginTransaction();
            
            // 1. Supprimer les rendez-vous de test
            $stmt = $this->db->prepare("DELETE FROM rendez_vous WHERE notes = 'Rendez-vous de test'");
            $stmt->execute();
            $results['appointments_deleted'] = $stmt->rowCount();
            $results['messages'][] = "📅 {$results['appointments_deleted']} rendez-vous de test supprimés";
            
            // 2. Supprimer les créneaux suggérés de test
            $stmt = $this->db->query("SHOW TABLES LIKE 'creneaux_suggeres'");
            if ($stmt->fetch()) {
                $stmt = $this->db->prepare("DELETE FROM creneaux_suggeres WHERE raison IN ('Créneau disponible', 'Consultation de suivi', 'Nouveau patient')");
                $stmt->execute();
                $results['slots_deleted'] = $stmt->rowCount();
                $results['messages'][] = "🕒 {$results['slots_deleted']} créneaux suggérés de test supprimés";
            }
            
            // 3. Supprimer les conflits de test (si la table existe)
            $stmt = $this->db->query("SHOW TABLES LIKE 'conflits'");
            if ($stmt->fetch()) {
                $stmt = $this->db->prepare("DELETE FROM conflits WHERE description LIKE '%test%' OR notes LIKE '%test%'");
                $stmt->execute();
                $results['conflicts_deleted'] = $stmt->rowCount();
                $results['messages'][] = "⚠️ {$results['conflicts_deleted']} conflits de test supprimés";
            }
            
            // 4. Supprimer les utilisateurs de test (médecins et patients)
            $testEmails = [
                '<EMAIL>',
                '<EMAIL>', 
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];
            
            $placeholders = str_repeat('?,', count($testEmails) - 1) . '?';
            $stmt = $this->db->prepare("DELETE FROM utilisateur WHERE email IN ($placeholders)");
            $stmt->execute($testEmails);
            $results['users_deleted'] = $stmt->rowCount();
            $results['messages'][] = "👥 {$results['users_deleted']} utilisateurs de test supprimés";
            
            // Valider la transaction
            $this->db->commit();
            
            $results['messages'][] = "✅ Toutes les données de test ont été supprimées avec succès!";
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $results,
                'message' => 'Données de test supprimées avec succès'
            ]);
            
        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollBack();
            
            error_log("Erreur dans CleanupController->removeTestData: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression des données de test: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Supprimer toutes les données (ATTENTION: DANGEREUX)
     */
    public function removeAllData() {
        try {
            $results = [
                'appointments_deleted' => 0,
                'slots_deleted' => 0,
                'users_deleted' => 0,
                'conflicts_deleted' => 0,
                'messages' => []
            ];
            
            $results['messages'][] = "⚠️ SUPPRESSION DE TOUTES LES DONNÉES...";
            
            // Commencer une transaction
            $this->db->beginTransaction();
            
            // 1. Supprimer tous les rendez-vous
            $stmt = $this->db->prepare("DELETE FROM rendez_vous");
            $stmt->execute();
            $results['appointments_deleted'] = $stmt->rowCount();
            $results['messages'][] = "📅 {$results['appointments_deleted']} rendez-vous supprimés";
            
            // 2. Supprimer tous les créneaux suggérés
            $stmt = $this->db->query("SHOW TABLES LIKE 'creneaux_suggeres'");
            if ($stmt->fetch()) {
                $stmt = $this->db->prepare("DELETE FROM creneaux_suggeres");
                $stmt->execute();
                $results['slots_deleted'] = $stmt->rowCount();
                $results['messages'][] = "🕒 {$results['slots_deleted']} créneaux suggérés supprimés";
            }
            
            // 3. Supprimer tous les conflits
            $stmt = $this->db->query("SHOW TABLES LIKE 'conflits'");
            if ($stmt->fetch()) {
                $stmt = $this->db->prepare("DELETE FROM conflits");
                $stmt->execute();
                $results['conflicts_deleted'] = $stmt->rowCount();
                $results['messages'][] = "⚠️ {$results['conflicts_deleted']} conflits supprimés";
            }
            
            // 4. Supprimer tous les utilisateurs SAUF les admins
            $stmt = $this->db->prepare("DELETE FROM utilisateur WHERE role != 'admin'");
            $stmt->execute();
            $results['users_deleted'] = $stmt->rowCount();
            $results['messages'][] = "👥 {$results['users_deleted']} utilisateurs supprimés (admins préservés)";
            
            // Valider la transaction
            $this->db->commit();
            
            $results['messages'][] = "✅ Toutes les données ont été supprimées avec succès!";
            $results['messages'][] = "⚠️ Seuls les comptes administrateurs ont été préservés";
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $results,
                'message' => 'Toutes les données supprimées avec succès'
            ]);
            
        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollBack();
            
            error_log("Erreur dans CleanupController->removeAllData: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression de toutes les données: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Réinitialiser les tables (vider complètement)
     */
    public function resetTables() {
        try {
            $results = [
                'tables_reset' => [],
                'messages' => []
            ];
            
            $results['messages'][] = "🔄 Réinitialisation des tables...";
            
            // Commencer une transaction
            $this->db->beginTransaction();
            
            // Désactiver les contraintes de clés étrangères temporairement
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Tables à réinitialiser
            $tables = ['rendez_vous', 'creneaux_suggeres', 'conflits'];
            
            foreach ($tables as $table) {
                // Vérifier si la table existe
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->fetch()) {
                    // Vider la table
                    $this->db->exec("TRUNCATE TABLE $table");
                    $results['tables_reset'][] = $table;
                    $results['messages'][] = "🗑️ Table '$table' réinitialisée";
                }
            }
            
            // Réactiver les contraintes de clés étrangères
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            // Valider la transaction
            $this->db->commit();
            
            $results['messages'][] = "✅ Tables réinitialisées avec succès!";
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $results,
                'message' => 'Tables réinitialisées avec succès'
            ]);
            
        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollBack();
            
            error_log("Erreur dans CleanupController->resetTables: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la réinitialisation des tables: ' . $e->getMessage()
            ]);
        }
    }
}
?>
