<?php

class SuggestedSlot {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Créer un nouveau créneau suggéré
     */
    public function create($slotData) {
        try {
            $query = "
                INSERT INTO creneaux_suggeres
                (id_medecin, id_patient, date_heure_suggeree, duree, type_creneau_id, score_confiance, raison, expire_le)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $slotData['id_medecin'],
                $slotData['id_patient'],
                $slotData['date_heure_suggeree'],
                $slotData['duree'] ?? 30,
                $slotData['type_creneau_id'] ?? null,
                $slotData['score_confiance'] ?? null,
                $slotData['raison'] ?? null,
                $slotData['expire_le']
            ]);
            
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log("Erreur lors de la création du créneau suggéré: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Récupérer les créneaux suggérés d'un médecin
     */
    public function getByDoctor($medecinId = null, $includeExpired = false) {
        try {
            $whereClause = "";
            $params = [];

            if ($medecinId) {
                $whereClause = "WHERE cs.id_medecin = ?";
                $params = [$medecinId];
            }

            if (!$includeExpired) {
                $whereClause .= ($whereClause ? " AND" : "WHERE") . " cs.expire_le > NOW()";
            }
            
            $query = "
                SELECT
                    cs.*,
                    CASE
                        WHEN cs.id_patient IS NOT NULL THEN CONCAT(p.nom, ' ', p.prenom)
                        ELSE 'Créneau disponible'
                    END as patient_nom,
                    p.telephone as patient_telephone,
                    p.email as patient_email,
                    CONCAT('Dr. ', u.nom, ' ', u.prenom) as medecin_nom,
                    m.specialite,
                    tc.nom as type_nom,
                    tc.couleur as type_couleur,
                    tc.prix_base as type_prix,
                    CASE
                        WHEN cs.expire_le < NOW() THEN 'expire'
                        WHEN cs.id_patient IS NULL THEN 'disponible'
                        WHEN cs.accepte = 1 THEN 'accepte'
                        WHEN cs.accepte = 0 THEN 'refuse'
                        ELSE 'en_attente'
                    END as statut
                FROM creneaux_suggeres cs
                LEFT JOIN patients p ON cs.id_patient = p.id
                LEFT JOIN medecins m ON cs.id_medecin = m.id
                LEFT JOIN utilisateur u ON m.user_id = u.id
                LEFT JOIN types_creneaux tc ON cs.type_creneau_id = tc.id
                $whereClause
                ORDER BY cs.date_heure_suggeree ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des créneaux suggérés: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les créneaux suggérés d'un patient
     */
    public function getByPatient($patientId, $includeExpired = false) {
        try {
            $whereClause = "WHERE cs.id_patient = ?";
            $params = [$patientId];
            
            if (!$includeExpired) {
                $whereClause .= " AND cs.expire_le > NOW()";
            }
            
            $query = "
                SELECT 
                    cs.*,
                    CONCAT(m_user.nom, ' ', m_user.prenom) as medecin_nom,
                    m.specialite,
                    CASE 
                        WHEN cs.expire_le < NOW() THEN 'expire'
                        WHEN cs.accepte = 1 THEN 'accepte'
                        WHEN cs.accepte = 0 THEN 'refuse'
                        ELSE 'en_attente'
                    END as statut
                FROM creneaux_suggeres cs
                INNER JOIN medecins m ON cs.id_medecin = m.id
                INNER JOIN utilisateur m_user ON m.user_id = m_user.id
                $whereClause
                ORDER BY cs.date_heure_suggeree ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des créneaux suggérés: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Mettre à jour le statut d'acceptation d'un créneau
     */
    public function updateAcceptance($slotId, $accepted, $userId = null) {
        try {
            $query = "
                UPDATE creneaux_suggeres 
                SET accepte = ?, 
                    cree_le = CURRENT_TIMESTAMP
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$accepted ? 1 : 0, $slotId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la mise à jour du créneau suggéré: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Supprimer un créneau suggéré
     */
    public function delete($slotId) {
        try {
            $query = "DELETE FROM creneaux_suggeres WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$slotId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la suppression du créneau suggéré: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Récupérer les statistiques des créneaux suggérés
     */
    public function getStats($medecinId = null) {
        try {
            $whereClause = $medecinId ? "WHERE id_medecin = ?" : "";
            $params = $medecinId ? [$medecinId] : [];
            
            $query = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN accepte = 1 THEN 1 ELSE 0 END) as acceptes,
                    SUM(CASE WHEN accepte = 0 THEN 1 ELSE 0 END) as refuses,
                    SUM(CASE WHEN accepte IS NULL AND expire_le > NOW() THEN 1 ELSE 0 END) as en_attente,
                    SUM(CASE WHEN expire_le < NOW() THEN 1 ELSE 0 END) as expires,
                    AVG(score_confiance) as score_moyen,
                    AVG(CASE WHEN accepte = 1 THEN score_confiance ELSE NULL END) as score_acceptes
                FROM creneaux_suggeres 
                $whereClause
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des statistiques: " . $e->getMessage());
            return [
                'total' => 0,
                'acceptes' => 0,
                'refuses' => 0,
                'en_attente' => 0,
                'expires' => 0,
                'score_moyen' => 0,
                'score_acceptes' => 0
            ];
        }
    }
    
    /**
     * Nettoyer les créneaux expirés
     */
    public function cleanExpired() {
        try {
            $query = "DELETE FROM creneaux_suggeres WHERE expire_le < NOW()";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Erreur lors du nettoyage des créneaux expirés: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Vérifier les conflits avec les créneaux existants
     */
    public function checkConflicts($medecinId, $dateHeure, $duree) {
        try {
            $endTime = date('Y-m-d H:i:s', strtotime($dateHeure) + ($duree * 60));
            
            $query = "
                SELECT 
                    rdv.id,
                    rdv.date_rendez_vous,
                    rdv.duree,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                WHERE rdv.id_medecin = ?
                AND rdv.statut != 'annule'
                AND (
                    (rdv.date_rendez_vous <= ? AND DATE_ADD(rdv.date_rendez_vous, INTERVAL rdv.duree MINUTE) > ?)
                    OR (rdv.date_rendez_vous < ? AND DATE_ADD(rdv.date_rendez_vous, INTERVAL rdv.duree MINUTE) >= ?)
                )
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$medecinId, $dateHeure, $dateHeure, $endTime, $endTime]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la vérification des conflits: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculer un score de confiance basé sur l'historique
     */
    public function calculateConfidenceScore($medecinId, $patientId, $dateHeure) {
        try {
            // Facteurs pour le calcul du score :
            // 1. Historique d'acceptation du patient
            // 2. Jour de la semaine
            // 3. Heure de la journée
            // 4. Délai de proposition
            
            $score = 0.5; // Score de base
            
            // Historique d'acceptation du patient
            $historyQuery = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN accepte = 1 THEN 1 ELSE 0 END) as acceptes
                FROM creneaux_suggeres 
                WHERE id_patient = ? AND id_medecin = ?
            ";
            
            $stmt = $this->db->prepare($historyQuery);
            $stmt->execute([$patientId, $medecinId]);
            $history = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($history['total'] > 0) {
                $acceptanceRate = $history['acceptes'] / $history['total'];
                $score = ($score + $acceptanceRate) / 2;
            }
            
            // Bonus pour les créneaux en semaine vs weekend
            $dayOfWeek = date('N', strtotime($dateHeure));
            if ($dayOfWeek <= 5) { // Lundi à vendredi
                $score += 0.1;
            }
            
            // Bonus pour les heures de bureau (9h-17h)
            $hour = date('H', strtotime($dateHeure));
            if ($hour >= 9 && $hour <= 17) {
                $score += 0.1;
            }
            
            // Malus pour les créneaux trop proches (moins de 24h)
            $hoursUntil = (strtotime($dateHeure) - time()) / 3600;
            if ($hoursUntil < 24) {
                $score -= 0.2;
            }
            
            return max(0, min(1, $score)); // Entre 0 et 1
        } catch (PDOException $e) {
            error_log("Erreur lors du calcul du score de confiance: " . $e->getMessage());
            return 0.5;
        }
    }
}
