import api from './api'

class AppointmentStatusService {
  /**
   * Met à jour automatiquement tous les rendez-vous expirés
   */
  async updateExpiredAppointments() {
    try {
      console.log('🔄 Mise à jour automatique des rendez-vous expirés...')
      const response = await api.post('/appointments/update-expired')
      console.log('✅ Mise à jour terminée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour automatique:', error)
      throw error
    }
  }

  /**
   * Met à jour les rendez-vous d'un médecin spécifique
   */
  async updateDoctorAppointments(doctorId) {
    try {
      console.log(`🔄 Mise à jour des RDV pour le médecin ${doctorId}...`)
      const response = await api.post(`/appointments/update-doctor/${doctorId}`)
      console.log('✅ Mise à jour médecin terminée:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la mise à jour des RDV du médecin ${doctorId}:`, error)
      throw error
    }
  }

  /**
   * Met à jour un rendez-vous spécifique
   */
  async updateSingleAppointment(appointmentId) {
    try {
      console.log(`🔄 Mise à jour du RDV ${appointmentId}...`)
      const response = await api.post(`/appointments/update-single/${appointmentId}`)
      console.log('✅ Mise à jour RDV terminée:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la mise à jour du RDV ${appointmentId}:`, error)
      throw error
    }
  }

  /**
   * Vérifie les rendez-vous expirés sans les mettre à jour
   */
  async checkExpiredAppointments() {
    try {
      console.log('🔍 Vérification des rendez-vous expirés...')
      const response = await api.get('/appointments/check-expired')
      console.log('📊 Rendez-vous expirés trouvés:', response.data.data.count)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la vérification:', error)
      throw error
    }
  }

  /**
   * Obtient les statistiques des mises à jour
   */
  async getUpdateStats() {
    try {
      console.log('📊 Récupération des statistiques de mise à jour...')
      const response = await api.get('/appointments/status-stats')
      console.log('✅ Statistiques récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      throw error
    }
  }

  /**
   * Obtient la configuration de mise à jour automatique
   */
  async getAutoUpdateConfig() {
    try {
      console.log('⚙️ Récupération de la configuration...')
      const response = await api.get('/appointments/auto-update-config')
      console.log('✅ Configuration récupérée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de la configuration:', error)
      throw error
    }
  }

  /**
   * Configure la mise à jour automatique
   */
  async setAutoUpdateConfig(enabled = true, intervalMinutes = 5) {
    try {
      console.log(`⚙️ Configuration de la mise à jour automatique: ${enabled ? 'activée' : 'désactivée'}`)
      const response = await api.post('/appointments/auto-update-config', {
        enabled,
        interval_minutes: intervalMinutes
      })
      console.log('✅ Configuration mise à jour:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la configuration:', error)
      throw error
    }
  }

  /**
   * Nettoie les anciens logs
   */
  async cleanLogs() {
    try {
      console.log('🧹 Nettoyage des anciens logs...')
      const response = await api.post('/appointments/clean-logs')
      console.log('✅ Logs nettoyés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage des logs:', error)
      throw error
    }
  }

  /**
   * Démarre la mise à jour automatique périodique
   */
  startAutoUpdate(intervalMinutes = 5, doctorId = null) {
    console.log(`🔄 Démarrage de la mise à jour automatique (${intervalMinutes} min)`)
    
    const updateFunction = doctorId 
      ? () => this.updateDoctorAppointments(doctorId)
      : () => this.updateExpiredAppointments()

    // Première exécution immédiate
    updateFunction().catch(error => {
      console.warn('⚠️ Erreur lors de la première mise à jour automatique:', error.message)
    })

    // Puis exécution périodique
    const intervalId = setInterval(() => {
      updateFunction().catch(error => {
        console.warn('⚠️ Erreur lors de la mise à jour automatique périodique:', error.message)
      })
    }, intervalMinutes * 60 * 1000)

    console.log(`✅ Mise à jour automatique démarrée (ID: ${intervalId})`)
    return intervalId
  }

  /**
   * Arrête la mise à jour automatique
   */
  stopAutoUpdate(intervalId) {
    if (intervalId) {
      clearInterval(intervalId)
      console.log(`🛑 Mise à jour automatique arrêtée (ID: ${intervalId})`)
      return true
    }
    return false
  }

  /**
   * Vérifie si un rendez-vous devrait être terminé (côté client)
   */
  shouldBeCompleted(appointment) {
    try {
      const appointmentDate = new Date(appointment.date_rendez_vous || appointment.date)
      const duration = appointment.duree || 30 // durée par défaut 30 minutes
      const endTime = new Date(appointmentDate.getTime() + duration * 60000)
      const now = new Date()

      const shouldComplete = now > endTime && 
        ['planifie', 'confirme', 'planifier', 'confirmer'].includes(appointment.statut)

      if (shouldComplete) {
        console.log(`⏰ RDV ${appointment.id} devrait être terminé:`, {
          date: appointmentDate.toISOString(),
          endTime: endTime.toISOString(),
          now: now.toISOString(),
          status: appointment.statut
        })
      }

      return shouldComplete
    } catch (error) {
      console.error('❌ Erreur lors de la vérification du statut:', error)
      return false
    }
  }

  /**
   * Filtre les rendez-vous qui devraient être terminés
   */
  filterExpiredAppointments(appointments) {
    return appointments.filter(appointment => this.shouldBeCompleted(appointment))
  }

  /**
   * Met à jour automatiquement les rendez-vous d'une liste (côté client)
   */
  async updateAppointmentsList(appointments, updateCallback = null) {
    try {
      const expiredAppointments = this.filterExpiredAppointments(appointments)
      
      if (expiredAppointments.length === 0) {
        console.log('✅ Aucun rendez-vous à mettre à jour dans la liste')
        return { updated: 0, appointments: appointments }
      }

      console.log(`🔄 ${expiredAppointments.length} rendez-vous à mettre à jour dans la liste`)

      // Mettre à jour chaque rendez-vous expiré
      const updatePromises = expiredAppointments.map(async (appointment) => {
        try {
          await this.updateSingleAppointment(appointment.id)
          
          // Mettre à jour localement
          appointment.statut = 'termine'
          appointment.status = 'terminé'
          
          // Callback optionnel pour mise à jour de l'interface
          if (updateCallback) {
            updateCallback(appointment)
          }
          
          return appointment
        } catch (error) {
          console.warn(`⚠️ Erreur lors de la mise à jour du RDV ${appointment.id}:`, error.message)
          return appointment
        }
      })

      await Promise.all(updatePromises)

      console.log(`✅ ${expiredAppointments.length} rendez-vous mis à jour dans la liste`)
      
      return {
        updated: expiredAppointments.length,
        appointments: appointments
      }
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour de la liste:', error)
      throw error
    }
  }

  /**
   * Initialise la mise à jour automatique pour un composant
   */
  initializeForComponent(doctorId = null, intervalMinutes = 5) {
    console.log('🚀 Initialisation de la mise à jour automatique pour le composant')
    
    // Vérifier la configuration
    this.getAutoUpdateConfig().then(config => {
      if (config.data.enabled) {
        console.log('✅ Mise à jour automatique activée dans la configuration')
        return this.startAutoUpdate(intervalMinutes, doctorId)
      } else {
        console.log('⚠️ Mise à jour automatique désactivée dans la configuration')
        return null
      }
    }).catch(error => {
      console.warn('⚠️ Erreur lors de la vérification de la configuration, démarrage par défaut:', error.message)
      return this.startAutoUpdate(intervalMinutes, doctorId)
    })
  }
}

// Instance singleton
const appointmentStatusService = new AppointmentStatusService()

export default appointmentStatusService
