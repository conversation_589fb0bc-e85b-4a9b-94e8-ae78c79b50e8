import api from './api'

export const slotTypeService = {
  /**
   * Récupérer tous les types de créneaux
   */
  async getAll() {
    try {
      console.log('📋 Récupération de tous les types de créneaux...')

      // Essayer d'abord l'API
      try {
        const response = await api.get('/slot-types')
        console.log('✅ Types récupérés via API:', response.data)
        return response.data
      } catch (apiError) {
        console.warn('⚠️ API non disponible, utilisation de données de fallback:', apiError.message)

        // Fallback avec des données de démonstration
        const mockTypes = [
          {
            id: 1,
            nom: 'Court',
            duree: 15,
            description: 'Consultation rapide, suivi simple',
            couleur: '#10b981',
            prix_base: 25000,
            actif: 1
          },
          {
            id: 2,
            nom: 'Standard',
            duree: 30,
            description: 'Consultation standard, examen général',
            couleur: '#3b82f6',
            prix_base: 50000,
            actif: 1
          },
          {
            id: 3,
            nom: 'Long',
            duree: 60,
            description: 'Consultation approfondie, premier rendez-vous',
            couleur: '#f59e0b',
            prix_base: 80000,
            actif: 1
          }
        ]

        console.log('📝 Types de démonstration générés:', mockTypes.length, 'types')
        return {
          status: 'success',
          data: mockTypes
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des types de créneaux:', error)
      throw error
    }
  },

  /**
   * Récupérer les types de créneaux actifs
   */
  async getActive() {
    try {
      const response = await api.get('/slot-types/active')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des types actifs:', error)
      throw error
    }
  },

  /**
   * Récupérer les statistiques des types
   */
  async getStats() {
    try {
      console.log('📊 Récupération des statistiques des types de créneaux...')

      // Essayer d'abord l'API
      try {
        const response = await api.get('/slot-types/stats')
        console.log('✅ Statistiques récupérées via API:', response.data)
        return response.data
      } catch (apiError) {
        console.warn('⚠️ API non disponible, utilisation de données de fallback:', apiError.message)

        // Fallback avec des données de démonstration
        const mockStats = [
          {
            id: 1,
            nom: 'Court',
            duree: 15,
            description: 'Consultation rapide, suivi simple',
            couleur: '#10b981',
            prix_base: 25000,
            actif: 1,
            nb_creneaux_suggeres: 12,
            nb_acceptes: 8,
            nb_refuses: 2,
            nb_en_attente: 2
          },
          {
            id: 2,
            nom: 'Standard',
            duree: 30,
            description: 'Consultation standard, examen général',
            couleur: '#3b82f6',
            prix_base: 50000,
            actif: 1,
            nb_creneaux_suggeres: 25,
            nb_acceptes: 18,
            nb_refuses: 3,
            nb_en_attente: 4
          },
          {
            id: 3,
            nom: 'Long',
            duree: 60,
            description: 'Consultation approfondie, premier rendez-vous',
            couleur: '#f59e0b',
            prix_base: 80000,
            actif: 1,
            nb_creneaux_suggeres: 8,
            nb_acceptes: 6,
            nb_refuses: 1,
            nb_en_attente: 1
          }
        ]

        console.log('📝 Statistiques de démonstration générées:', mockStats.length, 'types')
        return {
          status: 'success',
          data: mockStats
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      throw error
    }
  },

  /**
   * Créer un nouveau type de créneau
   */
  async create(typeData) {
    try {
      console.log('➕ Création d\'un nouveau type de créneau:', typeData)

      // Essayer d'abord l'API
      try {
        const response = await api.post('/slot-types', typeData)
        console.log('✅ Type créé via API:', response.data)
        return response.data
      } catch (apiError) {
        console.warn('⚠️ API non disponible, simulation de création:', apiError.message)

        // Fallback : simuler la création
        const newType = {
          id: Date.now(), // ID temporaire basé sur timestamp
          ...typeData,
          actif: typeData.actif !== undefined ? typeData.actif : true
        }

        console.log('📝 Type simulé créé:', newType)
        return {
          status: 'success',
          message: 'Type de créneau créé avec succès (mode simulation)',
          data: newType
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la création du type:', error)
      throw error
    }
  },

  /**
   * Mettre à jour un type de créneau
   */
  async update(id, typeData) {
    try {
      const response = await api.put(`/slot-types/${id}`, typeData)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour du type:', error)
      throw error
    }
  },

  /**
   * Supprimer un type de créneau
   */
  async delete(id) {
    try {
      const response = await api.delete(`/slot-types/${id}`)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la suppression du type:', error)
      throw error
    }
  },

  /**
   * Initialiser les types par défaut
   */
  async initializeDefaults() {
    try {
      const response = await api.post('/slot-types/initialize')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error)
      throw error
    }
  }
}
