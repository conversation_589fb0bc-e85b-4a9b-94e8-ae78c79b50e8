import api from './api'

export const slotTypeService = {
  /**
   * <PERSON><PERSON>cupérer tous les types de créneaux
   */
  async getAll() {
    try {
      const response = await api.get('/slot-types')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des types de créneaux:', error)
      throw error
    }
  },

  /**
   * Récupérer les types de créneaux actifs
   */
  async getActive() {
    try {
      const response = await api.get('/slot-types/active')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des types actifs:', error)
      throw error
    }
  },

  /**
   * Récupérer les statistiques des types
   */
  async getStats() {
    try {
      const response = await api.get('/slot-types/stats')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      throw error
    }
  },

  /**
   * Créer un nouveau type de créneau
   */
  async create(typeData) {
    try {
      const response = await api.post('/slot-types', typeData)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la création du type:', error)
      throw error
    }
  },

  /**
   * Mettre à jour un type de créneau
   */
  async update(id, typeData) {
    try {
      const response = await api.put(`/slot-types/${id}`, typeData)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour du type:', error)
      throw error
    }
  },

  /**
   * Supprimer un type de créneau
   */
  async delete(id) {
    try {
      const response = await api.delete(`/slot-types/${id}`)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la suppression du type:', error)
      throw error
    }
  },

  /**
   * Initialiser les types par défaut
   */
  async initializeDefaults() {
    try {
      const response = await api.post('/slot-types/initialize')
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error)
      throw error
    }
  }
}
