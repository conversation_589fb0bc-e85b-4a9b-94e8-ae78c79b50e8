<?php
require_once __DIR__ . '/../config/database.php';

class PatientController {
    private $db;

    public function __construct($db = null) {
        $this->db = $db ?? Database::getInstance()->getConnection();
    }
public function index() {
    header('Content-Type: application/json');

    try {
        error_log("Début de la récupération des patients");

        $query = "
            SELECT
                p.id,
                COALESCE(u.nom, p.nom) as nom,
                COALESCE(u.prenom, p.prenom) as prenom,
                COALESCE(u.email, p.email) as email,
                p.telephone,
                p.date_naissance,
                p.numero_securite_sociale,
                p.creneaux_preferes,
                p.adresse,
                p.archived,
                IFNULL(GROUP_CONCAT(DISTINCT a.allergie SEPARATOR '||'), '') as allergies,
                IFNULL(GROUP_CONCAT(DISTINCT m.medicament SEPARATOR '||'), '') as medicaments
            FROM patients p
            LEFT JOIN utilisateur u ON p.user_id = u.id
            LEFT JOIN allergies a ON p.id = a.patient_id
            LEFT JOIN medicaments m ON p.id = m.patient_id
            WHERE p.archived = 0
            GROUP BY p.id
            ORDER BY COALESCE(u.nom, p.nom), COALESCE(u.prenom, p.prenom)
        ";
        
        error_log("Exécution de la requête: " . substr($query, 0, 200) . "..."); // Log partiel de la requête
        
        $stmt = $this->db->query($query);
        
        if ($stmt === false) {
            $error = $this->db->errorInfo();
            error_log("Erreur SQL complète: " . print_r($error, true));
            throw new Exception("Erreur lors de l'exécution de la requête: " . $error[2]);
        }
        
        $patients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log(sprintf("Nombre de patients trouvés: %d", count($patients)));
        
        $formattedPatients = array_map(function($patient) {
            // Conversion des chaînes séparées par || en tableaux
            $allergies = !empty($patient['allergies']) ?
                array_filter(explode('||', $patient['allergies'])) : [];

            $medicaments = !empty($patient['medicaments']) ?
                array_filter(explode('||', $patient['medicaments'])) : [];

            return [
                'id' => (int)$patient['id'],
                'nom' => $patient['nom'] ?? '',
                'prenom' => $patient['prenom'] ?? '',
                'email' => $patient['email'] ?? null,
                'telephone' => $patient['telephone'] ?? null,
                'date_naissance' => $patient['date_naissance'],
                'numero_securite_sociale' => $patient['numero_securite_sociale'],
                'creneaux_preferes' => $patient['creneaux_preferes'],
                'adresse' => $patient['adresse'] ?? null,
                'archived' => (bool)$patient['archived'],
                'allergies' => $allergies,
                'medicaments' => $medicaments,
                'fullName' => trim(($patient['nom'] ?? '') . ' ' . ($patient['prenom'] ?? ''))
            ];
        }, $patients);

        error_log("Formatage terminé, envoi de la réponse");

        // Retourner dans le format attendu par le frontend
        echo json_encode([
            'status' => 'success',
            'data' => $formattedPatients
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        error_log("ERREUR: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'details' => 'Erreur lors de la récupération des données patients'
        ]);
    }
}








    public function show($id) {
        try {
            // Récupérer un patient spécifique avec ses allergies et médicaments
            $query = "
                SELECT 
                    p.*,
                    GROUP_CONCAT(DISTINCT a.allergie) as allergies,
                    GROUP_CONCAT(DISTINCT m.medicament) as medicaments
                FROM patients p
                LEFT JOIN allergies a ON p.id = a.patient_id
                LEFT JOIN medicaments m ON p.id = m.patient_id
                WHERE p.id = ?
                GROUP BY p.id
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            $patient = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($patient) {
                $patient['allergies'] = $patient['allergies'] ? explode(',', $patient['allergies']) : [];
                $patient['medicaments'] = $patient['medicaments'] ? explode(',', $patient['medicaments']) : [];
                
                header('Content-Type: application/json');
                echo json_encode($patient);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Patient non trouvé']);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la récupération du patient: ' . $e->getMessage()]);
        }
    }

    public function store() {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            
            // Validation des données requises
            if (empty($data['nom']) || empty($data['email'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Le nom et l\'email sont requis']);
                return;
            }

            // Démarrer une transaction
            $this->db->beginTransaction();

            // 1. Insérer le patient
            $sql = "INSERT INTO patients (nom, prenom, email, telephone, date_naissance, numero_securite_sociale, creneaux_preferes, user_id) 
                    VALUES (:nom, :prenom, :email, :telephone, :date_naissance, :numero_securite_sociale, :creneaux_preferes, :user_id)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                'nom' => $data['nom'],
                'prenom' => $data['prenom'] ?? null,
                'email' => $data['email'],
                'telephone' => $data['telephone'] ?? null,
                'date_naissance' => $data['date_naissance'] ?? null,
                'numero_securite_sociale' => $data['numero_securite_sociale'] ?? null,
                'creneaux_preferes' => $data['creneaux_preferes'] ?? null,
                'user_id' => $data['user_id'] ?? null
            ]);


            $patientId = $this->db->lastInsertId();

            // 2. Insérer les allergies
            if (!empty($data['allergies']) && is_array($data['allergies'])) {
                $stmtAllergies = $this->db->prepare("INSERT INTO allergies (patient_id, allergie) VALUES (:patient_id, :allergie)");
                foreach ($data['allergies'] as $allergie) {
                    $stmtAllergies->execute([
                        'patient_id' => $patientId,
                        'allergie' => $allergie
                    ]);
                }
            }

            // 3. Insérer les médicaments
            if (!empty($data['medicaments']) && is_array($data['medicaments'])) {
                $stmtMedicaments = $this->db->prepare("INSERT INTO medicaments (patient_id, medicament) VALUES (:patient_id, :medicament)");
                foreach ($data['medicaments'] as $medicament) {
                    $stmtMedicaments->execute([
                        'patient_id' => $patientId,
                        'medicament' => $medicament
                    ]);
                }
            }

            // Valider la transaction
            $this->db->commit();

            // Retourner le patient créé avec toutes ses informations
            http_response_code(201);
            $this->show($patientId);

        } catch (PDOException $e) {
            $this->db->rollBack();
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la création du patient: ' . $e->getMessage()]);
        }
    }
public function update($id) {
    header('Content-Type: application/json');
    
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Données JSON invalides');
        }

        // Log pour débogage
        error_log("Données reçues: " . print_r($data, true));

        // Démarrer une transaction
        $this->db->beginTransaction();

        // 1. Mettre à jour le patient
        $updateFields = [];
        $params = ['id' => $id];

        $fieldsToUpdate = [
            'nom', 'prenom', 'email', 'telephone', 'adresse',
            'date_naissance', 'creneaux_preferes', 
            'user_id', 'numero_securite_sociale'
        ];

        foreach ($fieldsToUpdate as $field) {
            if (array_key_exists($field, $data)) {
                $updateFields[] = "$field = :$field";
                $params[$field] = $data[$field];
            }
        }

        if (!empty($updateFields)) {
            $sql = "UPDATE patients SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            if (!$stmt->execute($params)) {
                throw new Exception("Échec de la mise à jour du patient");
            }
        }

        // 2. Mettre à jour les allergies
        if (isset($data['allergies'])) {
            $this->updateRelatedTable($id, 'allergies', 'allergie', $data['allergies']);
        }

        // 3. Mettre à jour les médicaments
        if (isset($data['medicaments'])) {
            $this->updateRelatedTable($id, 'medicaments', 'medicament', $data['medicaments']);
        }

        // 4. Mettre à jour le contact d'urgence
        if (isset($data['contact_urgence'])) {
            $emergencyContact = $data['contact_urgence'];
            
            // Vérifiez que les champs requis existent
            if (!isset($emergencyContact['nom']) || !isset($emergencyContact['relation']) || !isset($emergencyContact['telephone'])) {
                throw new Exception('Données de contact d\'urgence incomplètes');
            }

            $stmt = $this->db->prepare("
                INSERT INTO contacts_urgence 
                (patient_id, nom, relation, telephone) 
                VALUES (:patient_id, :nom, :relation, :telephone)
                ON DUPLICATE KEY UPDATE
                nom = VALUES(nom),
                relation = VALUES(relation),
                telephone = VALUES(telephone)
            ");
            $stmt->execute([
                'patient_id' => $id,
                'nom' => $emergencyContact['nom'],
                'relation' => $emergencyContact['relation'],
                'telephone' => $emergencyContact['telephone']
            ]);
        }

        // Valider la transaction
        $this->db->commit();

        // Retourner le patient mis à jour
        http_response_code(200);
        $this->show($id);

    } catch (Exception $e) {
        $this->db->rollBack();
        error_log("Erreur mise à jour patient: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'details' => 'Erreur lors de la mise à jour du patient',
            'received_data' => $data // Ajout des données reçues pour débogage
        ]);
    }
}




// Méthode helper pour mettre à jour les tables associatives
private function updateRelatedTable($patientId, $tableName, $columnName, $items) {
    // Supprimer les entrées existantes
    $stmt = $this->db->prepare("DELETE FROM $tableName WHERE patient_id = ?");
    $stmt->execute([$patientId]);

    // Si items est null ou non array, on s'arrête là
    if (!is_array($items)) {
        return;
    }

    // Filtrer les valeurs vides
    $filteredItems = array_filter($items, function($item) {
        return !empty(trim($item));
    });

    // Si tableau vide après filtrage, on s'arrête
    if (empty($filteredItems)) {
        return;
    }

    // Préparation de la requête d'insertion
    $stmt = $this->db->prepare("INSERT INTO $tableName (patient_id, $columnName) VALUES (:patient_id, :value)");
    
    foreach ($filteredItems as $item) {
        $stmt->execute([
            'patient_id' => $patientId,
            'value' => $item
        ]);
    }
}



    public function destroy($id) {
        try {
            // Démarrer une transaction
            $this->db->beginTransaction();

            // 1. Supprimer les allergies
            $stmt = $this->db->prepare("DELETE FROM allergies WHERE patient_id = ?");
            $stmt->execute([$id]);

            // 2. Supprimer les médicaments
            $stmt = $this->db->prepare("DELETE FROM medicaments WHERE patient_id = ?");
            $stmt->execute([$id]);

            // 3. Supprimer le patient
            $stmt = $this->db->prepare("DELETE FROM patients WHERE id = ?");
            $stmt->execute([$id]);

            // Valider la transaction
            $this->db->commit();

            http_response_code(200);
            echo json_encode(['message' => 'Patient supprimé avec succès']);

        } catch (PDOException $e) {
            $this->db->rollBack();
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la suppression du patient: ' . $e->getMessage()]);
        }
    }

    public function export() {
        try {
            // Récupérer les données des patients
            $query = "
                SELECT 
                    p.*,
                    u.email,
                    GROUP_CONCAT(DISTINCT a.allergie SEPARATOR ', ') as allergies,
                    GROUP_CONCAT(DISTINCT m.medicament SEPARATOR ', ') as medicaments
                FROM patients p
                LEFT JOIN utilisateur u ON p.user_id = u.id
                LEFT JOIN allergies a ON p.id = a.patient_id
                LEFT JOIN medicaments m ON p.id = m.patient_id
                GROUP BY p.id
                ORDER BY p.nom, p.prenom
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $patients = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // En-têtes pour le CSV
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename=liste_patients_' . date('Y-m-d') . '.csv');

            // Créer le fichier CSV
            $output = fopen('php://output', 'w');
            
            // BOM UTF-8 pour Excel
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

            // En-têtes des colonnes
            fputcsv($output, array('Nom', 'Prénom', 'Date de naissance', 'Téléphone', 'Email', 'Allergies', 'Médicaments'));

            // Données
            foreach ($patients as $patient) {
                fputcsv($output, array(
                    $patient['nom'],
                    $patient['prenom'],
                    date('d/m/Y', strtotime($patient['date_naissance'])),
                    $patient['telephone'],
                    $patient['email'],
                    $patient['allergies'] ?? '',
                    $patient['medicaments'] ?? ''
                ));
            }

            fclose($output);
            exit();

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de l\'export: ' . $e->getMessage()]);
        }
    }

    public function toggleArchive($id) {
        try {
            // Vérifier si le patient existe
            $stmt = $this->db->prepare("SELECT archived FROM patients WHERE id = ?");
            $stmt->execute([$id]);
            $patient = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$patient) {
                http_response_code(404);
                echo json_encode(['error' => 'Patient non trouvé']);
                return;
            }

            // Récupérer les données JSON du corps de la requête
            $data = json_decode(file_get_contents('php://input'), true);
            $newStatus = isset($data['archived']) ? (bool)$data['archived'] : !$patient['archived'];

            // Mettre à jour le statut d'archivage
            $stmt = $this->db->prepare("UPDATE patients SET archived = ? WHERE id = ?");
            $stmt->execute([$newStatus, $id]);

            // Retourner le nouveau statut
            header('Content-Type: application/json');
            echo json_encode([
                'id' => $id,
                'archived' => $newStatus
            ]);

        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la mise à jour du statut d\'archivage: ' . $e->getMessage()]);
        }
    }
}
