import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/services/api'

export const useStatsStore = defineStore('stats', () => {
  const dashboardStats = ref({
    totalPatients: 0,
    totalDoctors: 0,
    appointmentsToday: 0,
    upcomingAppointments: 0
  })
  const loading = ref(false)
  const error = ref(null)

  async function fetchDashboardStats() {
    loading.value = true
    error.value = null
    try {
      const response = await api.get('/stats/dashboard')
      if (response.data.status === 'success') {
        dashboardStats.value = response.data.data
      } else {
        throw new Error(response.data.message || 'Erreur lors du chargement des statistiques')
      }
    } catch (err) {
      error.value = err.message
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  return {
    dashboardStats,
    loading,
    error,
    fetchDashboardStats
  }
})