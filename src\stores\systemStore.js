import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/services/api'


export const useSystemStore = defineStore('system', () => {
  const settings = ref({})
  const isLoading = ref(false)
  const error = ref(null)

  async function getSettings() {
    try {
      isLoading.value = true
      const response = await api.get('/system/settings')
      settings.value = response.data
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function saveSettings(newSettings) {
    try {
      isLoading.value = true
      const response = await api.put('/system/settings', newSettings)
      settings.value = response.data
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    settings,
    isLoading,
    error,
    getSettings,
    saveSettings
  }
})