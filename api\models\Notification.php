<?php

class Notification {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * <PERSON><PERSON>er une notification
     */
    public function create($notificationData) {
        try {
            $query = "
                INSERT INTO notifications 
                (type, destinataire_id, destinataire_type, rdv_id, sujet, contenu, statut, envoye_le, programme_pour)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $notificationData['type'],
                $notificationData['destinataire_id'],
                $notificationData['destinataire_type'],
                $notificationData['rdv_id'],
                $notificationData['sujet'],
                $notificationData['contenu'],
                $notificationData['statut'] ?? 'en_attente',
                $notificationData['envoye_le'] ?? null,
                $notificationData['programme_pour'] ?? null
            ]);
            
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log("Erreur lors de la création de la notification: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Marquer une notification comme envoyée
     */
    public function markAsSent($notificationId) {
        try {
            $query = "
                UPDATE notifications 
                SET statut = 'envoye', envoye_le = NOW()
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$notificationId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Erreur lors de la mise à jour de la notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Récupérer les notifications en attente d'envoi
     */
    public function getPendingNotifications() {
        try {
            $query = "
                SELECT n.*, 
                       rdv.date_rendez_vous,
                       rdv.duree,
                       rdv.type as rdv_type,
                       rdv.notes,
                       CASE 
                           WHEN n.destinataire_type = 'patient' THEN CONCAT(p.nom, ' ', p.prenom)
                           WHEN n.destinataire_type = 'medecin' THEN CONCAT('Dr. ', u.nom, ' ', u.prenom)
                       END as destinataire_nom,
                       CASE 
                           WHEN n.destinataire_type = 'patient' THEN p.email
                           WHEN n.destinataire_type = 'medecin' THEN u.email
                       END as destinataire_email,
                       CASE 
                           WHEN n.destinataire_type = 'patient' THEN p.telephone
                           ELSE NULL
                       END as destinataire_telephone
                FROM notifications n
                INNER JOIN rendez_vous rdv ON n.rdv_id = rdv.id
                LEFT JOIN patients p ON n.destinataire_type = 'patient' AND n.destinataire_id = p.id
                LEFT JOIN medecins m ON n.destinataire_type = 'medecin' AND n.destinataire_id = m.id
                LEFT JOIN utilisateur u ON m.user_id = u.id
                WHERE n.statut = 'en_attente'
                AND (n.programme_pour IS NULL OR n.programme_pour <= NOW())
                ORDER BY n.programme_pour ASC, n.cree_le ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les RDV nécessitant des rappels
     */
    public function getAppointmentsNeedingReminders() {
        try {
            // RDV dans les prochaines 24h qui n'ont pas encore eu de rappel
            $query = "
                SELECT rdv.*, 
                       CONCAT(p.nom, ' ', p.prenom) as patient_nom,
                       p.email as patient_email,
                       p.telephone as patient_telephone,
                       CONCAT('Dr. ', u.nom, ' ', u.prenom) as medecin_nom,
                       u.email as medecin_email,
                       m.specialite
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                INNER JOIN medecins m ON rdv.id_medecin = m.id
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE rdv.statut = 'planifie'
                AND rdv.date_rendez_vous BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 24 HOUR)
                AND NOT EXISTS (
                    SELECT 1 FROM notifications n 
                    WHERE n.rdv_id = rdv.id 
                    AND n.type = 'rappel' 
                    AND n.statut = 'envoye'
                )
                ORDER BY rdv.date_rendez_vous ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des RDV pour rappels: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Créer les notifications de confirmation pour un RDV
     */
    public function createConfirmationNotifications($rdvId) {
        try {
            // Récupérer les données du RDV
            $rdvData = $this->getAppointmentData($rdvId);
            
            if (!$rdvData) {
                throw new Exception("RDV non trouvé: $rdvId");
            }
            
            $notifications = [];
            
            // Notification pour le patient
            $patientNotificationId = $this->create([
                'type' => 'confirmation',
                'destinataire_id' => $rdvData['id_patient'],
                'destinataire_type' => 'patient',
                'rdv_id' => $rdvId,
                'sujet' => 'Confirmation de votre rendez-vous médical',
                'contenu' => 'Votre rendez-vous a été confirmé',
                'statut' => 'en_attente'
            ]);
            
            $notifications[] = $patientNotificationId;
            
            // Notification pour le médecin
            $doctorNotificationId = $this->create([
                'type' => 'notification',
                'destinataire_id' => $rdvData['id_medecin'],
                'destinataire_type' => 'medecin',
                'rdv_id' => $rdvId,
                'sujet' => 'Nouveau rendez-vous programmé',
                'contenu' => 'Un nouveau rendez-vous a été ajouté à votre planning',
                'statut' => 'en_attente'
            ]);
            
            $notifications[] = $doctorNotificationId;
            
            return $notifications;
        } catch (Exception $e) {
            error_log("Erreur lors de la création des notifications de confirmation: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Créer les notifications de rappel pour un RDV
     */
    public function createReminderNotifications($rdvId) {
        try {
            $rdvData = $this->getAppointmentData($rdvId);
            
            if (!$rdvData) {
                throw new Exception("RDV non trouvé: $rdvId");
            }
            
            $notifications = [];
            
            // Rappel pour le patient (24h avant)
            $patientReminderId = $this->create([
                'type' => 'rappel',
                'destinataire_id' => $rdvData['id_patient'],
                'destinataire_type' => 'patient',
                'rdv_id' => $rdvId,
                'sujet' => 'Rappel : Votre rendez-vous médical demain',
                'contenu' => 'Rappel de votre rendez-vous de demain',
                'statut' => 'en_attente',
                'programme_pour' => date('Y-m-d H:i:s', strtotime($rdvData['date_rendez_vous']) - 24*3600)
            ]);
            
            $notifications[] = $patientReminderId;
            
            // Rappel pour le médecin (24h avant)
            $doctorReminderId = $this->create([
                'type' => 'rappel',
                'destinataire_id' => $rdvData['id_medecin'],
                'destinataire_type' => 'medecin',
                'rdv_id' => $rdvId,
                'sujet' => 'Rappel : Rendez-vous patient demain',
                'contenu' => 'Rappel de votre rendez-vous de demain',
                'statut' => 'en_attente',
                'programme_pour' => date('Y-m-d H:i:s', strtotime($rdvData['date_rendez_vous']) - 24*3600)
            ]);
            
            $notifications[] = $doctorReminderId;
            
            return $notifications;
        } catch (Exception $e) {
            error_log("Erreur lors de la création des notifications de rappel: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Récupérer les données complètes d'un RDV
     */
    private function getAppointmentData($rdvId) {
        try {
            $query = "
                SELECT rdv.*, 
                       CONCAT(p.nom, ' ', p.prenom) as patient_nom,
                       p.email as patient_email,
                       p.telephone as patient_telephone,
                       CONCAT('Dr. ', u.nom, ' ', u.prenom) as medecin_nom,
                       u.email as medecin_email,
                       m.specialite
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                INNER JOIN medecins m ON rdv.id_medecin = m.id
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE rdv.id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$rdvId]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération des données RDV: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Nettoyer les anciennes notifications
     */
    public function cleanOldNotifications($daysOld = 30) {
        try {
            $query = "
                DELETE FROM notifications 
                WHERE cree_le < DATE_SUB(NOW(), INTERVAL ? DAY)
                AND statut = 'envoye'
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$daysOld]);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Erreur lors du nettoyage des notifications: " . $e->getMessage());
            return 0;
        }
    }
}
?>
