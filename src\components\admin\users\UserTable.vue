<template>
  <div class="user-management-container">
    <div class="header-section">
      <h2 class="page-title">
        <i class="fas fa-users-cog"></i>
        Gestion des Utilisateurs
      </h2>
      <button @click="openUserForm()" class="add-user-btn">
        <i class="fas fa-plus"></i>
        Ajouter un utilisateur
      </button>
    </div>

    <div class="table-container">
      <table class="table">
        <thead class="table-header">
          <tr>
            <th v-for="header in headers" :key="header.key" class="table-header-cell">
              <i :class="header.icon"></i>
              {{ header.label }}
            </th>
            <th class="table-header-cell">
              <i class="fas fa-cog"></i>
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="(user, index) in users" 
            :key="user.id" 
            class="table-row"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <td class="table-cell user-name">{{ user.nom }} {{ user.prenom }}</td>
            <td class="table-cell user-email">{{ user.email }}</td>
            <td class="table-cell">
              <UserRoleBadge :role="user.role" />
            </td>
            <td class="table-cell last-login">{{ formatDate(user.lastLogin) }}</td>
            <td class="table-cell">
              <div class="action-buttons">
                <button @click="openUserForm(user)" class="action-btn edit-btn">
                  <i class="fas fa-edit"></i>
                </button>
                <button @click="deleteUser(user.id)" class="action-btn delete-btn">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <div v-if="users.length === 0" class="empty-state">
        <i class="fas fa-users"></i>
        <p>Aucun utilisateur trouvé</p>
      </div>
    </div>

    <UserForm
      v-if="showUserForm"
      :user="selectedUser"
      :is-editing="isEditing"
      :show="showUserForm"
      @close="showUserForm = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAdminStore } from '@/stores/adminStore'
import UserRoleBadge from './UserRoleBadge.vue'
import UserForm from './UserForm.vue'

const adminStore = useAdminStore()
const users = computed(() => adminStore.users)

const showUserForm = ref(false)
const selectedUser = ref(null)
const isEditing = ref(false)

const headers = ref([
  { key: 'name', label: 'Nom', icon: 'fas fa-user' },
  { key: 'email', label: 'Email', icon: 'fas fa-envelope' },
  { key: 'role', label: 'Rôle', icon: 'fas fa-shield-alt' },
  { key: 'lastLogin', label: 'Dernière connexion', icon: 'fas fa-clock' }
])

onMounted(() => {
  adminStore.fetchUsers()
})

function openUserForm(user = null) {
  if (user) {
    selectedUser.value = { ...user }
    isEditing.value = true
  } else {
    selectedUser.value = {}
    isEditing.value = false
  }
  showUserForm.value = true
}

async function deleteUser(userId) {
  if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
    await adminStore.deleteUser(userId)
  }
}

function formatDate(dateStr) {
  if (!dateStr) return 'N/A'
  return new Date(dateStr).toLocaleDateString('fr-FR', { 
    day: '2-digit', 
    month: '2-digit', 
    year: 'numeric' 
  })
}
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.user-management-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background: linear-gradient(135deg,rgb(5, 189, 63) 0%,rgb(39, 137, 39) 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #1a202c;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  background: rgba(181, 250, 205, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(63, 183, 69, 0.1);
  background: linear-gradient(135deg,rgb(255, 255, 255), #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.add-user-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg,rgb(49, 214, 102),rgb(117, 220, 13));
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(8, 17, 3, 0.4);
  position: relative;
  overflow: hidden;
}

.add-user-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(56, 147, 75, 0.2), transparent);
  transition: left 0.5s;
}

.add-user-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(223, 225, 221, 0.6);
  background: linear-gradient(135deg,rgb(175, 196, 180),rgb(19, 188, 62));
}

.add-user-btn:hover::before {
  left: 100%;
}

.table-container {
  background: rgba(209, 223, 207, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table-header {
  background: linear-gradient(135deg,rgb(87, 204, 124), #e2e8f0);
  border-bottom: 1px solid #e2e8f0;
}

.table-header-cell {
  padding: 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 700;
  color:rgb(42, 220, 75);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
}

.table-header-cell i {
  margin-right: 0.5rem;
}

.table-header-cell::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg,rgb(53, 206, 71),rgb(7, 88, 41));
  transition: width 0.3s ease;
}

.table-header-cell:hover::after {
  width: 100%;
}

.table-row {
  transition: all 0.3s ease;
  border-bottom: 1px solidrgb(118, 225, 141);
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.table-row:hover {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  transform: scale(1.002);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.table-cell {
  padding: 1.5rem;
  font-size: 0.95rem;
  color: #334155;
  vertical-align: middle;
}

.user-name {
  font-weight: 600;
  color: #1e293b;
}

.user-email {
  color: #64748b;
  font-family: 'Monaco', monospace;
}

.last-login {
  color: #64748b;
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.edit-btn {
  background: linear-gradient(135deg,rgb(118, 187, 129),rgb(158, 234, 191));
  color:rgb(4, 44, 13);
  border: 1px solid #a5b4fc;
}

.edit-btn:hover {
  background: linear-gradient(135deg,rgb(97, 221, 202),rgb(54, 210, 213));
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.delete-btn {
  background: linear-gradient(135deg,rgb(120, 226, 238),rgb(57, 181, 227));
  color:rgb(71, 81, 80);
  border: 1px solidrgb(53, 225, 196);
}

.delete-btn:hover {
  background: linear-gradient(135deg,rgb(58, 186, 186),rgb(30, 205, 173));
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 163, 143, 0.4);
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .page-title {
    font-size: 2rem;
  }

  .table-container {
    overflow-x: auto;
  }

  .table-cell {
    padding: 1rem;
    font-size: 0.875rem;
  }
}

/* Styles pour le composant UserRoleBadge */
:deep(.role-badge) {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

:deep(.role-admin) {
  background: linear-gradient(135deg,rgb(127, 207, 172), #fecaca);
  color: #dc2626;
  border: 1px solid #fca5a5;
}

:deep(.role-manager) {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #2563eb;
  border: 1px solid #93c5fd;
}

:deep(.role-user) {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #059669;
  border: 1px solid #6ee7b7;
}
</style>