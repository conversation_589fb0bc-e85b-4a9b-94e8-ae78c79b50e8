<template>
  <label class="toggle-switch">
    <input
      type="checkbox"
      :checked="modelValue"
      @change="$emit('update:modelValue', $event.target.checked)"
      class="toggle-switch-input"
      :aria-checked="modelValue"
      role="switch"
    />
    <span class="toggle-switch-slider"></span>
    <span class="toggle-switch-label">
      <slot>{{ modelValue ? onLabel : offLabel }}</slot>
    </span>
  </label>
</template>

<script setup>
defineProps({
  modelValue: Boolean,
  onLabel: { type: String, default: 'On' },
  offLabel: { type: String, default: 'Off' }
})
defineEmits(['update:modelValue'])
</script>

<style scoped>
.toggle-switch {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}
.toggle-switch-input {
  display: none;
}
.toggle-switch-slider {
  width: 40px;
  height: 22px;
  background: #d1d5db;
  border-radius: 22px;
  position: relative;
  transition: background 0.2s;
  margin-right: 0.5em;
}
.toggle-switch-slider::before {
  content: '';
  position: absolute;
  left: 2px;
  top: 2px;
  width: 18px;
  height: 18px;
  background: #fff;
  border-radius: 50%;
  transition: transform 0.2s;
}
.toggle-switch-input:checked + .toggle-switch-slider {
  background: #2563eb;
}
.toggle-switch-input:checked + .toggle-switch-slider::before {
  transform: translateX(18px);
}
.toggle-switch-label {
  font-size: 1em;
  color: #334155;
}
</style>
