<template>
  <div class="conflict-type-manager">
    <div class="manager-header">
      <h2>
        <i class="fas fa-cogs"></i>
        Gestionnaire des Types de Conflits
      </h2>
      <div class="header-actions">
        <button @click="refreshTypes" class="refresh-btn" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Actualiser
        </button>
        <button @click="showCreateModal = true" class="create-btn">
          <i class="fas fa-plus"></i>
          Nouveau Type
        </button>
      </div>
    </div>

    <!-- Messages de notification -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <div class="notification-content">
        <i :class="notification.icon"></i>
        <span>{{ notification.message }}</span>
      </div>
      <button @click="hideNotification" class="notification-close">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Statistiques des types de conflits -->
    <div class="conflict-stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-list"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ Object.keys(conflictTypes).length }}</div>
          <div class="stat-label">Types disponibles</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon critical">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ criticalTypesCount }}</div>
          <div class="stat-label">Types critiques</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ mostUsedType?.label || 'N/A' }}</div>
          <div class="stat-label">Plus fréquent</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon database">
          <i class="fas fa-database"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dataSource }}</div>
          <div class="stat-label">Source des données</div>
        </div>
      </div>
    </div>

    <!-- Liste des types de conflits -->
    <div class="conflict-types-list">
      <div class="list-header">
        <h3>Types de Conflits Configurés</h3>
        <div class="list-filters">
          <select v-model="selectedSeverity" class="filter-select">
            <option value="">Toutes les sévérités</option>
            <option value="faible">Faible</option>
            <option value="moyenne">Moyenne</option>
            <option value="elevee">Élevée</option>
            <option value="critique">Critique</option>
          </select>
        </div>
      </div>

      <div class="types-grid">
        <div 
          v-for="(type, key) in filteredConflictTypes" 
          :key="key"
          class="type-card"
          :class="type.severity_default"
        >
          <div class="type-header">
            <div class="type-icon" :style="{ color: type.color }">
              <i :class="type.icon"></i>
            </div>
            <div class="type-info">
              <h4>{{ type.label }}</h4>
              <span class="type-key">{{ key }}</span>
            </div>
            <div class="type-actions">
              <button @click="editType(key, type)" class="edit-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="deleteType(key)" class="delete-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="type-description">
            {{ type.description }}
          </div>
          
          <div class="type-details">
            <div class="detail-item">
              <span class="detail-label">Sévérité par défaut:</span>
              <span class="severity-badge" :class="type.severity_default">
                {{ type.severity_default }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Couleur:</span>
              <div class="color-indicator" :style="{ backgroundColor: type.color }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de création/édition -->
    <div v-if="showCreateModal || editingType" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>
            <i class="fas fa-cog"></i>
            {{ editingType ? 'Modifier le Type' : 'Nouveau Type de Conflit' }}
          </h3>
          <button @click="closeModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <form @submit.prevent="saveType" class="type-form">
            <div class="form-group">
              <label>Clé du type:</label>
              <input 
                v-model="typeForm.key" 
                type="text" 
                class="form-input"
                :disabled="!!editingType"
                placeholder="ex: nouveau_type_conflit"
                required
              >
            </div>
            
            <div class="form-group">
              <label>Libellé:</label>
              <input 
                v-model="typeForm.label" 
                type="text" 
                class="form-input"
                placeholder="ex: Nouveau Type de Conflit"
                required
              >
            </div>
            
            <div class="form-group">
              <label>Description:</label>
              <textarea 
                v-model="typeForm.description" 
                class="form-textarea"
                placeholder="Description détaillée du type de conflit"
                required
              ></textarea>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label>Sévérité par défaut:</label>
                <select v-model="typeForm.severity_default" class="form-select" required>
                  <option value="faible">Faible</option>
                  <option value="moyenne">Moyenne</option>
                  <option value="elevee">Élevée</option>
                  <option value="critique">Critique</option>
                </select>
              </div>
              
              <div class="form-group">
                <label>Couleur:</label>
                <input 
                  v-model="typeForm.color" 
                  type="color" 
                  class="form-color"
                  required
                >
              </div>
            </div>
            
            <div class="form-group">
              <label>Icône (classe FontAwesome):</label>
              <input 
                v-model="typeForm.icon" 
                type="text" 
                class="form-input"
                placeholder="ex: fas fa-exclamation-triangle"
                required
              >
              <div class="icon-preview">
                <i :class="typeForm.icon" :style="{ color: typeForm.color }"></i>
                Aperçu de l'icône
              </div>
            </div>
            
            <div class="form-actions">
              <button type="button" @click="closeModal" class="btn-cancel">
                Annuler
              </button>
              <button type="submit" class="btn-save" :disabled="saving">
                <i class="fas fa-save"></i>
                {{ saving ? 'Enregistrement...' : 'Enregistrer' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div v-if="deletingType" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content small" @click.stop>
        <div class="modal-header danger">
          <h3>
            <i class="fas fa-exclamation-triangle"></i>
            Confirmer la suppression
          </h3>
        </div>
        
        <div class="modal-body">
          <p>Êtes-vous sûr de vouloir supprimer le type de conflit <strong>{{ deletingType.label }}</strong> ?</p>
          <p class="warning-text">Cette action est irréversible.</p>
          
          <div class="form-actions">
            <button @click="cancelDelete" class="btn-cancel">
              Annuler
            </button>
            <button @click="confirmDelete" class="btn-delete" :disabled="deleting">
              <i class="fas fa-trash"></i>
              {{ deleting ? 'Suppression...' : 'Supprimer' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import conflictService from '@/services/conflictService'
import conflictTypeService from '@/services/conflictTypeService'

// Variables réactives
const conflictTypes = ref({})
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const selectedSeverity = ref('')
const showCreateModal = ref(false)
const editingType = ref(null)
const deletingType = ref(null)
const dataSource = ref('Base de données')

// Notifications
const notification = ref({
  show: false,
  type: 'success', // success, error, warning, info
  message: '',
  icon: 'fas fa-check-circle'
})

// Formulaire pour créer/éditer un type
const typeForm = ref({
  key: '',
  label: '',
  description: '',
  severity_default: 'moyenne',
  icon: 'fas fa-exclamation-circle',
  color: '#ffc107'
})

// Computed properties
const filteredConflictTypes = computed(() => {
  if (!selectedSeverity.value) return conflictTypes.value
  
  return Object.fromEntries(
    Object.entries(conflictTypes.value).filter(([key, type]) => 
      type.severity_default === selectedSeverity.value
    )
  )
})

const criticalTypesCount = computed(() => {
  return Object.values(conflictTypes.value).filter(type => 
    type.severity_default === 'critique'
  ).length
})

const mostUsedType = computed(() => {
  // Pour l'instant, retourne le premier type critique trouvé
  // Dans une vraie implémentation, cela viendrait des statistiques d'usage
  const criticalTypes = Object.values(conflictTypes.value).filter(type => 
    type.severity_default === 'critique'
  )
  return criticalTypes[0] || Object.values(conflictTypes.value)[0]
})

// Méthodes
const loadConflictTypes = async () => {
  try {
    loading.value = true
    const response = await conflictTypeService.getAll()
    conflictTypes.value = response.data

    // Déterminer la source des données
    if (response.source === 'database') {
      dataSource.value = 'Base de données'
    } else if (response.source === 'hardcoded') {
      dataSource.value = 'Hardcodé'
      showNotification('info', 'Types chargés depuis la configuration par défaut', 'fas fa-info-circle')
    } else {
      dataSource.value = 'Fallback'
      showNotification('warning', 'Utilisation des types de secours', 'fas fa-exclamation-triangle')
    }

    console.log('Types de conflits chargés:', conflictTypes.value)
  } catch (error) {
    console.error('Erreur lors du chargement des types:', error)
    // Fallback avec les types hardcodés
    conflictTypes.value = await conflictService.getConflictTypes()
    dataSource.value = 'Fallback'
    showNotification('error', 'Erreur lors du chargement, utilisation des types par défaut', 'fas fa-exclamation-circle')
  } finally {
    loading.value = false
  }
}

const refreshTypes = async () => {
  await loadConflictTypes()
}

const editType = (key, type) => {
  editingType.value = key
  typeForm.value = {
    key: key,
    label: type.label,
    description: type.description,
    severity_default: type.severity_default,
    icon: type.icon,
    color: type.color
  }
}

const deleteType = (key) => {
  const type = conflictTypes.value[key]
  deletingType.value = { key, ...type }
}

const saveType = async () => {
  try {
    saving.value = true

    // Validation des données
    const validation = conflictTypeService.validateTypeData(typeForm.value)
    if (!validation.isValid) {
      showNotification('error', `Erreurs de validation: ${validation.errors.join(', ')}`, 'fas fa-exclamation-circle')
      return
    }

    const typeData = conflictTypeService.formatForApi(typeForm.value)

    if (editingType.value) {
      // Mise à jour d'un type existant
      const existingType = conflictTypes.value[editingType.value]
      await conflictTypeService.update(existingType.id, typeData)
      showNotification('success', `Type "${typeForm.value.label}" mis à jour avec succès`, 'fas fa-check-circle')
    } else {
      // Création d'un nouveau type
      await conflictTypeService.create(typeData)
      showNotification('success', `Type "${typeForm.value.label}" créé avec succès`, 'fas fa-plus-circle')
    }

    // Recharger la liste
    await loadConflictTypes()
    closeModal()

  } catch (error) {
    console.error('Erreur lors de la sauvegarde:', error)
    showNotification('error', 'Erreur lors de la sauvegarde du type de conflit', 'fas fa-exclamation-circle')
  } finally {
    saving.value = false
  }
}

const confirmDelete = async () => {
  try {
    deleting.value = true

    const typeToDelete = conflictTypes.value[deletingType.value.key]
    const response = await conflictTypeService.delete(typeToDelete.id)

    // Afficher le message approprié selon le type de suppression
    if (response.message.includes('désactivé')) {
      showNotification('warning', `Type "${deletingType.value.label}" désactivé (utilisé dans des conflits existants)`, 'fas fa-eye-slash')
    } else {
      showNotification('success', `Type "${deletingType.value.label}" supprimé avec succès`, 'fas fa-trash')
    }

    // Recharger la liste
    await loadConflictTypes()
    cancelDelete()

  } catch (error) {
    console.error('Erreur lors de la suppression:', error)
    showNotification('error', 'Erreur lors de la suppression du type de conflit', 'fas fa-exclamation-circle')
  } finally {
    deleting.value = false
  }
}

const closeModal = () => {
  showCreateModal.value = false
  editingType.value = null
  typeForm.value = {
    key: '',
    label: '',
    description: '',
    severity_default: 'moyenne',
    icon: 'fas fa-exclamation-circle',
    color: '#ffc107'
  }
}

const cancelDelete = () => {
  deletingType.value = null
}

// Méthodes de notification
const showNotification = (type, message, icon) => {
  notification.value = {
    show: true,
    type,
    message,
    icon
  }

  // Auto-hide après 5 secondes
  setTimeout(() => {
    hideNotification()
  }, 5000)
}

const hideNotification = () => {
  notification.value.show = false
}

// Hooks de cycle de vie
onMounted(async () => {
  await loadConflictTypes()
})
</script>

<style scoped>
.conflict-type-manager {
  padding: 2rem;
  background: #f8f9fa;
  min-height: 100vh;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.manager-header h2 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.refresh-btn, .create-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn {
  background: #6c757d;
  color: white;
}

.refresh-btn:hover {
  background: #5a6268;
}

.create-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.conflict-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  font-size: 1.5rem;
}

.stat-icon.critical {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-icon.database {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.conflict-types-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.list-header h3 {
  margin: 0;
  color: #2c3e50;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #495057;
  cursor: pointer;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.type-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #dee2e6;
}

.type-card.faible::before {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.type-card.moyenne::before {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.type-card.elevee::before {
  background: linear-gradient(90deg, #fd7e14, #e74c3c);
}

.type-card.critique::before {
  background: linear-gradient(90deg, #e74c3c, #dc3545);
}

.type-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.type-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.type-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.type-info {
  flex: 1;
}

.type-info h4 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.type-key {
  font-size: 0.8rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: monospace;
}

.type-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn, .delete-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-btn {
  background: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #138496;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

.type-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.type-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.severity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.severity-badge.faible {
  background: #d4edda;
  color: #155724;
}

.severity-badge.moyenne {
  background: #fff3cd;
  color: #856404;
}

.severity-badge.elevee {
  background: #f8d7da;
  color: #721c24;
}

.severity-badge.critique {
  background: #f5c6cb;
  color: #721c24;
}

.color-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #dee2e6;
}

/* Styles pour les modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.small {
  max-width: 400px;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header.danger {
  background: #f8d7da;
  color: #721c24;
}

.modal-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 1.5rem;
}

.type-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.form-input, .form-textarea, .form-select {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-color {
  padding: 0.25rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  width: 60px;
  height: 40px;
  cursor: pointer;
}

.icon-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.8rem;
  color: #6c757d;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-cancel, .btn-save, .btn-delete {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
}

.btn-save {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.btn-save:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.btn-delete {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.btn-delete:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
}

.warning-text {
  color: #721c24;
  font-style: italic;
  margin-top: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .conflict-type-manager {
    padding: 1rem;
  }

  .manager-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .types-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInRight 0.3s ease-out;
}

.notification.success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.notification.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.notification.warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.notification.info {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.notification-content i {
  font-size: 1.2rem;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.notification-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
