// services/calendarsync.js

// Pour Google Calendar API, il faut généralement OAuth2 côté client ou serveur.
// Ici, on suppose que tu as déjà un token OAuth valide.

const GOOGLE_CALENDAR_API = 'https://www.googleapis.com/calendar/v3';

export class CalendarSyncService {
  constructor(accessToken) {
    this.accessToken = accessToken;
  }

  // Ajouter un rendez-vous au calendrier Google
  async addEvent(appointment, calendarId = 'primary') {
    const event = {
      summary: appointment.type || 'Rendez-vous médical',
      description: appointment.notes || '',
      start: {
        dateTime: appointment.appointment_date,
        timeZone: 'Europe/Paris'
      },
      end: {
        dateTime: this._getEndTime(appointment.appointment_date, appointment.duration),
        timeZone: 'Europe/Paris'
      }
    };

    const response = await fetch(
      `${GOOGLE_CALENDAR_API}/calendars/${calendarId}/events`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(event)
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Erreur lors de l’ajout à Google Calendar');
    }
    return await response.json();
  }

  // Supprimer un événement du calendrier Google
  async deleteEvent(eventId, calendarId = 'primary') {
    const response = await fetch(
      `${GOOGLE_CALENDAR_API}/calendars/${calendarId}/events/${eventId}`,
      {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      }
    );
    if (!response.ok) {
      throw new Error('Erreur lors de la suppression de l’événement');
    }
    return true;
  }

  // Mettre à jour un événement
  async updateEvent(eventId, appointment, calendarId = 'primary') {
    const event = {
      summary: appointment.type || 'Rendez-vous médical',
      description: appointment.notes || '',
      start: {
        dateTime: appointment.appointment_date,
        timeZone: 'Europe/Paris'
      },
      end: {
        dateTime: this._getEndTime(appointment.appointment_date, appointment.duration),
        timeZone: 'Europe/Paris'
      }
    };

    const response = await fetch(
      `${GOOGLE_CALENDAR_API}/calendars/${calendarId}/events/${eventId}`,
      {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(event)
      }
    );
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Erreur lors de la mise à jour de l’événement');
    }
    return await response.json();
  }

  // Méthode utilitaire pour calculer la fin du rendez-vous
  _getEndTime(startDateTime, durationMinutes) {
    const start = new Date(startDateTime);
    start.setMinutes(start.getMinutes() + durationMinutes);
    return start.toISOString();
  }
}
