<?php

class AppointmentStatusUpdater {
    private $pdo;
    private $logFile;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->logFile = __DIR__ . '/../logs/appointment_status_updates.log';
        
        // C<PERSON>er le dossier logs s'il n'existe pas
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * Met à jour automatiquement les statuts des rendez-vous
     */
    public function updateExpiredAppointments() {
        try {
            $this->log("🔄 Début de la mise à jour automatique des statuts");

            // Récupérer les rendez-vous qui devraient être terminés
            $expiredAppointments = $this->getExpiredAppointments();
            
            if (empty($expiredAppointments)) {
                $this->log("✅ Aucun rendez-vous à mettre à jour");
                return ['updated' => 0, 'message' => 'Aucun rendez-vous à mettre à jour'];
            }

            $updatedCount = 0;
            
            foreach ($expiredAppointments as $appointment) {
                if ($this->updateAppointmentStatus($appointment)) {
                    $updatedCount++;
                    $this->log("✅ RDV #{$appointment['id']} mis à jour: {$appointment['patient_nom']} - {$appointment['date_rendez_vous']}");
                }
            }

            $this->log("🎯 Mise à jour terminée: {$updatedCount} rendez-vous mis à jour");
            
            return [
                'updated' => $updatedCount,
                'message' => "{$updatedCount} rendez-vous mis à jour automatiquement"
            ];

        } catch (Exception $e) {
            $this->log("❌ Erreur lors de la mise à jour: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Récupère les rendez-vous qui devraient être terminés
     */
    private function getExpiredAppointments() {
        $sql = "
            SELECT 
                r.id,
                r.date_rendez_vous,
                r.duree,
                r.statut,
                CONCAT(p.prenom, ' ', p.nom) as patient_nom,
                CONCAT(m.prenom, ' ', m.nom) as medecin_nom
            FROM rendez_vous r
            LEFT JOIN patients p ON r.id_patient = p.id
            LEFT JOIN medecins m ON r.id_medecin = m.id
            WHERE r.statut IN ('planifie', 'confirme', 'planifier', 'confirmer')
            AND TIMESTAMPADD(MINUTE, r.duree, r.date_rendez_vous) < NOW()
            ORDER BY r.date_rendez_vous ASC
        ";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Met à jour le statut d'un rendez-vous spécifique
     */
    private function updateAppointmentStatus($appointment) {
        try {
            $sql = "
                UPDATE rendez_vous 
                SET statut = 'termine',
                    updated_at = NOW()
                WHERE id = ?
            ";

            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$appointment['id']]);

        } catch (Exception $e) {
            $this->log("❌ Erreur lors de la mise à jour du RDV #{$appointment['id']}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Met à jour un rendez-vous spécifique par ID
     */
    public function updateSingleAppointment($appointmentId) {
        try {
            // Vérifier si le rendez-vous doit être terminé
            $sql = "
                SELECT 
                    r.id,
                    r.date_rendez_vous,
                    r.duree,
                    r.statut,
                    CONCAT(p.prenom, ' ', p.nom) as patient_nom
                FROM rendez_vous r
                LEFT JOIN patients p ON r.id_patient = p.id
                WHERE r.id = ?
                AND r.statut IN ('planifie', 'confirme', 'planifier', 'confirmer')
                AND TIMESTAMPADD(MINUTE, r.duree, r.date_rendez_vous) < NOW()
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$appointmentId]);
            $appointment = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($appointment) {
                if ($this->updateAppointmentStatus($appointment)) {
                    $this->log("✅ RDV #{$appointmentId} mis à jour individuellement");
                    return true;
                }
            }

            return false;

        } catch (Exception $e) {
            $this->log("❌ Erreur lors de la mise à jour individuelle du RDV #{$appointmentId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Vérifie et met à jour les rendez-vous d'un médecin spécifique
     */
    public function updateDoctorAppointments($doctorId) {
        try {
            $this->log("🔄 Mise à jour des RDV pour le médecin #{$doctorId}");

            $sql = "
                SELECT 
                    r.id,
                    r.date_rendez_vous,
                    r.duree,
                    r.statut,
                    CONCAT(p.prenom, ' ', p.nom) as patient_nom
                FROM rendez_vous r
                LEFT JOIN patients p ON r.id_patient = p.id
                WHERE r.id_medecin = ?
                AND r.statut IN ('planifie', 'confirme', 'planifier', 'confirmer')
                AND TIMESTAMPADD(MINUTE, r.duree, r.date_rendez_vous) < NOW()
                ORDER BY r.date_rendez_vous ASC
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$doctorId]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $updatedCount = 0;
            foreach ($appointments as $appointment) {
                if ($this->updateAppointmentStatus($appointment)) {
                    $updatedCount++;
                }
            }

            $this->log("✅ {$updatedCount} RDV mis à jour pour le médecin #{$doctorId}");
            
            return [
                'updated' => $updatedCount,
                'message' => "{$updatedCount} rendez-vous mis à jour pour ce médecin"
            ];

        } catch (Exception $e) {
            $this->log("❌ Erreur lors de la mise à jour des RDV du médecin #{$doctorId}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Obtient les statistiques des mises à jour
     */
    public function getUpdateStats() {
        try {
            // Compter les RDV qui devraient être terminés
            $sql = "
                SELECT COUNT(*) as expired_count
                FROM rendez_vous 
                WHERE statut IN ('planifie', 'confirme', 'planifier', 'confirmer')
                AND TIMESTAMPADD(MINUTE, duree, date_rendez_vous) < NOW()
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $expiredCount = $stmt->fetch(PDO::FETCH_ASSOC)['expired_count'];

            // Compter les RDV terminés aujourd'hui
            $sql = "
                SELECT COUNT(*) as completed_today
                FROM rendez_vous 
                WHERE statut = 'termine'
                AND DATE(updated_at) = CURDATE()
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $completedToday = $stmt->fetch(PDO::FETCH_ASSOC)['completed_today'];

            return [
                'expired_pending' => $expiredCount,
                'completed_today' => $completedToday,
                'last_check' => date('Y-m-d H:i:s')
            ];

        } catch (Exception $e) {
            $this->log("❌ Erreur lors de la récupération des statistiques: " . $e->getMessage());
            return [
                'expired_pending' => 0,
                'completed_today' => 0,
                'last_check' => date('Y-m-d H:i:s'),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Log des opérations
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        // Écrire dans le fichier de log
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // Afficher aussi dans la console si en mode debug
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            echo $logMessage;
        }
    }

    /**
     * Nettoie les anciens logs (garde les 30 derniers jours)
     */
    public function cleanOldLogs() {
        try {
            if (file_exists($this->logFile)) {
                $lines = file($this->logFile);
                $cutoffDate = date('Y-m-d', strtotime('-30 days'));
                
                $filteredLines = array_filter($lines, function($line) use ($cutoffDate) {
                    if (preg_match('/\[(\d{4}-\d{2}-\d{2})/', $line, $matches)) {
                        return $matches[1] >= $cutoffDate;
                    }
                    return true;
                });
                
                file_put_contents($this->logFile, implode('', $filteredLines));
                $this->log("🧹 Anciens logs nettoyés (gardé 30 derniers jours)");
            }
        } catch (Exception $e) {
            $this->log("❌ Erreur lors du nettoyage des logs: " . $e->getMessage());
        }
    }
}
