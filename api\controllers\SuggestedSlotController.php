<?php
require_once __DIR__ . '/../models/SuggestedSlot.php';

class SuggestedSlotController {
    private $db;
    private $suggestedSlotModel;
    
    public function __construct($database) {
        $this->db = $database;
        $this->suggestedSlotModel = new SuggestedSlot($database);
    }
    
    /**
     * Récupérer tous les créneaux suggérés d'un médecin
     */
    public function index() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $includeExpired = isset($_GET['include_expired']) && $_GET['include_expired'] === 'true';

            // Permettre la récupération de tous les créneaux si aucun médecin spécifié
            $slots = $this->suggestedSlotModel->getByDoctor($medecinId, $includeExpired);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $slots
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->index: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des créneaux suggérés'
            ]);
        }
    }
    
    /**
     * Créer un nouveau créneau suggéré
     */
    public function store() {
        try {
            $rawData = file_get_contents("php://input");
            $data = json_decode($rawData, true);
            
            error_log("Données reçues pour création créneau suggéré: " . $rawData);
            
            // Validation des données requises
            $requiredFields = ['id_medecin', 'date_heure_suggeree', 'duree'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Champs manquants: ' . implode(', ', $missingFields),
                    'received_data' => array_keys($data)
                ]);
                return;
            }
            
            // Vérifier les conflits
            $conflicts = $this->suggestedSlotModel->checkConflicts(
                $data['id_medecin'],
                $data['date_heure_suggeree'],
                $data['duree']
            );
            
            if (!empty($conflicts)) {
                http_response_code(409);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Conflit détecté avec des rendez-vous existants',
                    'conflicts' => $conflicts
                ]);
                return;
            }
            
            // Calculer le score de confiance (1.0 pour les créneaux disponibles)
            $scoreConfiance = isset($data['id_patient']) && $data['id_patient'] ?
                $this->suggestedSlotModel->calculateConfidenceScore(
                    $data['id_medecin'],
                    $data['id_patient'],
                    $data['date_heure_suggeree']
                ) : 1.0;
            
            // Calculer la date d'expiration (par défaut 7 jours)
            $expireDays = $data['expire_days'] ?? 7;
            $expireLe = date('Y-m-d H:i:s', strtotime("+{$expireDays} days"));
            
            $slotData = [
                'id_medecin' => $data['id_medecin'],
                'id_patient' => $data['id_patient'] ?? null,
                'date_heure_suggeree' => $data['date_heure_suggeree'],
                'duree' => $data['duree'],
                'type_creneau_id' => $data['type_creneau_id'] ?? null,
                'score_confiance' => $scoreConfiance,
                'raison' => $data['raison'] ?? null,
                'expire_le' => $expireLe
            ];
            
            $slotId = $this->suggestedSlotModel->create($slotData);
            
            // Récupérer le créneau créé avec toutes ses données
            $createdSlots = $this->suggestedSlotModel->getByDoctor($data['id_medecin']);
            $createdSlot = array_filter($createdSlots, function($slot) use ($slotId) {
                return $slot['id'] == $slotId;
            });
            $createdSlot = reset($createdSlot);
            
            http_response_code(201);
            echo json_encode([
                'status' => 'success',
                'message' => 'Créneau suggéré créé avec succès',
                'data' => $createdSlot
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->store: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création du créneau suggéré'
            ]);
        }
    }
    
    /**
     * Mettre à jour l'acceptation d'un créneau suggéré
     */
    public function updateAcceptance($slotId) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            
            $accepted = $data['accepted'] ?? null;
            $userId = $data['user_id'] ?? null;
            
            if ($accepted === null) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Statut d\'acceptation requis'
                ]);
                return;
            }
            
            $success = $this->suggestedSlotModel->updateAcceptance($slotId, $accepted, $userId);
            
            if ($success) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => $accepted ? 'Créneau accepté' : 'Créneau refusé'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Créneau suggéré non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->updateAcceptance: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour du créneau suggéré'
            ]);
        }
    }
    
    /**
     * Supprimer un créneau suggéré
     */
    public function delete($slotId) {
        try {
            $success = $this->suggestedSlotModel->delete($slotId);
            
            if ($success) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Créneau suggéré supprimé avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Créneau suggéré non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression du créneau suggéré'
            ]);
        }
    }
    
    /**
     * Récupérer les statistiques des créneaux suggérés
     */
    public function getStats() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $stats = $this->suggestedSlotModel->getStats($medecinId);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->getStats: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }
    
    /**
     * Récupérer les créneaux suggérés pour un patient
     */
    public function getByPatient($patientId) {
        try {
            $includeExpired = isset($_GET['include_expired']) && $_GET['include_expired'] === 'true';
            $slots = $this->suggestedSlotModel->getByPatient($patientId, $includeExpired);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $slots
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->getByPatient: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des créneaux suggérés'
            ]);
        }
    }
    
    /**
     * Nettoyer les créneaux expirés
     */
    public function cleanExpired() {
        try {
            $deletedCount = $this->suggestedSlotModel->cleanExpired();
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => "$deletedCount créneaux expirés supprimés",
                'deleted_count' => $deletedCount
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans SuggestedSlotController->cleanExpired: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors du nettoyage des créneaux expirés'
            ]);
        }
    }
}
