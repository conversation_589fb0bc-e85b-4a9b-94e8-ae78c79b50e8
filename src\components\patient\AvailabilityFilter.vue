<template>
  <div class="availability-filter">
    <div class="filter-container">
      <!-- En-tête du filtre -->
      <div class="filter-header">
        <div class="header-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 17V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V17H3ZM20 2H4C2.9 2 2 2.9 2 4V6C2 7.1 2.9 8 4 8H20C21.1 8 22 7.1 22 6V4C22 2.9 21.1 2 20 2ZM3 10V12C3 13.1 3.9 14 5 14H19C20.1 14 21 13.1 21 12V10H3Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="header-content">
          <h4 class="filter-title">Filtrer les disponibilités</h4>
          <p class="filter-subtitle">Affinez votre recherche selon vos préférences</p>
        </div>
      </div>

      <!-- Formulaire de filtrage -->
      <form class="filter-form">
        <div class="form-row">
          <!-- Filtre spécialité -->
          <div class="form-group">
            <label class="form-label">
              <svg class="label-icon" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L18 7L16.74 12.26L23 11L21.09 16.26L24 15L22.74 20.26L19 19L17.09 24.26L12 23L6.91 24.26L5 19L1.26 20.26L0 15L2.91 16.26L1 11L7.26 12.26L6 7L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              Spécialité
            </label>
            <div class="select-wrapper">
              <select v-model="filter.specialty" class="form-select">
                <option value="">Toutes les spécialités</option>
                <option v-for="spec in specialties" :key="spec" :value="spec">{{ spec }}</option>
              </select>
              <svg class="select-arrow" viewBox="0 0 24 24">
                <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
              </svg>
            </div>
          </div>

          <!-- Filtre date -->
          <div class="form-group">
            <label class="form-label">
              <svg class="label-icon" viewBox="0 0 24 24" fill="none">
                <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19Z" fill="currentColor"/>
              </svg>
              Date
            </label>
            <input 
              type="date" 
              v-model="filter.date" 
              class="form-input"
              :min="minDate"
            >
          </div>
        </div>

        <!-- Actions -->
        <div class="filter-actions">
          <button 
            type="button" 
            class="btn-filter" 
            @click="applyFilter"
            :disabled="!hasFilters"
          >
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none">
              <path d="M15.5 14H20.5L22 15.5V20.5L20.5 22H15.5L14 20.5V15.5L15.5 14ZM18 16V21H21V16H18ZM15.5 14H14L12 16V21L14 23H19L21 21V16L19 14H15.5ZM9 11H15V13H9V11ZM9 7H19V9H9V7Z" fill="currentColor"/>
            </svg>
            Appliquer le filtre
          </button>
          
          <button 
            type="button" 
            class="btn-clear" 
            @click="clearFilters"
            v-if="hasFilters"
          >
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none">
              <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
            </svg>
            Effacer
          </button>
        </div>

        <!-- Indicateurs de filtres actifs -->
        <div v-if="hasFilters" class="active-filters">
          <div class="filters-label">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M7 6H17L13.59 10.41L9.83 6.65L7 6ZM2 4.27L4.28 2L22 19.72L19.73 22L16.41 18.68L14 21V15L10 11.27L2 4.27Z" fill="currentColor"/>
            </svg>
            Filtres actifs :
          </div>
          <div class="filter-tags">
            <span v-if="filter.specialty" class="filter-tag specialty">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L18 7L16.74 12.26L23 11L21.09 16.26L24 15L22.74 20.26L19 19L17.09 24.26L12 23L6.91 24.26L5 19L1.26 20.26L0 15L2.91 16.26L1 11L7.26 12.26L6 7L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
              {{ filter.specialty }}
              <button @click="filter.specialty = ''" class="tag-remove">×</button>
            </span>
            <span v-if="filter.date" class="filter-tag date">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19Z" fill="currentColor"/>
              </svg>
              {{ formatDate(filter.date) }}
              <button @click="filter.date = ''" class="tag-remove">×</button>
            </span>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const specialties = ref(['Généraliste', 'Cardiologue', 'Dermatologue', 'Pédiatre', 'Neurologue'])

const filter = ref({
  specialty: '',
  date: ''
})

const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const hasFilters = computed(() => {
  return filter.value.specialty || filter.value.date
})

function applyFilter() {
  if (!hasFilters.value) {
    alert('Veuillez sélectionner au moins un critère de filtrage')
    return
  }
  
  // Émettre un événement avec les filtres
  const filterData = {
    specialty: filter.value.specialty,
    date: filter.value.date
  }
  
  console.log('Filtres appliqués:', filterData)
  
  // Animation de feedback
  const btn = document.querySelector('.btn-filter')
  btn.classList.add('applied')
  setTimeout(() => btn.classList.remove('applied'), 1000)
  
  // Ici, tu peux émettre un événement ou filtrer une liste selon les critères
  alert(`Filtre appliqué :\nSpécialité: ${filter.value.specialty || 'Toutes'}\nDate: ${filter.value.date || 'Toutes'}`)
}

function clearFilters() {
  filter.value.specialty = ''
  filter.value.date = ''
  
  // Animation de feedback
  const form = document.querySelector('.filter-form')
  form.classList.add('cleared')
  setTimeout(() => form.classList.remove('cleared'), 600)
}

function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}
</script>

<style scoped>
.availability-filter {
  margin-bottom: 30px;
}

.filter-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideIn 0.6s ease-out;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.header-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}

.header-icon svg {
  width: 24px;
  height: 24px;
}

.header-content {
  flex: 1;
}

.filter-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 5px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filter-subtitle {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

.filter-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.label-icon {
  width: 16px;
  height: 16px;
  color: #667eea;
}

.form-input {
  padding: 15px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
  color: #2d3748;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.select-wrapper {
  position: relative;
}

.form-select {
  width: 100%;
  padding: 15px 50px 15px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  color: #2d3748;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.select-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #a0aec0;
  pointer-events: none;
  transition: transform 0.3s ease;
}

.form-select:focus + .select-arrow {
  transform: translateY(-50%) rotate(180deg);
}

.filter-actions {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.btn-filter, .btn-clear {
  padding: 15px 25px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn-filter {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #764ba2, #667eea);
  transition: left 0.3s ease;
}

.btn-filter:hover::before {
  left: 0;
}

.btn-filter:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-filter:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-filter:disabled::before {
  display: none;
}

.btn-clear {
  background: linear-gradient(135deg, #feb2b2, #fc8181);
  color: #742a2a;
}

.btn-clear::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #fc8181, #e53e3e);
  transition: left 0.3s ease;
}

.btn-clear:hover::before {
  left: 0;
}

.btn-clear:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(229, 62, 62, 0.3);
}

.btn-icon {
  width: 16px;
  height: 16px;
  position: relative;
  z-index: 2;
}

.active-filters {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #0ea5e9;
  border-radius: 12px;
  padding: 20px;
  animation: slideDown 0.3s ease-out;
}

.filters-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #0c4a6e;
  margin-bottom: 15px;
  font-size: 14px;
}

.filters-label svg {
  width: 16px;
  height: 16px;
}

.filter-tags {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  position: relative;
  animation: popIn 0.3s ease-out;
}

.filter-tag.specialty {
  background: linear-gradient(135deg, #ddd6fe, #c4b5fd);
  color: #5b21b6;
}

.filter-tag.date {
  background: linear-gradient(135deg, #fed7d7, #fbb6ce);
  color: #be185d;
}

.filter-tag svg {
  width: 12px;
  height: 12px;
}

.tag-remove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 4px;
}

.tag-remove:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

@keyframes popIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.btn-filter.applied {
  animation: pulse 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.filter-form.cleared {
  animation: shake 0.6s ease-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive */
@media (max-width: 768px) {
  .filter-container {
    padding: 20px;
  }
  
  .filter-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-filter, .btn-clear {
    justify-content: center;
  }
  
  .filter-tags {
    justify-content: center;
  }
}
</style>