<?php
require_once __DIR__ . '/../config/database.php';

class DatabaseController {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    /**
     * Obtenir les statistiques générales de la base de données
     */
    public function getOverview() {
        try {
            $stats = [];

            // Récupérer toutes les tables existantes dans la base de données
            $dbName = $this->getDatabaseName();
            $stats['tables'] = [];

            if ($dbName) {
                try {
                    // Obtenir la liste de toutes les tables
                    $stmt = $this->db->prepare("SHOW TABLES");
                    $stmt->execute();
                    $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

                    // Labels pour les tables connues
                    $tableLabels = [
                        'utilisateur' => 'Utilisateurs',
                        'medecins' => 'Médecins',
                        'patients' => 'Patients',
                        'rendez_vous' => 'Rendez-vous',
                        'conflits' => 'Conflits',
                        'types_conflits' => 'Types de conflits',
                        'creneaux_suggeres' => 'Créneaux suggérés',
                        'types_creneaux' => 'Types de créneaux'
                    ];

                    // Compter les enregistrements pour chaque table
                    foreach ($allTables as $table) {
                        try {
                            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM `$table`");
                            $stmt->execute();
                            $result = $stmt->fetch();

                            $stats['tables'][$table] = [
                                'label' => $tableLabels[$table] ?? ucfirst(str_replace('_', ' ', $table)),
                                'count' => (int)($result['count'] ?? 0)
                            ];
                        } catch (Exception $e) {
                            $stats['tables'][$table] = [
                                'label' => $tableLabels[$table] ?? ucfirst(str_replace('_', ' ', $table)),
                                'count' => 0,
                                'error' => 'Erreur de lecture: ' . $e->getMessage()
                            ];
                        }
                    }
                } catch (Exception $e) {
                    error_log("Erreur lors de la récupération des tables: " . $e->getMessage());
                    // Fallback avec les tables principales
                    $mainTables = ['utilisateur', 'medecins', 'patients', 'rendez_vous'];
                    foreach ($mainTables as $table) {
                        try {
                            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM `$table`");
                            $stmt->execute();
                            $result = $stmt->fetch();
                            $stats['tables'][$table] = [
                                'label' => $tableLabels[$table] ?? ucfirst($table),
                                'count' => (int)($result['count'] ?? 0)
                            ];
                        } catch (Exception $e) {
                            $stats['tables'][$table] = [
                                'label' => $tableLabels[$table] ?? ucfirst($table),
                                'count' => 0,
                                'error' => 'Table non accessible'
                            ];
                        }
                    }
                }
            }

            // Taille de la base de données
            $dbName = $this->getDatabaseName();
            if ($dbName) {
                $stmt = $this->db->prepare("
                    SELECT 
                        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                        COUNT(*) as table_count
                    FROM information_schema.tables 
                    WHERE table_schema = ?
                ");
                $stmt->execute([$dbName]);
                $dbInfo = $stmt->fetch();
                
                $stats['database'] = [
                    'name' => $dbName,
                    'size_mb' => $dbInfo['size_mb'] ?? 0,
                    'table_count' => $dbInfo['table_count'] ?? 0
                ];
            }

            // Statistiques de performance (dernières 24h)
            $stats['performance'] = [
                'recent_appointments' => $this->getRecentCount('rendez_vous', 24),
                'recent_conflicts' => $this->getRecentCount('conflits', 24),
                'active_users' => $this->getActiveUsersCount()
            ];

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans DatabaseController->getOverview: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }

    /**
     * Obtenir la liste des tables avec leurs informations
     */
    public function getTables() {
        try {
            $dbName = $this->getDatabaseName();
            if (!$dbName) {
                throw new Exception("Nom de base de données non trouvé");
            }

            $stmt = $this->db->prepare("
                SELECT 
                    table_name,
                    table_rows,
                    ROUND((data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                    table_comment,
                    create_time,
                    update_time
                FROM information_schema.tables 
                WHERE table_schema = ?
                ORDER BY table_name
            ");
            $stmt->execute([$dbName]);
            $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $tables
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans DatabaseController->getTables: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des tables'
            ]);
        }
    }

    /**
     * Obtenir la structure d'une table
     */
    public function getTableStructure($tableName) {
        try {
            // Sécurité : vérifier que la table existe
            if (!$this->tableExists($tableName)) {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Table non trouvée'
                ]);
                return;
            }

            $stmt = $this->db->prepare("DESCRIBE `$tableName`");
            $stmt->execute();
            $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'table_name' => $tableName,
                    'columns' => $structure
                ]
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans DatabaseController->getTableStructure: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération de la structure'
            ]);
        }
    }

    /**
     * Obtenir les données d'une table avec pagination
     */
    public function getTableData($tableName) {
        try {
            if (!$this->tableExists($tableName)) {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Table non trouvée'
                ]);
                return;
            }

            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 50;
            $offset = ($page - 1) * $limit;

            // Compter le total
            $countStmt = $this->db->prepare("SELECT COUNT(*) as total FROM `$tableName`");
            $countStmt->execute();
            $total = $countStmt->fetch()['total'];

            // Récupérer les données
            $stmt = $this->db->prepare("SELECT * FROM `$tableName` LIMIT ? OFFSET ?");
            $stmt->execute([$limit, $offset]);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'table_name' => $tableName,
                    'rows' => $data,
                    'pagination' => [
                        'current_page' => (int)$page,
                        'per_page' => (int)$limit,
                        'total' => (int)$total,
                        'total_pages' => ceil($total / $limit)
                    ]
                ]
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans DatabaseController->getTableData: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des données'
            ]);
        }
    }

    /**
     * Optimiser la base de données
     */
    public function optimize() {
        try {
            $results = [];
            $dbName = $this->getDatabaseName();
            
            if ($dbName) {
                // Obtenir la liste des tables
                $stmt = $this->db->prepare("
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = ?
                ");
                $stmt->execute([$dbName]);
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

                foreach ($tables as $table) {
                    try {
                        $this->db->exec("OPTIMIZE TABLE `$table`");
                        $results[] = "Table $table optimisée";
                    } catch (Exception $e) {
                        $results[] = "Erreur lors de l'optimisation de $table: " . $e->getMessage();
                    }
                }
            }

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'message' => 'Optimisation terminée',
                    'results' => $results
                ]
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans DatabaseController->optimize: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'optimisation'
            ]);
        }
    }

    /**
     * Méthodes utilitaires privées
     */
    private function getDatabaseName() {
        try {
            $stmt = $this->db->query("SELECT DATABASE() as db_name");
            $result = $stmt->fetch();
            return $result['db_name'] ?? null;
        } catch (Exception $e) {
            return null;
        }
    }

    private function tableExists($tableName) {
        try {
            $stmt = $this->db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$tableName]);
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    private function getRecentCount($table, $hours) {
        try {
            // Adapter selon la table et les colonnes disponibles
            $dateColumn = '';

            switch ($table) {
                case 'rendez_vous':
                    // Vérifier quelle colonne de date existe
                    $columns = $this->getTableColumns($table);
                    if (in_array('date_creation', $columns)) {
                        $dateColumn = 'date_creation';
                    } elseif (in_array('created_at', $columns)) {
                        $dateColumn = 'created_at';
                    } elseif (in_array('date_rendez_vous', $columns)) {
                        $dateColumn = 'date_rendez_vous';
                    } else {
                        // Si aucune colonne de date, compter tous les enregistrements
                        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM `$table`");
                        $stmt->execute();
                        $result = $stmt->fetch();
                        return $result['count'] ?? 0;
                    }
                    break;

                case 'conflits':
                    $columns = $this->getTableColumns($table);
                    if (in_array('detecte_le', $columns)) {
                        $dateColumn = 'detecte_le';
                    } elseif (in_array('created_at', $columns)) {
                        $dateColumn = 'created_at';
                    } else {
                        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM `$table`");
                        $stmt->execute();
                        $result = $stmt->fetch();
                        return $result['count'] ?? 0;
                    }
                    break;

                default:
                    // Pour les autres tables, essayer les colonnes communes
                    $columns = $this->getTableColumns($table);
                    if (in_array('created_at', $columns)) {
                        $dateColumn = 'created_at';
                    } elseif (in_array('date_creation', $columns)) {
                        $dateColumn = 'date_creation';
                    } else {
                        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM `$table`");
                        $stmt->execute();
                        $result = $stmt->fetch();
                        return $result['count'] ?? 0;
                    }
            }

            if ($dateColumn) {
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) as count
                    FROM `$table`
                    WHERE `$dateColumn` >= DATE_SUB(NOW(), INTERVAL ? HOUR)
                ");
                $stmt->execute([$hours]);
                $result = $stmt->fetch();
                return $result['count'] ?? 0;
            }

            return 0;
        } catch (Exception $e) {
            error_log("Erreur dans getRecentCount pour $table: " . $e->getMessage());
            return 0;
        }
    }

    private function getActiveUsersCount() {
        try {
            // Vérifier si la table utilisateur existe et quelles colonnes elle a
            if (!$this->tableExists('utilisateur')) {
                return 0;
            }

            $columns = $this->getTableColumns('utilisateur');

            if (in_array('role', $columns)) {
                $stmt = $this->db->query("
                    SELECT COUNT(*) as count
                    FROM utilisateur
                    WHERE role IN ('doctor', 'patient', 'medecin')
                ");
            } else {
                // Si pas de colonne role, compter tous les utilisateurs
                $stmt = $this->db->query("SELECT COUNT(*) as count FROM utilisateur");
            }

            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Erreur dans getActiveUsersCount: " . $e->getMessage());
            return 0;
        }
    }

    private function getTableColumns($tableName) {
        try {
            $stmt = $this->db->prepare("DESCRIBE `$tableName`");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
            return $columns;
        } catch (Exception $e) {
            error_log("Erreur lors de la récupération des colonnes de $tableName: " . $e->getMessage());
            return [];
        }
    }
}
?>
