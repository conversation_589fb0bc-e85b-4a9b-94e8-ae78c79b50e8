<template>
  <div class="dashboard-stats">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <AdminStatCard
        title="Patients"
        :value="stats.totalPatients"
        icon="fa-user-injured"
       
      />
      <AdminStatCard
        title="Médecins"
        :value="stats.totalDoctors"
        icon="fa-user-md"
       
      />
      <AdminStatCard
        title="Rendez-vous Aujourd'hui"
        :value="stats.appointmentsToday"
        icon="fa-calendar-day"
        
      />
      <AdminStatCard
        title="Prochains Rendez-vous"
        :value="stats.upcomingAppointments"
        icon="fa-calendar-alt"
        color="indigo"
      />
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue'
import { useStatsStore } from '@/stores/statsStore'
import AdminStatCard from '@/components/admin/shared/AdminStatCard.vue'

const statsStore = useStatsStore()
const stats = computed(() => statsStore.dashboardStats)

onMounted(() => {
  statsStore.fetchDashboardStats()
})
</script>

<style scoped>
.dashboard-stats {
  padding: 1.5rem;
  background-color:rgb(112, 211, 129);
  color:#ffffff;
  font-size:2rem;
 
}

/* Animation pour le chargement */
.grid {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-stats {
    padding: 1rem;
  }
}

@media (max-width: 640px) {
  .dashboard-stats {
    padding: 0.5rem;
  }
}
</style>