
<!-- components/ui/DatePicker.vue -->
<template>
  <div class="date-picker">
    <label v-if="label" class="date-picker-label">{{ label }}</label>
    <div class="date-picker-input-container">
      <input
        type="datetime-local"
        :value="formattedValue"
        @input="handleInput"
        :min="minDate"
        :max="maxDate"
        :disabled="disabled"
        :required="required"
        class="date-picker-input"
        :class="{ 'error': hasError }"
      />
      <div class="date-picker-icon">
        📅
      </div>
    </div>
    <div v-if="error" class="date-picker-error">{{ error }}</div>
    <div v-if="helper" class="date-picker-helper">{{ helper }}</div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { dateHelpers } from '@/utils/dateHelpers.js'

export default {
  name: 'DatePicker',
  props: {
    modelValue: {
      type: [String, Date],
      default: null
    },
    label: {
      type: String,
      default: ''
    },
    minDate: {
      type: String,
      default: ''
    },
    maxDate: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    helper: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const formattedValue = computed(() => {
      if (!props.modelValue) return ''
      const date = new Date(props.modelValue)
      return date.toISOString().slice(0, 16) // Format YYYY-MM-DDTHH:mm
    })
    
    const hasError = computed(() => !!props.error)
    
    const handleInput = (event) => {
      const value = event.target.value
      emit('update:modelValue', value ? new Date(value).toISOString() : null)
    }
    
    return {
      formattedValue,
      hasError,
      handleInput
    }
  }
}
</script>

<style scoped>
.date-picker {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-picker-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.date-picker-input-container {
  position: relative;
}

.date-picker-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s;
  background-color: white;
}

.date-picker-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.date-picker-input.error {
  border-color: #ef4444;
}

.date-picker-input:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.date-picker-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #9ca3af;
}

.date-picker-error {
  color: #ef4444;
  font-size: 0.75rem;
}

.date-picker-helper {
  color: #6b7280;
  font-size: 0.75rem;
}
</style>
