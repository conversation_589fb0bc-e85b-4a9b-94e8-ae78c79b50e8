<?php

require_once __DIR__ . '/../services/AppointmentStatusUpdater.php';

class AppointmentStatusController {
    private $pdo;
    private $statusUpdater;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->statusUpdater = new AppointmentStatusUpdater($pdo);
    }

    /**
     * Met à jour automatiquement tous les rendez-vous expirés
     */
    public function updateExpiredAppointments() {
        try {
            $result = $this->statusUpdater->updateExpiredAppointments();
            
            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $result,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour automatique: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Met à jour les rendez-vous d'un médecin spécifique
     */
    public function updateDoctorAppointments($doctorId) {
        try {
            if (!$doctorId || !is_numeric($doctorId)) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'ID médecin invalide'
                ]);
                return;
            }

            $result = $this->statusUpdater->updateDoctorAppointments($doctorId);
            
            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $result,
                'doctor_id' => $doctorId,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour des RDV du médecin: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Met à jour un rendez-vous spécifique
     */
    public function updateSingleAppointment($appointmentId) {
        try {
            if (!$appointmentId || !is_numeric($appointmentId)) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'ID rendez-vous invalide'
                ]);
                return;
            }

            $updated = $this->statusUpdater->updateSingleAppointment($appointmentId);
            
            if ($updated) {
                http_response_code(200);
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Rendez-vous mis à jour avec succès',
                    'appointment_id' => $appointmentId,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                http_response_code(200);
                echo json_encode([
                    'status' => 'info',
                    'message' => 'Aucune mise à jour nécessaire pour ce rendez-vous',
                    'appointment_id' => $appointmentId
                ]);
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour du rendez-vous: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Obtient les statistiques des mises à jour
     */
    public function getUpdateStats() {
        try {
            $stats = $this->statusUpdater->getUpdateStats();
            
            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $stats,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Nettoie les anciens logs
     */
    public function cleanLogs() {
        try {
            $this->statusUpdater->cleanOldLogs();
            
            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => 'Logs nettoyés avec succès',
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors du nettoyage des logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Vérifie les rendez-vous qui devraient être terminés (sans les mettre à jour)
     */
    public function checkExpiredAppointments() {
        try {
            $sql = "
                SELECT 
                    r.id,
                    r.date_rendez_vous,
                    r.duree,
                    r.statut,
                    CONCAT(p.prenom, ' ', p.nom) as patient_nom,
                    CONCAT(m.prenom, ' ', m.nom) as medecin_nom,
                    TIMESTAMPADD(MINUTE, r.duree, r.date_rendez_vous) as end_time,
                    TIMESTAMPDIFF(MINUTE, TIMESTAMPADD(MINUTE, r.duree, r.date_rendez_vous), NOW()) as minutes_overdue
                FROM rendez_vous r
                LEFT JOIN patients p ON r.id_patient = p.id
                LEFT JOIN medecins m ON r.id_medecin = m.id
                WHERE r.statut IN ('planifie', 'confirme', 'planifier', 'confirmer')
                AND TIMESTAMPADD(MINUTE, r.duree, r.date_rendez_vous) < NOW()
                ORDER BY r.date_rendez_vous ASC
                LIMIT 50
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $expiredAppointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'count' => count($expiredAppointments),
                    'appointments' => $expiredAppointments
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la vérification: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Configuration automatique - active/désactive la mise à jour automatique
     */
    public function toggleAutoUpdate() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $enabled = isset($input['enabled']) ? (bool)$input['enabled'] : true;

            // Sauvegarder la configuration dans un fichier
            $configFile = __DIR__ . '/../config/auto_update.json';
            $configDir = dirname($configFile);
            
            if (!is_dir($configDir)) {
                mkdir($configDir, 0755, true);
            }

            $config = [
                'enabled' => $enabled,
                'interval_minutes' => $input['interval_minutes'] ?? 5,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => $enabled ? 'Mise à jour automatique activée' : 'Mise à jour automatique désactivée',
                'config' => $config
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la configuration: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Obtient la configuration actuelle
     */
    public function getConfig() {
        try {
            $configFile = __DIR__ . '/../config/auto_update.json';
            
            if (file_exists($configFile)) {
                $config = json_decode(file_get_contents($configFile), true);
            } else {
                $config = [
                    'enabled' => true,
                    'interval_minutes' => 5,
                    'updated_at' => null
                ];
            }

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $config,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération de la configuration: ' . $e->getMessage()
            ]);
        }
    }
}
