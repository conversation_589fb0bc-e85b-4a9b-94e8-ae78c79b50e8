<?php
require_once __DIR__ . '/../config/database.php';

try {
    $database = Database::getInstance();
    $db = $database->getConnection();
    
    // Lecture du fichier SQL
    $sql = file_get_contents(__DIR__ . '/cleanup.sql');
    
    // Exécution des requêtes SQL
    $result = $db->exec($sql);
    
    echo "Nettoyage réussi !\n";
    
} catch (PDOException $e) {
    die("Erreur lors du nettoyage : " . $e->getMessage() . "\n");
} 