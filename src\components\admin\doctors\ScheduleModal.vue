<template>
  <div class="schedule-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Planning de {{ doctor.nom }} {{ doctor.prenom }}</h3>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="schedule-container">
          <div class="schedule-header">
            <button @click="previousWeek" class="btn-icon">
              <i class="fas fa-chevron-left"></i>
            </button>
            <h4><PERSON><PERSON><PERSON> du {{ formatDate(weekStart) }}</h4>
            <button @click="nextWeek" class="btn-icon">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          <div class="schedule-grid">
            <div class="time-column">
              <div class="time-slot-header"></div>
              <div v-for="hour in hours" :key="hour" class="time-slot">
                {{ formatHour(hour) }}
              </div>
            </div>
            <div v-for="day in weekDays" :key="day.date" class="day-column">
              <div class="day-header">
                {{ day.name }}<br>
                {{ formatDayNumber(day.date) }}
              </div>
              <div
                v-for="hour in hours"
                :key="hour"
                class="time-slot"
                :class="{ 'has-appointment': hasAppointment(day.date, hour) }"
              >
                <div v-if="getAppointment(day.date, hour)" class="appointment-info">
                  {{ getAppointment(day.date, hour).patient_nom }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { format, addDays, startOfWeek, addWeeks, subWeeks } from 'date-fns'
import { fr } from 'date-fns/locale'
import api from '@/services/api'

const props = defineProps({
  doctor: {
    type: Object,
    required: true
  }
})

defineEmits(['close'])

const currentWeek = ref(new Date())
const appointments = ref([])

const weekStart = computed(() => startOfWeek(currentWeek.value, { weekStartsOn: 1 }))

const weekDays = computed(() => {
  return Array.from({ length: 5 }, (_, i) => {
    const date = addDays(weekStart.value, i)
    return {
      date,
      name: format(date, 'EEEE', { locale: fr })
    }
  })
})

const hours = computed(() => {
  return Array.from({ length: 16 }, (_, i) => i + 8) // 8h à 23h
})

const formatDate = (date) => {
  return format(date, 'd MMMM yyyy', { locale: fr })
}

const formatHour = (hour) => {
  return `${hour}:00`
}

const formatDayNumber = (date) => {
  return format(date, 'd', { locale: fr })
}

const previousWeek = () => {
  currentWeek.value = subWeeks(currentWeek.value, 1)
  fetchAppointments()
}

const nextWeek = () => {
  currentWeek.value = addWeeks(currentWeek.value, 1)
  fetchAppointments()
}

const hasAppointment = (date, hour) => {
  return getAppointment(date, hour) !== null
}

const getAppointment = (date, hour) => {
  return appointments.value.find(apt => {
    const aptDate = new Date(apt.date_rendez_vous)
    return format(aptDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd') &&
           aptDate.getHours() === hour
  })
}

const fetchAppointments = async () => {
  try {
    const response = await api.get(`/doctors/${props.doctor.id}/appointments`, {
      params: {
        start_date: format(weekStart.value, 'yyyy-MM-dd'),
        end_date: format(addDays(weekStart.value, 6), 'yyyy-MM-dd')
      }
    })
    appointments.value = response.data.data
  } catch (error) {
    console.error('Erreur lors de la récupération des rendez-vous:', error)
  }
}

onMounted(() => {
  fetchAppointments()
})
</script>

<style scoped>
.schedule-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-body {
  padding: 1rem;
}

.schedule-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.schedule-grid {
  display: grid;
  grid-template-columns: 80px repeat(5, 1fr);
  gap: 1px;
  background-color: #e2e8f0;
  border: 1px solid #e2e8f0;
}

.time-column, .day-column {
  background-color: white;
}

.time-slot-header, .day-header {
  padding: 0.5rem;
  text-align: center;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
}

.time-slot {
  height: 60px;
  padding: 0.25rem;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
}

.time-column .time-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #64748b;
}

.has-appointment {
  background-color: #e0f2fe;
}

.appointment-info {
  font-size: 0.875rem;
  padding: 0.25rem;
  background-color: #0ea5e9;
  color: white;
  border-radius: 4px;
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
}

.btn-icon {
  padding: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: #e5e7eb;
}

.close-btn {
  padding: 0.5rem;
  border: none;
  background: none;
  cursor: pointer;
  color: #64748b;
}

.close-btn:hover {
  color: #1e293b;
}
</style> 