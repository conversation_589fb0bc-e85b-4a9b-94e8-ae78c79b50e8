// services/conflictDetection.js
export class ConflictDetectionService {
  static detectLocalConflicts(appointments, newAppointment) {
    const conflicts = []
    const newStart = new Date(newAppointment.appointment_date)
    const newEnd = new Date(newStart.getTime() + (newAppointment.duration * 60000))

    appointments.forEach(existing => {
      if (existing.id === newAppointment.id) return
      if (existing.status === 'cancelled') return
      if (existing.doctor_id !== newAppointment.doctor_id) return

      const existingStart = new Date(existing.appointment_date)
      const existingEnd = new Date(existingStart.getTime() + (existing.duration * 60000))

      // Détection de chevauchement
      if (this.timesOverlap(newStart, newEnd, existingStart, existingEnd)) {
        conflicts.push({
          type: 'overlap',
          severity: 'high',
          description: `Chevauchement avec le RDV de ${existing.patient_name}`,
          conflicting_appointment: existing
        })
      }

      // Vérification du temps de pause minimum (15 minutes par défaut)
      const minBreak = 15 * 60000 // 15 minutes en ms
      if (Math.abs(newStart - existingEnd) < minBreak || 
          Math.abs(existingStart - newEnd) < minBreak) {
        conflicts.push({
          type: 'break_violation',
          severity: 'medium',
          description: 'Temps de pause insuffisant entre les rendez-vous',
          conflicting_appointment: existing
        })
      }
    })

    return conflicts
  }

  static timesOverlap(start1, end1, start2, end2) {
    return start1 < end2 && end1 > start2
  }

  static validateAppointmentTime(appointmentData, doctorWorkingHours) {
    const appointmentDate = new Date(appointmentData.appointment_date)
    const dayOfWeek = appointmentDate.getDay()
    const timeStr = appointmentDate.toTimeString().slice(0, 5)

    const daySchedule = doctorWorkingHours[dayOfWeek]
    if (!daySchedule || !daySchedule.available) {
      return {
        valid: false,
        error: 'Le médecin n\'est pas disponible ce jour'
      }
    }

    if (timeStr < daySchedule.start || timeStr > daySchedule.end) {
      return {
        valid: false,
        error: 'Horaire en dehors des heures de consultation'
      }
    }

    return { valid: true }
  }
}
