// utils/dateHelpers.js
/**
 * Formate une date en chaîne de caractères selon le format spécifié
 * @param {Date|string} date - La date à formater
 * @param {string} format - Le format souhaité (optionnel, par défaut 'DD/MM/YYYY')
 * @returns {string} La date formatée
 */
export const formatDate = (date, format = 'DD/MM/YYYY') => {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const day = String(d.getDate()).padStart(2, '0')
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const year = d.getFullYear()
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')

  switch (format) {
    case 'DD/MM/YYYY':
      return `${day}/${month}/${year}`
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'DD/MM/YYYY HH:mm':
      return `${day}/${month}/${year} ${hours}:${minutes}`
    case 'HH:mm':
      return `${hours}:${minutes}`
    default:
      return `${day}/${month}/${year}`
  }
}

/**
 * Vérifie si une date est aujourd'hui
 * @param {Date|string} date - La date à vérifier
 * @returns {boolean}
 */
export const isToday = (date) => {
  const today = new Date()
  const d = new Date(date)
  return d.getDate() === today.getDate() &&
    d.getMonth() === today.getMonth() &&
    d.getFullYear() === today.getFullYear()
}

/**
 * Vérifie si une date est dans le passé
 * @param {Date|string} date - La date à vérifier
 * @returns {boolean}
 */
export const isPast = (date) => {
  return new Date(date) < new Date()
}

/**
 * Ajoute des jours à une date
 * @param {Date|string} date - La date de départ
 * @param {number} days - Le nombre de jours à ajouter
 * @returns {Date}
 */
export const addDays = (date, days) => {
  const d = new Date(date)
  d.setDate(d.getDate() + days)
  return d
}

/**
 * Retourne le début de la semaine pour une date donnée
 * @param {Date|string} date - La date
 * @returns {Date}
 */
export const getStartOfWeek = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1)
  return new Date(d.setDate(diff))
}

/**
 * Retourne la fin de la semaine pour une date donnée
 * @param {Date|string} date - La date
 * @returns {Date}
 */
export const getEndOfWeek = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() + (day === 0 ? 0 : 7 - day)
  return new Date(d.setDate(diff))
}

/**
 * Retourne un tableau des jours de la semaine pour une date donnée
 * @param {Date|string} date - La date
 * @returns {Date[]}
 */
export const getWeekDays = (date) => {
  const start = getStartOfWeek(date)
  const days = []
  for (let i = 0; i < 7; i++) {
    days.push(addDays(start, i))
  }
  return days
}

export const dateHelpers = {
  addMinutes(date, minutes) {
    const result = new Date(date)
    result.setMinutes(result.getMinutes() + minutes)
    return result
  },

  getWeekDates(date = new Date()) {
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Lundi = début de semaine
    startOfWeek.setDate(diff)
    startOfWeek.setHours(0, 0, 0, 0)

    const weekDates = []
    for (let i = 0; i < 7; i++) {
      weekDates.push(new Date(startOfWeek.getTime() + (i * 24 * 60 * 60 * 1000)))
    }
    return weekDates
  },

  getTimeSlots(startTime, endTime, duration = 30) {
    const slots = []
    const start = new Date(`2000-01-01T${startTime}:00`)
    const end = new Date(`2000-01-01T${endTime}:00`)
    
    let current = new Date(start)
    while (current < end) {
      slots.push(current.toTimeString().slice(0, 5))
      current.setMinutes(current.getMinutes() + duration)
    }
    
    return slots
  }
}