<?php
/**
 * Script pour supprimer les données de test
 * Exécutez ce script depuis la ligne de commande ou via le navigateur
 */

require_once __DIR__ . '/../config/database.php';

echo "🧹 SUPPRESSION DES DONNÉES DE TEST\n";
echo "===================================\n\n";

try {
    // Commencer une transaction
    $pdo->beginTransaction();
    
    echo "📊 État avant suppression:\n";
    echo "-------------------------\n";
    
    // Compter les données avant suppression
    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'medecin' AND email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    $doctorsCount = $stmt->fetchColumn();
    echo "👨‍⚕️ Médecins de test: $doctorsCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'patient' AND email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')");
    $patientsCount = $stmt->fetchColumn();
    echo "👤 Patients de test: $patientsCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM rendez_vous WHERE notes = 'Rendez-vous de test'");
    $appointmentsCount = $stmt->fetchColumn();
    echo "📅 Rendez-vous de test: $appointmentsCount\n";
    
    // Vérifier si la table creneaux_suggeres existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'creneaux_suggeres'");
    $slotsTableExists = $stmt->fetch();
    $slotsCount = 0;
    if ($slotsTableExists) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM creneaux_suggeres WHERE raison IN ('Créneau disponible', 'Consultation de suivi', 'Nouveau patient')");
        $slotsCount = $stmt->fetchColumn();
    }
    echo "🕒 Créneaux suggérés de test: $slotsCount\n\n";
    
    echo "🗑️ Début de la suppression...\n";
    echo "-----------------------------\n";
    
    $totalDeleted = 0;
    
    // 1. Supprimer les rendez-vous de test
    $stmt = $pdo->prepare("DELETE FROM rendez_vous WHERE notes = 'Rendez-vous de test'");
    $stmt->execute();
    $deletedAppointments = $stmt->rowCount();
    $totalDeleted += $deletedAppointments;
    echo "📅 Rendez-vous de test supprimés: $deletedAppointments\n";
    
    // 2. Supprimer les créneaux suggérés de test
    $deletedSlots = 0;
    if ($slotsTableExists) {
        $stmt = $pdo->prepare("DELETE FROM creneaux_suggeres WHERE raison IN ('Créneau disponible', 'Consultation de suivi', 'Nouveau patient')");
        $stmt->execute();
        $deletedSlots = $stmt->rowCount();
        $totalDeleted += $deletedSlots;
    }
    echo "🕒 Créneaux suggérés de test supprimés: $deletedSlots\n";
    
    // 3. Supprimer les conflits de test (si la table existe)
    $stmt = $pdo->query("SHOW TABLES LIKE 'conflits'");
    $conflictsTableExists = $stmt->fetch();
    $deletedConflicts = 0;
    if ($conflictsTableExists) {
        $stmt = $pdo->prepare("DELETE FROM conflits WHERE description LIKE '%test%' OR notes LIKE '%test%'");
        $stmt->execute();
        $deletedConflicts = $stmt->rowCount();
        $totalDeleted += $deletedConflicts;
    }
    echo "⚠️ Conflits de test supprimés: $deletedConflicts\n";
    
    // 4. Supprimer les utilisateurs de test
    $testEmails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];
    
    $placeholders = str_repeat('?,', count($testEmails) - 1) . '?';
    $stmt = $pdo->prepare("DELETE FROM utilisateur WHERE email IN ($placeholders)");
    $stmt->execute($testEmails);
    $deletedUsers = $stmt->rowCount();
    $totalDeleted += $deletedUsers;
    echo "👥 Utilisateurs de test supprimés: $deletedUsers\n\n";
    
    // Valider la transaction
    $pdo->commit();
    
    echo "✅ Suppression terminée avec succès!\n";
    echo "Total d'éléments supprimés: $totalDeleted\n\n";
    
    // État après suppression
    echo "📊 État après suppression:\n";
    echo "-------------------------\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'medecin'");
    echo "👨‍⚕️ Médecins restants: " . $stmt->fetchColumn() . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'patient'");
    echo "👤 Patients restants: " . $stmt->fetchColumn() . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM rendez_vous");
    echo "📅 Rendez-vous restants: " . $stmt->fetchColumn() . "\n";
    
    if ($slotsTableExists) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM creneaux_suggeres");
        echo "🕒 Créneaux suggérés restants: " . $stmt->fetchColumn() . "\n";
    }
    
    echo "\n🎉 Toutes les données de test ont été supprimées!\n";
    echo "Votre base de données est maintenant nettoyée.\n\n";
    
    // Afficher les utilisateurs restants
    echo "👥 Utilisateurs restants dans le système:\n";
    echo "========================================\n";
    $stmt = $pdo->query("
        SELECT 
            CONCAT(prenom, ' ', nom) as nom_complet,
            email,
            role,
            COALESCE(specialite, 'N/A') as specialite
        FROM utilisateur 
        ORDER BY role, nom
    ");
    $remainingUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($remainingUsers)) {
        echo "Aucun utilisateur restant dans le système.\n";
    } else {
        foreach ($remainingUsers as $user) {
            echo "- {$user['nom_complet']} ({$user['email']}) - {$user['role']}";
            if ($user['role'] === 'medecin' && $user['specialite'] !== 'N/A') {
                echo " - {$user['specialite']}";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    // Annuler la transaction en cas d'erreur
    $pdo->rollBack();
    
    echo "❌ Erreur lors de la suppression: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    echo "\nLa transaction a été annulée. Aucune donnée n'a été supprimée.\n";
}
?>
