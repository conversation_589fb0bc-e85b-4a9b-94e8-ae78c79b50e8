<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{ doctor ? 'Modifier le médecin' : 'Nouveau médecin' }}</h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="doctor-form">
        <div class="form-group">
          <label for="fullName">Nom complet</label>
          <input 
            id="fullName"
            v-model="form.fullName"
            type="text"
            required
          />
        </div>

        <div class="form-group">
          <label for="specialty">Spécialité</label>
          <select 
            id="specialty"
            v-model="form.specialty"
            required
          >
            <option value="">Sélectionner une spécialité</option>
            <option v-for="specialty in specialties" :key="specialty" :value="specialty">
              {{ specialty }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input 
            id="email"
            v-model="form.email"
            type="email"
            required
          />
        </div>

        <div class="form-group">
          <label for="phone">Téléphone</label>
          <input 
            id="phone"
            v-model="form.phone"
            type="tel"
            required
          />
        </div>

        <div class="form-actions">
          <button type="button" class="btn-secondary" @click="$emit('close')">
            Annuler
          </button>
          <button type="submit" class="btn-primary">
            {{ doctor ? 'Mettre à jour' : 'Créer' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  doctor: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['save', 'close'])

const specialties = [
  'Médecin généraliste',
  'Cardiologue',
  'Dermatologue',
  'Pédiatre',
  'Ophtalmologue'
]

const form = ref({
  fullName: '',
  specialty: '',
  email: '',
  phone: ''
})

onMounted(() => {
  if (props.doctor) {
    form.value = { ...props.doctor }
  }
})

const handleSubmit = () => {
  emit('save', form.value)
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--admin-dark);
}

.close-btn {
  background: none;
  border: none;
  color: var(--admin-gray);
  cursor: pointer;
  font-size: 1.25rem;
}

.doctor-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--admin-dark);
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid var(--admin-border);
  border-radius: 6px;
  font-size: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

.btn-primary {
  background: var(--admin-blue);
  color: white;
  border: none;
}

.btn-secondary {
  background: white;
  color: var(--admin-dark);
  border: 1px solid var(--admin-border);
}
</style> 