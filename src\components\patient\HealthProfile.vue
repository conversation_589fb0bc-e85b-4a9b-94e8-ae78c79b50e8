<template>
  <div class="health-profile">
    <h2 class="section-title">
      <i class="fas fa-heartbeat"></i>
      <PERSON><PERSON>
    </h2>
    
    <div v-if="loading" class="loading">
      <i class="fas fa-spinner fa-spin"></i>
      Chargement du profil...
    </div>
    
    <div v-else class="profile-content">
      <div class="profile-section">
        <h3>Informations Générales</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">Groupe Sanguin</span>
            <span class="value">{{ profile.bloodType || 'Non renseigné' }}</span>
          </div>
          <div class="info-item">
            <span class="label">Taille</span>
            <span class="value">{{ profile.height || 'Non renseigné' }} cm</span>
          </div>
          <div class="info-item">
            <span class="label">Poids</span>
            <span class="value">{{ profile.weight || 'Non renseigné' }} kg</span>
          </div>
          <div class="info-item">
            <span class="label">IMC</span>
            <span class="value">{{ calculateBMI() }}</span>
          </div>
        </div>
      </div>

      <div class="profile-section">
        <h3>Allergies</h3>
        <div class="tags-container">
          <div v-if="profile.allergies?.length" class="tags">
            <span v-for="allergy in profile.allergies" 
                  :key="allergy" 
                  class="tag">
              {{ allergy }}
            </span>
          </div>
          <p v-else class="empty-text">Aucune allergie renseignée</p>
        </div>
      </div>

      <div class="profile-section">
        <h3>Antécédents Médicaux</h3>
        <div class="medical-history">
          <div v-if="profile.medicalHistory?.length" class="history-list">
            <div v-for="item in profile.medicalHistory" 
                 :key="item.id" 
                 class="history-item">
              <span class="date">{{ item.date }}</span>
              <span class="description">{{ item.description }}</span>
            </div>
          </div>
          <p v-else class="empty-text">Aucun antécédent renseigné</p>
        </div>
      </div>

      <div class="profile-section">
        <h3>Traitements en Cours</h3>
        <div class="treatments">
          <div v-if="profile.currentTreatments?.length" class="treatments-list">
            <div v-for="treatment in profile.currentTreatments" 
                 :key="treatment.id" 
                 class="treatment-item">
              <div class="treatment-header">
                <span class="name">{{ treatment.name }}</span>
                <span class="dosage">{{ treatment.dosage }}</span>
              </div>
              <p class="frequency">{{ treatment.frequency }}</p>
            </div>
          </div>
          <p v-else class="empty-text">Aucun traitement en cours</p>
        </div>
      </div>

      <button class="edit-btn" @click="editProfile">
        <i class="fas fa-edit"></i>
        Modifier le profil
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loading = ref(true)
const profile = ref({
  bloodType: 'A+',
  height: 175,
  weight: 70,
  allergies: ['Pénicilline', 'Pollen'],
  medicalHistory: [
    { id: 1, date: '2024', description: 'Opération de l\'appendicite' }
  ],
  currentTreatments: [
    { 
      id: 1, 
      name: 'Paracétamol', 
      dosage: '1000mg',
      frequency: '3 fois par jour'
    }
  ]
})

const calculateBMI = () => {
  if (!profile.value.height || !profile.value.weight) return 'Non calculable'
  const height = profile.value.height / 100 // conversion en mètres
  const bmi = (profile.value.weight / (height * height)).toFixed(1)
  return `${bmi} kg/m²`
}

const editProfile = () => {
  // À implémenter : ouverture du formulaire d'édition
  console.log('Édition du profil')
}

onMounted(async () => {
  try {
    // Simulation d'appel API
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Les données sont déjà dans profile.value pour la démo
  } catch (error) {
    console.error('Erreur lors du chargement du profil:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.health-profile {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e293b;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.section-title i {
  color: #3b82f6;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.loading i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.profile-section {
  margin-bottom: 2rem;
}

.profile-section h3 {
  color: #1e293b;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.label {
  display: block;
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.value {
  color: #1e293b;
  font-weight: 500;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
}

.history-list, .treatments-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item, .treatment-item {
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.date {
  color: #64748b;
  font-size: 0.875rem;
}

.description {
  display: block;
  margin-top: 0.25rem;
  color: #1e293b;
}

.treatment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.name {
  font-weight: 500;
  color: #1e293b;
}

.dosage {
  color: #64748b;
  font-size: 0.875rem;
}

.frequency {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

.empty-text {
  color: #64748b;
  font-style: italic;
  margin: 0;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.edit-btn:hover {
  background: #2563eb;
}

@media (max-width: 768px) {
  .health-profile {
    padding: 1rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .treatment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style> 