<template>
  <div class="data-diagnostic">
    <div class="diagnostic-header">
      <h2>🔍 Diagnostic des Données</h2>
      <p>Vérifiez si vos rendez-vous et créneaux suggérés sont correctement chargés</p>
    </div>

    <div class="diagnostic-actions">
      <button @click="checkData" class="btn-check" :disabled="loading">
        <i class="fas fa-search" :class="{ 'fa-spin': loading }"></i>
        {{ loading ? 'Vérification...' : 'Vérifier les données' }}
      </button>
      
      <button @click="addTestData" class="btn-add" :disabled="loading">
        <i class="fas fa-plus"></i>
        Ajouter des données de test
      </button>
      
      <button @click="refreshStores" class="btn-refresh" :disabled="loading">
        <i class="fas fa-sync-alt"></i>
        Actualiser les stores
      </button>

      <button @click="removeTestData" class="btn-remove" :disabled="loading">
        <i class="fas fa-trash"></i>
        Supprimer données de test
      </button>

      <button @click="showDangerousActions = !showDangerousActions" class="btn-danger-toggle" :disabled="loading">
        <i class="fas fa-exclamation-triangle"></i>
        Actions dangereuses
      </button>
    </div>

    <!-- Actions dangereuses (cachées par défaut) -->
    <div v-if="showDangerousActions" class="dangerous-actions">
      <h3>⚠️ Actions Dangereuses</h3>
      <p class="warning-text">Ces actions sont irréversibles. Utilisez avec précaution !</p>

      <div class="dangerous-buttons">
        <button @click="removeAllData" class="btn-danger" :disabled="loading">
          <i class="fas fa-bomb"></i>
          Supprimer TOUTES les données
        </button>

        <button @click="resetTables" class="btn-danger" :disabled="loading">
          <i class="fas fa-database"></i>
          Réinitialiser les tables
        </button>
      </div>
    </div>

    <div v-if="diagnosticResult" class="diagnostic-result">
      <h3>📊 Résultats du diagnostic</h3>
      <pre>{{ diagnosticResult }}</pre>
    </div>

    <div v-if="error" class="error-message">
      <h3>❌ Erreur</h3>
      <p>{{ error }}</p>
    </div>

    <!-- Affichage des données des stores -->
    <div class="stores-data">
      <div class="store-section">
        <h3>📅 Store des Rendez-vous</h3>
        <div class="store-info">
          <p><strong>Nombre de rendez-vous:</strong> {{ appointmentStore.appointments.length }}</p>
          <p><strong>Loading:</strong> {{ appointmentStore.loading }}</p>
          <p><strong>Error:</strong> {{ appointmentStore.error || 'Aucune' }}</p>
          
          <div v-if="appointmentStore.appointments.length > 0" class="data-preview">
            <h4>Aperçu des rendez-vous:</h4>
            <ul>
              <li v-for="apt in appointmentStore.appointments.slice(0, 3)" :key="apt.id">
                {{ apt.date_rendez_vous || apt.date }} - {{ apt.patient_nom || 'Patient inconnu' }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="store-section">
        <h3>🕒 Store des Créneaux Suggérés</h3>
        <div class="store-info">
          <p><strong>Nombre de créneaux:</strong> {{ suggestedSlotStore.slots.length }}</p>
          <p><strong>Loading:</strong> {{ suggestedSlotStore.loading }}</p>
          <p><strong>Error:</strong> {{ suggestedSlotStore.error || 'Aucune' }}</p>
          
          <div v-if="suggestedSlotStore.slots.length > 0" class="data-preview">
            <h4>Aperçu des créneaux:</h4>
            <ul>
              <li v-for="slot in suggestedSlotStore.slots.slice(0, 3)" :key="slot.id">
                {{ slot.date_heure_suggeree }} - {{ slot.patient_nom || 'Patient inconnu' }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Tests API directs -->
    <div class="api-tests">
      <h3>🔧 Tests API Directs</h3>
      <div class="test-buttons">
        <button @click="testAppointmentsAPI" class="btn-test">
          Test API Rendez-vous
        </button>
        <button @click="testSuggestedSlotsAPI" class="btn-test">
          Test API Créneaux Suggérés
        </button>
      </div>
      
      <div v-if="apiTestResults" class="api-results">
        <h4>Résultats des tests API:</h4>
        <pre>{{ apiTestResults }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import { useAuthStore } from '@/stores/authStore'
import api from '@/services/api'

const appointmentStore = useAppointmentStore()
const suggestedSlotStore = useSuggestedSlotStore()
const authStore = useAuthStore()

const loading = ref(false)
const diagnosticResult = ref('')
const error = ref('')
const apiTestResults = ref('')
const showDangerousActions = ref(false)

const checkData = async () => {
  try {
    loading.value = true
    error.value = ''

    const response = await api.get('/test-data/check')
    diagnosticResult.value = JSON.stringify(response.data, null, 2)
  } catch (err) {
    error.value = `Erreur lors de la vérification: ${err.message}`
    console.error('Erreur diagnostic:', err)
  } finally {
    loading.value = false
  }
}

const addTestData = async () => {
  try {
    loading.value = true
    error.value = ''

    const response = await api.post('/test-data/add')
    diagnosticResult.value = JSON.stringify(response.data, null, 2)

    // Actualiser les stores après ajout des données
    await refreshStores()
  } catch (err) {
    error.value = `Erreur lors de l'ajout des données: ${err.message}`
    console.error('Erreur ajout données:', err)
  } finally {
    loading.value = false
  }
}

const refreshStores = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const doctorId = authStore.user?.id || 1
    
    // Recharger les rendez-vous
    await appointmentStore.fetchAppointmentsByDoctor(doctorId)
    
    // Recharger les créneaux suggérés
    await suggestedSlotStore.loadSlotsByDoctor(doctorId)
    
    console.log('Stores actualisés')
  } catch (err) {
    error.value = `Erreur lors de l'actualisation: ${err.message}`
    console.error('Erreur actualisation:', err)
  } finally {
    loading.value = false
  }
}

const testAppointmentsAPI = async () => {
  try {
    const doctorId = authStore.user?.id || 1
    const response = await api.get(`/appointments/doctor/${doctorId}`)
    apiTestResults.value = `API Rendez-vous:\n${JSON.stringify(response.data, null, 2)}`
  } catch (err) {
    apiTestResults.value = `Erreur API Rendez-vous: ${err.message}`
  }
}

const testSuggestedSlotsAPI = async () => {
  try {
    const doctorId = authStore.user?.id || 1
    const response = await api.get(`/suggested-slots/doctor/${doctorId}`)
    apiTestResults.value = `API Créneaux Suggérés:\n${JSON.stringify(response.data, null, 2)}`
  } catch (err) {
    apiTestResults.value = `Erreur API Créneaux: ${err.message}`
  }
}

// Nouvelles méthodes de suppression
const removeTestData = async () => {
  if (!confirm('⚠️ Êtes-vous sûr de vouloir supprimer toutes les données de test ?\n\nCette action supprimera :\n- Les médecins et patients de test\n- Les rendez-vous de test\n- Les créneaux suggérés de test\n\nCette action est irréversible !')) {
    return
  }

  try {
    loading.value = true
    error.value = ''

    const response = await api.delete('/cleanup/test-data')
    diagnosticResult.value = JSON.stringify(response.data, null, 2)

    // Actualiser les stores après suppression
    await refreshStores()

    console.log('Données de test supprimées avec succès')
  } catch (err) {
    error.value = `Erreur lors de la suppression des données de test: ${err.message}`
    console.error('Erreur suppression données de test:', err)
  } finally {
    loading.value = false
  }
}

const removeAllData = async () => {
  if (!confirm('🚨 ATTENTION ! CETTE ACTION EST TRÈS DANGEREUSE !\n\nVoulez-vous vraiment supprimer TOUTES les données ?\n\nCela supprimera :\n- TOUS les médecins et patients\n- TOUS les rendez-vous\n- TOUS les créneaux suggérés\n- TOUS les conflits\n\nSeuls les comptes administrateurs seront préservés.\n\nCETTE ACTION EST IRRÉVERSIBLE !\n\nTapez "SUPPRIMER" pour confirmer')) {
    return
  }

  const confirmation = prompt('Pour confirmer, tapez exactement "SUPPRIMER" (en majuscules) :')
  if (confirmation !== 'SUPPRIMER') {
    alert('Suppression annulée - confirmation incorrecte')
    return
  }

  try {
    loading.value = true
    error.value = ''

    const response = await api.delete('/cleanup/all-data')
    diagnosticResult.value = JSON.stringify(response.data, null, 2)

    // Actualiser les stores après suppression
    await refreshStores()

    console.log('Toutes les données supprimées avec succès')
    alert('✅ Toutes les données ont été supprimées avec succès !')
  } catch (err) {
    error.value = `Erreur lors de la suppression de toutes les données: ${err.message}`
    console.error('Erreur suppression toutes données:', err)
  } finally {
    loading.value = false
  }
}

const resetTables = async () => {
  if (!confirm('🔄 Voulez-vous réinitialiser les tables ?\n\nCela videra complètement :\n- Table rendez_vous\n- Table creneaux_suggeres\n- Table conflits\n\nLes utilisateurs seront préservés.\n\nCette action est irréversible !')) {
    return
  }

  try {
    loading.value = true
    error.value = ''

    const response = await api.delete('/cleanup/reset-tables')
    diagnosticResult.value = JSON.stringify(response.data, null, 2)

    // Actualiser les stores après réinitialisation
    await refreshStores()

    console.log('Tables réinitialisées avec succès')
  } catch (err) {
    error.value = `Erreur lors de la réinitialisation des tables: ${err.message}`
    console.error('Erreur réinitialisation tables:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  console.log('Composant de diagnostic monté')
})
</script>

<style scoped>
.data-diagnostic {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.diagnostic-header {
  text-align: center;
  margin-bottom: 2rem;
}

.diagnostic-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.diagnostic-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.btn-check, .btn-add, .btn-refresh, .btn-test, .btn-remove, .btn-danger-toggle, .btn-danger {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-check {
  background: #3498db;
  color: white;
}

.btn-check:hover {
  background: #2980b9;
}

.btn-add {
  background: #27ae60;
  color: white;
}

.btn-add:hover {
  background: #229954;
}

.btn-refresh {
  background: #f39c12;
  color: white;
}

.btn-refresh:hover {
  background: #e67e22;
}

.btn-test {
  background: #9b59b6;
  color: white;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.btn-test:hover {
  background: #8e44ad;
}

.btn-remove {
  background: #e74c3c;
  color: white;
}

.btn-remove:hover {
  background: #c0392b;
}

.btn-danger-toggle {
  background: #f39c12;
  color: white;
}

.btn-danger-toggle:hover {
  background: #e67e22;
}

.btn-danger {
  background: #dc3545;
  color: white;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.dangerous-actions {
  background: #f8d7da;
  border: 2px solid #f5c6cb;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem 0;
  color: #721c24;
}

.dangerous-actions h3 {
  margin-top: 0;
  color: #721c24;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dangerous-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.warning-text {
  font-style: italic;
  margin: 0.5rem 0;
  font-weight: 500;
}

.diagnostic-result, .error-message, .api-results {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.error-message {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.diagnostic-result pre, .api-results pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.stores-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.store-section {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
}

.store-section h3 {
  margin-top: 0;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.store-info p {
  margin: 0.5rem 0;
}

.data-preview {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.data-preview h4 {
  margin: 0 0 0.5rem 0;
  color: #495057;
}

.data-preview ul {
  margin: 0;
  padding-left: 1.5rem;
}

.data-preview li {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.api-tests {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
}

.api-tests h3 {
  margin-top: 0;
  color: #2c3e50;
}

.test-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .stores-data {
    grid-template-columns: 1fr;
  }
  
  .diagnostic-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .test-buttons {
    flex-direction: column;
  }
}
</style>
