<?php
class AuthMiddleware {
    private static $secretKey = "votre_clé_secrète_ici"; // À changer en production

    public static function handle($request) {
        // 1. Vérification du token JWT
        $token = self::getBearerToken();
        
        if (!$token) {
            error_log("Token manquant dans la requête");
            return self::unauthorizedResponse('Token manquant');
        }

        // 2. Validation du token
        try {
            $payload = self::validateJWT($token);
            if (!$payload) {
                error_log("Token invalide");
                return self::unauthorizedResponse('Token invalide');
            }
            
            error_log("Token validé avec succès pour l'utilisateur: " . ($payload->user->email ?? 'inconnu'));
            
            // Ajouter les informations de l'utilisateur à la requête
            $request->user = $payload->user;
            return true;

        } catch (Exception $e) {
            error_log("Erreur de validation du token: " . $e->getMessage());
            return self::unauthorizedResponse($e->getMessage());
        }
    }

    private static function getBearerToken() {
        $headers = getallheaders();
        error_log("En-têtes reçus: " . print_r($headers, true));
        
        // Vérifier les deux variantes possibles de l'en-tête Authorization
        $authHeader = null;
        foreach ($headers as $name => $value) {
            if (strtolower($name) === 'authorization') {
                $authHeader = $value;
                break;
            }
        }
        
        if (!$authHeader) {
            error_log("Aucun en-tête d'autorisation trouvé");
            return null;
        }
        
        if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
            error_log("Token Bearer extrait avec succès");
            return $matches[1];
        }

        error_log("Format de token invalide dans l'en-tête: " . $authHeader);
        return null;
    }

    private static function validateJWT($token) {
        try {
            error_log("Début de la validation du token JWT");
            list($headerB64, $payloadB64, $signatureB64) = explode('.', $token);
            
            // Décoder le payload
            $payload = json_decode(base64_decode($payloadB64));
            
            if (!$payload) {
                throw new Exception('Token malformé');
            }
            
            // Vérifier l'expiration
            if (isset($payload->exp) && $payload->exp < time()) {
                throw new Exception('Token expiré');
            }
            
            // Vérifier la signature
            $signature = base64_decode($signatureB64);
            $expectedSignature = hash_hmac('sha256', "$headerB64.$payloadB64", self::$secretKey, true);
            
            if (!hash_equals($signature, $expectedSignature)) {
                throw new Exception('Signature invalide');
            }
            
            error_log("Token JWT validé avec succès");
            return $payload;
            
        } catch (Exception $e) {
            error_log("Erreur lors de la validation du token JWT: " . $e->getMessage());
            throw new Exception('Token invalide: ' . $e->getMessage());
        }
    }

    private static function unauthorizedResponse($message) {
        http_response_code(401);
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => $message
        ]);
        return false;
    }
}