<?php

class PatientHistoryController {
    private $db;

    public function __construct($db = null) {
        $this->db = $db ?? Database::getInstance()->getConnection();
    }

    public function getHistory($patientId) {
        try {
            // Récupérer les rendez-vous
            $appointmentsQuery = "
                SELECT 
                    'appointment' as type,
                    rdv.date_rendez_vous as date,
                    CONCAT('Rendez-vous avec Dr. ', m.nom, ' ', m.prenom) as title,
                    rdv.notes as description,
                    JSON_OBJECT(
                        'doctor', CONCAT(m.nom, ' ', m.prenom),
                        'specialite', m.specialite,
                        'status', rdv.statut,
                        'type', rdv.type,
                        'priorite', rdv.priorite,
                        'salle', rdv.id_salle,
                        'duree', rdv.duree
                    ) as details
                FROM rendez_vous rdv
                INNER JOIN medecins m ON rdv.id_medecin = m.id
                WHERE rdv.id_patient = ?
                ORDER BY rdv.date_rendez_vous DESC
            ";

            $stmt = $this->db->prepare($appointmentsQuery);
            $stmt->execute([$patientId]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Récupérer les allergies
            $allergiesQuery = "
                SELECT 
                    'allergy' as type,
                    NOW() as date,
                    'Allergie' as title,
                    a.allergie as description,
                    JSON_OBJECT(
                        'id', a.id,
                        'allergie', a.allergie
                    ) as details
                FROM allergies a
                WHERE a.patient_id = ?
            ";

            $stmt = $this->db->prepare($allergiesQuery);
            $stmt->execute([$patientId]);
            $allergies = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Récupérer les prescriptions
            $prescriptionsQuery = "
                SELECT 
                    'prescription' as type,
                    p.date_prescription as date,
                    'Prescription médicale' as title,
                    p.description,
                    JSON_OBJECT(
                        'medicaments', p.medicaments,
                        'posologie', p.posologie,
                        'duree', p.duree,
                        'doctor', CONCAT(m.nom, ' ', m.prenom)
                    ) as details
                FROM prescriptions p
                INNER JOIN medecins m ON p.medecin_id = m.id
                WHERE p.patient_id = ?
                ORDER BY p.date_prescription DESC
            ";

            $stmt = $this->db->prepare($prescriptionsQuery);
            $stmt->execute([$patientId]);
            $prescriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Récupérer les documents
            $documentsQuery = "
                SELECT 
                    'document' as type,
                    d.date_creation as date,
                    d.nom_fichier as title,
                    d.description,
                    JSON_OBJECT(
                        'id', d.id,
                        'type', d.type_fichier,
                        'taille', d.taille_fichier,
                        'chemin', d.chemin_fichier
                    ) as details
                FROM documents d
                WHERE d.patient_id = ?
                ORDER BY d.date_creation DESC
            ";

            $stmt = $this->db->prepare($documentsQuery);
            $stmt->execute([$patientId]);
            $documents = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Fusionner et trier tous les événements par date
            $history = array_merge($appointments, $allergies, $prescriptions, $documents);
            usort($history, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $history
            ]);

        } catch (PDOException $e) {
            error_log("Erreur dans getHistory: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération de l\'historique',
                'error' => $e->getMessage()
            ]);
        }
    }
} 