<?php
require_once __DIR__ . '/../models/Doctor.php';

class DoctorController {
    private $doctorModel;
    private $db;
    
    public function __construct($db) {
        $this->doctorModel = new Doctor();
        $this->db = $db;
    }
    
    public function index() {
        try {
            // Requête modifiée pour récupérer les informations complètes
            $query = "SELECT 
                        m.id as medecin_id,
                        m.specialite,
                        m.telephone,
                        u.id as user_id,
                        u.nom,
                        u.prenom,
                        u.email,
                        u.role
                     FROM medecins m
                     INNER JOIN utilisateur u ON m.user_id = u.id
                     WHERE u.role = 'doctor'
                     ORDER BY u.nom ASC, u.prenom ASC";
            
            error_log('Exécution de la requête SQL: ' . $query);
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            $doctors = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('Résultats bruts de la requête: ' . print_r($doctors, true));
            
            // Formater les données pour l'affichage
            $formattedDoctors = array_map(function($doctor) {
                return [
                    'id' => $doctor['medecin_id'],
                    'user_id' => $doctor['user_id'],
                    'nom' => $doctor['nom'],
                    'prenom' => $doctor['prenom'],
                    'email' => $doctor['email'],
                    'specialite' => $doctor['specialite'],
                    'telephone' => $doctor['telephone'],
                    'fullName' => $doctor['nom'] . ' ' . $doctor['prenom']
                ];
            }, $doctors);
            
            error_log('Nombre de médecins trouvés : ' . count($formattedDoctors));
            error_log('Données formatées: ' . print_r($formattedDoctors, true));
            
            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $formattedDoctors
            ]);
            
        } catch (Exception $e) {
            error_log('Erreur lors de la récupération des médecins: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des médecins',
                'details' => $e->getMessage()
            ]);
        }
    }

    public function create() {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Données invalides',
                    'field' => 'general'
                ]);
                return;
            }

            // Validation des champs requis
            $requiredFields = ['nom', 'prenom', 'email', 'specialite', 'password'];
            $errors = [];
            
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    $errors[$field] = "Le champ $field est requis";
                }
            }

            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Veuillez remplir tous les champs requis',
                    'errors' => $errors
                ]);
                return;
            }

            // Vérification de l'email unique
            if ($this->doctorModel->emailExists($data['email'])) {
                http_response_code(409);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Cet email est déjà utilisé',
                    'field' => 'email',
                    'code' => 'EMAIL_EXISTS'
                ]);
                return;
            }

            $result = $this->doctorModel->create($data);
            
            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Médecin créé avec succès',
                    'data' => $result
                ]);
            } else {
                throw new Exception('Erreur lors de la création du médecin');
            }
            
        } catch (Exception $e) {
            error_log('Erreur création médecin: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création du médecin',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function update($id) {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            error_log('Données reçues pour mise à jour: ' . print_r($data, true));

            // Validation des données
            if (empty($id)) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'ID du médecin manquant'
                ]);
                return;
            }

            // Vérifier si le médecin existe
            $stmt = $this->db->prepare("
                SELECT m.*, u.email, u.nom, u.prenom
                FROM medecins m
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE m.id = ?
            ");
            $stmt->execute([$id]);
            $doctor = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$doctor) {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Médecin non trouvé'
                ]);
                return;
            }

            // Vérifier si l'email est déjà utilisé par un autre utilisateur
            if (!empty($data['email']) && $data['email'] !== $doctor['email']) {
                $stmt = $this->db->prepare("SELECT id FROM utilisateur WHERE email = ? AND id != ?");
                $stmt->execute([$data['email'], $doctor['user_id']]);
                if ($stmt->fetch()) {
                    http_response_code(400);
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'Cet email est déjà utilisé'
                    ]);
                    return;
                }
            }

            // Démarrer une transaction
            $this->db->beginTransaction();

            // 1. Mettre à jour l'utilisateur
            $userUpdates = [];
            $userParams = [];
            if (!empty($data['nom'])) {
                $userUpdates[] = "nom = ?";
                $userParams[] = $data['nom'];
            }
            if (!empty($data['prenom'])) {
                $userUpdates[] = "prenom = ?";
                $userParams[] = $data['prenom'];
            }
            if (!empty($data['email'])) {
                $userUpdates[] = "email = ?";
                $userParams[] = $data['email'];
            }
            if (!empty($data['password'])) {
                $userUpdates[] = "password = ?";
                $userParams[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            if (!empty($userUpdates)) {
                $userParams[] = $doctor['user_id'];
                $sql = "UPDATE utilisateur SET " . implode(", ", $userUpdates) . " WHERE id = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute($userParams);
            }

            // 2. Mettre à jour le médecin
            $doctorUpdates = [];
            $doctorParams = [];
            if (!empty($data['specialite'])) {
                $doctorUpdates[] = "specialite = ?";
                $doctorParams[] = $data['specialite'];
            }
            if (isset($data['telephone'])) {
                $doctorUpdates[] = "telephone = ?";
                $doctorParams[] = $data['telephone'];
            }

            if (!empty($doctorUpdates)) {
                $doctorParams[] = $id;
                $sql = "UPDATE medecins SET " . implode(", ", $doctorUpdates) . " WHERE id = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute($doctorParams);
            }

            // Valider la transaction
            $this->db->commit();

            // Récupérer les données mises à jour
            $stmt = $this->db->prepare("
                SELECT 
                    m.id as medecin_id,
                    m.specialite,
                    m.telephone,
                    u.id as user_id,
                    u.nom,
                    u.prenom,
                    u.email,
                    u.role
                FROM medecins m
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE m.id = ?
            ");
            $stmt->execute([$id]);
            $updatedDoctor = $stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'status' => 'success',
                'message' => 'Médecin mis à jour avec succès',
                'data' => [
                    'id' => $updatedDoctor['medecin_id'],
                    'user_id' => $updatedDoctor['user_id'],
                    'nom' => $updatedDoctor['nom'],
                    'prenom' => $updatedDoctor['prenom'],
                    'email' => $updatedDoctor['email'],
                    'specialite' => $updatedDoctor['specialite'],
                    'telephone' => $updatedDoctor['telephone'],
                    'fullName' => $updatedDoctor['nom'] . ' ' . $updatedDoctor['prenom']
                ]
            ]);

        } catch (PDOException $e) {
            $this->db->rollBack();
            error_log("Erreur lors de la mise à jour du médecin: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la mise à jour du médecin',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function delete($id) {
        try {
            // Vérifier si l'ID est valide
            if (!is_numeric($id) || $id <= 0) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'ID de médecin invalide'
                ]);
                return;
            }

            $this->db->beginTransaction();

            // Récupérer l'ID de l'utilisateur associé
            $query = "SELECT user_id, nom, prenom FROM medecins WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            $doctor = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$doctor) {
                $this->db->rollBack();
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Médecin non trouvé'
                ]);
                return;
            }

            // Supprimer d'abord le médecin
            $query = "DELETE FROM medecins WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);

            // Puis supprimer l'utilisateur associé
            if ($doctor['user_id']) {
                $query = "DELETE FROM utilisateur WHERE id = ?";
                $stmt = $this->db->prepare($query);
                $stmt->execute([$doctor['user_id']]);
            }

            $this->db->commit();

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => 'Le médecin ' . $doctor['nom'] . ' ' . $doctor['prenom'] . ' a été supprimé avec succès'
            ]);

        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Erreur lors de la suppression du médecin: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression du médecin',
                'details' => $e->getMessage()
            ]);
        }
    }
    
    public function getById($id) {
        try {
            $query = "
                SELECT
                    m.*,
                    u.email,
                    u.role
                FROM medecins m
                LEFT JOIN utilisateur u ON m.user_id = u.id
                WHERE m.id = ? AND (u.role = 'doctor' OR u.role IS NULL)
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            $doctor = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($doctor) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'id' => $doctor['id'],
                        'nom' => $doctor['nom'],
                        'prenom' => $doctor['prenom'],
                        'email' => $doctor['email'],
                        'specialite' => $doctor['specialite'],
                        'telephone' => $doctor['telephone'],
                        'role' => 'doctor'
                    ]
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => 'Médecin non trouvé']);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la récupération du médecin: ' . $e->getMessage()]);
        }
    }

    public function getByUserId($userId) {
        try {
            $query = "
                SELECT
                    m.id as medecin_id,
                    m.specialite,
                    m.telephone,
                    u.id as user_id,
                    u.nom,
                    u.prenom,
                    u.email,
                    u.role
                FROM medecins m
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE u.id = ? AND u.role = 'doctor'
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$userId]);
            $doctor = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($doctor) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'id' => $doctor['medecin_id'],
                        'user_id' => $doctor['user_id'],
                        'nom' => $doctor['nom'],
                        'prenom' => $doctor['prenom'],
                        'email' => $doctor['email'],
                        'specialite' => $doctor['specialite'],
                        'telephone' => $doctor['telephone'],
                        'fullName' => $doctor['nom'] . ' ' . $doctor['prenom']
                    ]
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Médecin non trouvé pour cet utilisateur'
                ]);
            }
        } catch (PDOException $e) {
            error_log("Erreur getByUserId: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération du médecin: ' . $e->getMessage()
            ]);
        }
    }
    
    public function getAvailability($doctorId) {
        $date = $_GET['date'] ?? date('Y-m-d');
        
        try {
            $availability = $this->doctorModel->getAvailableSlots($doctorId, $date);
            echo json_encode([
                'success' => true,
                'data' => $availability
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getStatistics($doctorId) {
        error_log("Début de getStatistics pour doctorId: " . $doctorId);
        try {
            // Nombre total de patients
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM patients");
            $stmt->execute();
            $totalPatients = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            error_log("Total patients: " . $totalPatients);

            // Nombre de rendez-vous ce mois-ci
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total 
                FROM rendez_vous 
                WHERE id_medecin = ? 
                AND MONTH(date_rendez_vous) = MONTH(CURRENT_DATE())
                AND YEAR(date_rendez_vous) = YEAR(CURRENT_DATE())
            ");
            $stmt->execute([$doctorId]);
            $monthlyAppointments = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            error_log("Rendez-vous mensuels: " . $monthlyAppointments);

            // Taux de présence (rendez-vous honorés)
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(CASE WHEN statut = 'honoré' THEN 1 END) * 100.0 / COUNT(*) as completion_rate
                FROM rendez_vous 
                WHERE id_medecin = ?
                AND date_rendez_vous < CURRENT_TIMESTAMP
            ");
            $stmt->execute([$doctorId]);
            $completionRate = round($stmt->fetch(PDO::FETCH_ASSOC)['completion_rate'] ?? 0, 2);

            // Durée moyenne des consultations (en minutes)
            $stmt = $this->db->prepare("
                SELECT AVG(TIMESTAMPDIFF(MINUTE, date_rendez_vous, DATE_ADD(date_rendez_vous, INTERVAL duree MINUTE))) as avg_duration
                FROM rendez_vous 
                WHERE id_medecin = ?
                AND statut = 'honoré'
            ");
            $stmt->execute([$doctorId]);
            $averageConsultationTime = round($stmt->fetch(PDO::FETCH_ASSOC)['avg_duration'] ?? 30);

            $statistics = [
                'totalPatients' => $totalPatients,
                'monthlyAppointments' => $monthlyAppointments,
                'completionRate' => $completionRate,
                'averageConsultationTime' => $averageConsultationTime
            ];

            header('Content-Type: application/json');
            echo json_encode($statistics);

        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la récupération des statistiques']);
        }
    }

    /**
     * Récupère tous les médecins
     */
    public function getAll() {
        try {
            $doctors = $this->doctorModel->findAll();
            echo json_encode([
                'success' => true,
                'data' => $doctors
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Récupère les horaires de travail d'un médecin
     */
    public function getWorkingHours($id) {
        try {
            $horaires = $this->doctorModel->getWorkingHours($id);
            if ($horaires === null) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Médecin non trouvé'
                ]);
                return;
            }
            
            echo json_encode([
                'success' => true,
                'data' => $horaires
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Met à jour les horaires de travail d'un médecin
     */
    public function updateWorkingHours($id) {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (empty($data)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Aucun horaire fourni'
                ]);
                return;
            }
            
            $success = $this->doctorModel->updateWorkingHours($id, $data);
            if (!$success) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Médecin non trouvé'
                ]);
                return;
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Horaires mis à jour avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Vérifie la disponibilité d'un médecin
     */
    public function checkAvailability($id) {
        try {
            $dateTime = $_GET['datetime'] ?? null;
            if (!$dateTime) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Date et heure requises'
                ]);
                return;
            }
            
            $isAvailable = $this->doctorModel->isAvailable($id, $dateTime);
            echo json_encode([
                'success' => true,
                'data' => [
                    'available' => $isAvailable
                ]
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Récupère les créneaux disponibles
     */
    public function getAvailableSlots($id) {
        try {
            $date = $_GET['date'] ?? date('Y-m-d');
            $slots = $this->doctorModel->getAvailableSlots($id, $date);
            
            // Formater les créneaux pour le frontend
            $formattedSlots = array_map(function($slot) {
                return [
                    'time' => $slot['time'],
                    'duree' => $slot['duree'] ?? 30,
                    'isAvailable' => true
                ];
            }, $slots);
            
            echo json_encode([
                'success' => true,
                'data' => $formattedSlots
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

public function createDoctor(Request $request) {
    try {
        // Validation des données
        $validator = Validator::make($request->all(), [
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|unique:doctors',
            'specialite' => 'required|string',
            // Ajoutez d'autres règles de validation
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 400);
        }

        // Création du médecin
        $doctor = Doctor::create($request->all());

        return response()->json([
            'success' => true,
            'data' => $doctor
        ], 201);

    } catch (\Exception $e) {
        \Log::error("Erreur création médecin: " . $e->getMessage());
        
        return response()->json([
            'success' => false,
            'message' => 'Erreur serveur',
            'error' => config('app.debug') ? $e->getMessage() : null
        ], 500);
    }
}
}
?>