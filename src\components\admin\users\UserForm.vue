<template>
  <ConfirmationModal
    :show="show"
    @close="$emit('close')"
    @confirm="saveUser"
    class="user-form-modal"
  >
    <template #title>
      <div class="modal-title">
        <i :class="isEditing ? 'fas fa-user-edit' : 'fas fa-user-plus'"></i>
        {{ isEditing ? 'Modifier Utilisateur' : 'Nouvel Utilisateur' }}
      </div>
    </template>

    <div class="form-container">
      <div class="form-grid">
        <!-- Nom -->
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-user-tag"></i>
            Nom
          </label>
          <div class="input-wrapper">
            <input 
              v-model="form.nom" 
              type="text" 
              class="form-input"
              placeholder="Entrez le nom"
              :class="{ 'error': errors.nom }"
            >
            <div v-if="errors.nom" class="error-message">{{ errors.nom }}</div>
          </div>
        </div>

        <!-- Prénom -->
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-user"></i>
            Prénom
          </label>
          <div class="input-wrapper">
            <input 
              v-model="form.prenom" 
              type="text" 
              class="form-input"
              placeholder="Entrez le prénom"
              :class="{ 'error': errors.prenom }"
            >
            <div v-if="errors.prenom" class="error-message">{{ errors.prenom }}</div>
          </div>
        </div>
      </div>

      <!-- Email -->
      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-envelope"></i>
          Adresse Email
        </label>
        <div class="input-wrapper">
          <input 
            v-model="form.email" 
            type="email" 
            class="form-input"
            placeholder="<EMAIL>"
            :class="{ 'error': errors.email }"
          >
          <div v-if="errors.email" class="error-message">{{ errors.email }}</div>
        </div>
      </div>

      <!-- Mot de passe -->
      <div v-if="!isEditing" class="form-group">
        <label class="form-label">
          <i class="fas fa-lock"></i>
          Mot de passe
        </label>
        <div class="input-wrapper">
          <div class="password-input">
            <input 
              v-model="form.password" 
              :type="showPassword ? 'text' : 'password'"
              class="form-input"
              placeholder="Entrez le mot de passe"
              :class="{ 'error': errors.password }"
            >
            <button 
              type="button"
              @click="showPassword = !showPassword"
              class="password-toggle"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
          <div class="password-strength">
            <div class="strength-bar">
              <div 
                class="strength-fill"
                :class="passwordStrengthClass"
                :style="{ width: passwordStrength + '%' }"
              ></div>
            </div>
            <span class="strength-text">{{ passwordStrengthText }}</span>
          </div>
        </div>
      </div>

      <!-- Rôle -->
      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-shield-alt"></i>
          Rôle Utilisateur
        </label>
        <div class="input-wrapper">
          <div class="select-wrapper">
            <select v-model="form.role" class="form-select" :class="{ 'error': errors.role }">
              <option value="" disabled>Sélectionnez un rôle</option>
              <option value="admin">
                👑 Administrateur
              </option>
              <option value="doctor">
                🩺 Médecin
              </option>
              <option value="patient">
                👤 Patient
              </option>
            </select>
            <i class="fas fa-chevron-down select-arrow"></i>
          </div>
          <div v-if="errors.role" class="error-message">{{ errors.role }}</div>
        </div>
      </div>

      <!-- Informations du rôle -->
      <div v-if="form.role" class="role-info">
        <div class="role-card" :class="`role-${form.role}`">
          <div class="role-icon">
            <i :class="getRoleIcon(form.role)"></i>
          </div>
          <div class="role-details">
            <h4>{{ getRoleName(form.role) }}</h4>
            <p>{{ getRoleDescription(form.role) }}</p>
          </div>
        </div>
      </div>
    </div>
  </ConfirmationModal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useAdminStore } from '@/stores/adminStore'
import ConfirmationModal from '../shared/ConfirmationModal.vue'

const props = defineProps({
  user: Object,
  isEditing: Boolean,
  show: Boolean
})

const emit = defineEmits(['close'])

const adminStore = useAdminStore()
const form = ref({})
const errors = ref({})
const showPassword = ref(false)

// Validation des champs
const validateForm = () => {
  errors.value = {}
  
  if (!form.value.nom?.trim()) {
    errors.value.nom = 'Le nom est requis'
  }
  
  if (!form.value.prenom?.trim()) {
    errors.value.prenom = 'Le prénom est requis'
  }
  
  if (!form.value.email?.trim()) {
    errors.value.email = 'L\'email est requis'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
    errors.value.email = 'Format d\'email invalide'
  }
  
  if (!props.isEditing && !form.value.password?.trim()) {
    errors.value.password = 'Le mot de passe est requis'
  } else if (!props.isEditing && form.value.password && form.value.password.length < 8) {
    errors.value.password = 'Le mot de passe doit contenir au moins 8 caractères'
  }
  
  if (!form.value.role) {
    errors.value.role = 'Le rôle est requis'
  }
  
  return Object.keys(errors.value).length === 0
}

// Force du mot de passe
const passwordStrength = computed(() => {
  if (!form.value.password) return 0
  
  let strength = 0
  const password = form.value.password
  
  if (password.length >= 8) strength += 25
  if (/[a-z]/.test(password)) strength += 25
  if (/[A-Z]/.test(password)) strength += 25
  if (/[0-9]/.test(password)) strength += 12.5
  if (/[^A-Za-z0-9]/.test(password)) strength += 12.5
  
  return Math.min(100, strength)
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength < 30) return 'weak'
  if (strength < 60) return 'medium'
  if (strength < 80) return 'good'
  return 'strong'
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength < 30) return 'Faible'
  if (strength < 60) return 'Moyen'
  if (strength < 80) return 'Bon'
  return 'Très fort'
})

// Utilitaires pour les rôles
const getRoleIcon = (role) => {
  const icons = {
    admin: 'fas fa-crown',
    doctor: 'fas fa-user-md',
    patient: 'fas fa-user'
  }
  return icons[role] || 'fas fa-user'
}

const getRoleName = (role) => {
  const names = {
    admin: 'Administrateur',
    doctor: 'Médecin',
    patient: 'Patient'
  }
  return names[role] || 'Utilisateur'
}

const getRoleDescription = (role) => {
  const descriptions = {
    admin: 'Accès complet à toutes les fonctionnalités du système',
    doctor: 'Accès aux dossiers patients et aux outils médicaux',
    patient: 'Accès à son dossier médical et prise de rendez-vous'
  }
  return descriptions[role] || 'Utilisateur standard'
}

watch(() => props.user, (newUser) => {
  errors.value = {}
  if (newUser && props.isEditing) {
    form.value = {
      nom: newUser.nom || '',
      prenom: newUser.prenom || '',
      email: newUser.email || '',
      role: newUser.role || 'doctor',
    }
  } else {
    form.value = {
      nom: '',
      prenom: '',
      email: '',
      password: '',
      role: 'doctor'
    }
  }
}, { immediate: true, deep: true })

const saveUser = async () => {
  if (!validateForm()) {
    return
  }
  
  try {
    if (props.isEditing) {
      await adminStore.updateUser(props.user.id, form.value)
    } else {
      await adminStore.createUser(form.value)
    }
    emit('close')
  } catch (error) {
    console.error('Erreur lors de la sauvegarde:', error)
  }
}
</script>

<style scoped>
.user-form-modal {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
}

.modal-title i {
  color: var(--primary-color);
}

.form-container {
  padding: 1.5rem 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-label i {
  color: var(--primary-color);
  width: 16px;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
  color: var(--gray-800);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  transform: translateY(-2px);
}

.form-input.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input::placeholder {
  color: var(--gray-400);
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.password-strength {
  margin-top: 0.5rem;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: linear-gradient(90deg, #ef4444, #f87171);
}

.strength-fill.medium {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.strength-fill.good {
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
}

.strength-fill.strong {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.strength-text {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.select-wrapper {
  position: relative;
}

.form-select {
  width: 100%;
  padding: 0.875rem 2.5rem 0.875rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 0.95rem;
  background: white;
  color: var(--gray-800);
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  transform: translateY(-2px);
}

.form-select.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
  font-size: 0.75rem;
}

.error-message {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: '⚠';
  font-size: 0.875rem;
}

.role-info {
  margin-top: 1rem;
  animation: fadeInUp 0.3s ease;
}

.role-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  border: 2px solid;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  backdrop-filter: blur(10px);
}

.role-card.role-admin {
  border-color: #fca5a5;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.role-card.role-doctor {
  border-color: #93c5fd;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.role-card.role-patient {
  border-color: #6ee7b7;
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
}

.role-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.role-admin .role-icon {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
}

.role-doctor .role-icon {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
}

.role-patient .role-icon {
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
}

.role-details h4 {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.role-details p {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.4;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .modal-title {
    font-size: 1.25rem;
  }
  
  .form-container {
    max-height: 60vh;
  }
  
  .role-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
}
</style>