<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{ isEditing ? 'Modifier le Patient' : 'Ajouter un Patient' }}</h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="patient-form">
        <!-- Informations Personnelles -->
        <div class="form-section">
          <h3>Informations Personnelles</h3>
          <div class="form-grid">
            <div class="form-group">
              <label for="lastName">Nom</label>
              <input 
                id="lastName"
                v-model="formData.nom"
                type="text"
                required
                placeholder="Nom du patient"
              >
            </div>

            <div class="form-group">
              <label for="firstName">Prénom</label>
              <input 
                id="firstName"
                v-model="formData.prenom"
                type="text"
                required
                placeholder="Prénom du patient"
              >
            </div>

            <div class="form-group">
              <label for="birthDate">Date de Naissance</label>
              <input 
                id="birthDate"
                v-model="formData.date_naissance"
                type="date"
                required
              >
            </div>

            <div class="form-group">
              <label for="socialSecurityNumber">Numéro de Sécurité Sociale</label>
              <input 
                id="socialSecurityNumber"
                v-model="formData.numero_securite_sociale"
                type="text"
                required
                pattern="[0-9]{15}"
                title="Le numéro de sécurité sociale doit contenir 15 chiffres"
                placeholder="15 chiffres"
              >
            </div>

            <div class="form-group">
              <label for="email">Email</label>
              <input 
                id="email"
                v-model="formData.email"
                type="email"
                required
                placeholder="<EMAIL>"
              >
            </div>

            <div class="form-group">
              <label for="phone">Téléphone</label>
              <input 
                id="phone"
                v-model="formData.telephone"
                type="tel"
                required
                placeholder="0X XX XX XX XX"
              >
            </div>

            <div class="form-group">
              <label for="address">Adresse</label>
              <textarea 
                id="address"
                v-model="formData.adresse"
                rows="2"
                placeholder="Adresse complète"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Informations Médicales -->
        <div class="form-section">
          <h3>Informations Médicales</h3>
          <div class="form-grid">
            <div class="form-group">
              <label for="bloodGroup">Groupe Sanguin</label>
              <select id="bloodGroup" v-model="formData.groupe_sanguin">
                <option value="">Sélectionner</option>
                <option value="A+">A+</option>
                <option value="A-">A-</option>
                <option value="B+">B+</option>
                <option value="B-">B-</option>
                <option value="AB+">AB+</option>
                <option value="AB-">AB-</option>
                <option value="O+">O+</option>
                <option value="O-">O-</option>
              </select>
            </div>

            <div class="form-group">
              <label for="allergies">Allergies</label>
              <textarea 
                id="allergies"
                v-model="formData.allergies"
                rows="2"
                placeholder="Liste des allergies (séparées par des virgules)"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="medicaments">Médicaments</label>
              <textarea 
                id="medicaments"
                v-model="formData.medicaments"
                rows="2"
                placeholder="Liste des médicaments (séparés par des virgules)"
              ></textarea>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="cancel-btn" @click="$emit('close')">
            Annuler
          </button>
          <button type="submit" class="submit-btn" :disabled="loading">
            {{ loading ? 'Enregistrement...' : (isEditing ? 'Mettre à jour' : 'Ajouter') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  patient: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'submit'])

const isEditing = computed(() => Object.keys(props.patient).length > 0)
const loading = ref(false)

const formData = ref({
  nom: '',
  prenom: '',
  date_naissance: '',
  numero_securite_sociale: '',
  email: '',
  telephone: '',
  adresse: '',
  groupe_sanguin: '',
  allergies: '',
  medicaments: '',
  user_id: null
})

onMounted(() => {
  console.log('Patient reçu:', props.patient)
  if (isEditing.value) {
    Object.keys(formData.value).forEach(key => {
      if (key === 'allergies' && Array.isArray(props.patient[key])) {
        formData.value[key] = props.patient[key].join(', ')
      } else if (key === 'medicaments' && Array.isArray(props.patient[key])) {
        formData.value[key] = props.patient[key].join(', ')
      } else {
        formData.value[key] = props.patient[key] || ''
      }
    })
  }
})

const resetForm = () => {
  Object.keys(formData.value).forEach(key => {
    formData.value[key] = ''
  })
}

const handleSubmit = async () => {
  loading.value = true
  try {
    const payload = {
      ...formData.value,
      allergies: formData.value.allergies
        ? formData.value.allergies.split(',').map(s => s.trim()).filter(Boolean)
        : [],
      medicaments: formData.value.medicaments
        ? formData.value.medicaments.split(',').map(s => s.trim()).filter(Boolean)
        : []
    }

    emit('submit', payload)

    // Attendre un peu pour que la soumission se termine
    await new Promise(resolve => setTimeout(resolve, 500))

    if (!isEditing.value) {
      resetForm()
    }

    // Fermer le modal après soumission réussie
    emit('close')

  } catch (error) {
    console.error('Erreur lors de la soumission:', error)
  } finally {
    loading.value = false
  }
}

</script>

<style scoped>
/* Variables CSS - Palette Professionnelle */
:root {
  /* Couleurs principales */
  --primary-indigo: #4f46e5;
  --primary-indigo-light: #6366f1;
  --primary-indigo-dark: #3730a3;
  --primary-indigo-pale: #f0f0ff;

  /* Couleurs secondaires */
  --secondary-emerald: #10b981;
  --secondary-emerald-light: #34d399;
  --secondary-emerald-dark: #059669;
  --secondary-emerald-pale: #ecfdf5;

  /* Couleurs neutres */
  --neutral-white: #ffffff;
  --neutral-gray-50: #f8fafc;
  --neutral-gray-100: #f1f5f9;
  --neutral-gray-200: #e2e8f0;
  --neutral-gray-300: #cbd5e1;
  --neutral-gray-400: #94a3b8;
  --neutral-gray-500: #64748b;
  --neutral-gray-600: #475569;
  --neutral-gray-700: #334155;
  --neutral-gray-800: #1e293b;
  --neutral-gray-900: #0f172a;

  /* Couleurs de statut */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;

  /* Ombres */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --transition-fast: all 0.15s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: var(--neutral-white);
  border-radius: 20px;
  width: 90%;
  max-width: 900px;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--neutral-gray-200);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary-indigo) 0%, var(--primary-indigo-dark) 100%);
  color: var(--neutral-white);
  position: relative;
  overflow: hidden;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-header h2::before {
  content: '👤';
  font-size: 1.5rem;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--neutral-white);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 50%;
  transition: var(--transition-normal);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: rotate(90deg) scale(1.1);
}

.patient-form {
  padding: 2.5rem;
  max-height: calc(95vh - 120px);
  overflow-y: auto;
}

.form-section {
  margin-bottom: 3rem;
  background: var(--neutral-gray-50);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid var(--neutral-gray-200);
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-indigo), var(--secondary-emerald));
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--neutral-gray-800);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.form-section:nth-child(1) h3::before {
  content: '📋';
  font-size: 1.25rem;
}

.form-section:nth-child(2) h3::before {
  content: '🏥';
  font-size: 1.25rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
}

label {
  font-size: 0.875rem;
  color: var(--neutral-gray-700);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

label::before {
  content: '';
  width: 3px;
  height: 16px;
  background: var(--primary-indigo);
  border-radius: 2px;
}

input,
select,
textarea {
  padding: 1rem;
  border: 2px solid var(--neutral-gray-200);
  border-radius: 12px;
  font-size: 1rem;
  color: var(--neutral-gray-800);
  transition: var(--transition-normal);
  background: var(--neutral-white);
  box-shadow: var(--shadow-sm);
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-indigo);
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1), var(--shadow-md);
  transform: translateY(-1px);
}

input:valid,
select:valid,
textarea:valid {
  border-color: var(--secondary-emerald);
}

textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--neutral-gray-200);
  justify-content: flex-end;
}

.cancel-btn,
.submit-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 140px;
  justify-content: center;
}

.cancel-btn {
  background: var(--neutral-gray-100);
  color: var(--neutral-gray-600);
  border: 2px solid var(--neutral-gray-200);
}

.cancel-btn:hover {
  background: var(--neutral-gray-200);
  color: var(--neutral-gray-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-indigo) 0%, var(--secondary-emerald) 100%);
  color: var(--neutral-white);
  box-shadow: var(--shadow-lg);
}

.submit-btn:hover {
  background: linear-gradient(135deg, var(--primary-indigo-dark) 0%, var(--secondary-emerald-dark) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Animations pour les champs */
.form-group {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }
.form-group:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 1rem;
    border-radius: 16px;
  }

  .modal-header {
    padding: 1.5rem;
  }

  .modal-header h2 {
    font-size: 1.5rem;
  }

  .patient-form {
    padding: 1.5rem;
  }

  .form-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}

/* États de validation */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
  border-color: var(--status-error);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-group.success input,
.form-group.success select,
.form-group.success textarea {
  border-color: var(--status-success);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

/* Indicateurs de champs requis */
label[required]::after {
  content: ' *';
  color: var(--status-error);
  font-weight: bold;
}
</style>