<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>
        <i class="fas fa-tachometer-alt"></i>
        Aperçu Rapide
      </h2>
      <div class="dashboard-time">
        <i class="fas fa-clock"></i>
        {{ currentTime }}
      </div>
    </div>

    <!-- Cartes principales du tableau de bord -->
    <div class="dashboard-cards">
      <!-- Rendez-vous aujourd'hui -->
      <div class="dashboard-card today-appointments">
        <div class="card-header">
          <h3>
            <i class="fas fa-calendar-day"></i>
            Rendez-vous aujourd'hui
          </h3>
          <div class="card-badge today">{{ todayCount }}</div>
        </div>
        <div class="dashboard-value primary">{{ todayCount }}</div>
        <div class="dashboard-desc">
          <i class="fas fa-info-circle"></i>
          Total des rendez-vous prévus aujourd'hui
        </div>
        <div class="card-footer">
          <button class="quick-action-btn" @click="viewTodayAppointments">
            <i class="fas fa-eye"></i>
            Voir la liste
          </button>
        </div>
      </div>

      <!-- Conflits détectés -->
      <div class="dashboard-card conflict" :class="{ 'has-conflicts': conflictCount > 0 }">
        <div class="conflict-header">
          <h3>
            <i class="fas fa-exclamation-triangle conflict-icon"></i>
            Conflits détectés
          </h3>
          <div class="conflict-badge" v-if="conflictCount > 0">{{ conflictCount }}</div>
        </div>
        <div class="dashboard-value" :class="{ 'danger': conflictCount > 0 }">
          {{ conflictCount }}
        </div>
        <div class="dashboard-desc">
          <span v-if="conflictCount === 0" class="no-conflict">
            <i class="fas fa-check-circle"></i>
            Aucun conflit de planning
          </span>
          <span v-else class="has-conflict">
            <i class="fas fa-clock"></i>
            {{ conflictCount === 1 ? 'Conflit' : 'Conflits' }} de planning à résoudre
          </span>
        </div>

        <!-- Détails des conflits -->
        <div v-if="conflictCount > 0" class="conflict-details">
          <div class="conflict-list">
            <div
              v-for="(conflict, index) in conflictDetails"
              :key="`conflict-${index}-${conflict.apt1?.id}-${conflict.apt2?.id}`"
              class="conflict-item"
            >
              <div class="conflict-time">
                <span class="time">
                  {{ formatTime(conflict.apt1?.date_rendez_vous || conflict.apt1?.date || conflict.time1) }}
                </span>
                <i class="fas fa-arrows-alt-h"></i>
                <span class="time">
                  {{ formatTime(conflict.apt2?.date_rendez_vous || conflict.apt2?.date || conflict.time2) }}
                </span>
              </div>
              <div class="conflict-patients" v-if="conflict.apt1?.patient_nom || conflict.apt2?.patient_nom">
                <small>
                  {{ conflict.apt1?.patient_nom || conflict.apt1?.patient || 'Patient 1' }}
                  ↔
                  {{ conflict.apt2?.patient_nom || conflict.apt2?.patient || 'Patient 2' }}
                </small>
              </div>
              <div class="conflict-gap">
                Écart: {{ conflict.gapMinutes || 0 }} min
              </div>
            </div>
          </div>
          <button class="resolve-btn" @click="showConflictModal = true">
            <i class="fas fa-tools"></i>
            Résoudre les conflits
          </button>
        </div>
      </div>

      <!-- Prochain rendez-vous -->
      <div class="dashboard-card next-appointment">
        <div class="card-header">
          <h3>
            <i class="fas fa-clock"></i>
            Prochain rendez-vous
          </h3>
          <div class="card-badge next" v-if="nextTime">
            <i class="fas fa-arrow-right"></i>
          </div>
        </div>
        <div class="dashboard-value accent">
          {{ nextTime || 'Aucun' }}
        </div>
        <div class="dashboard-desc">
          <i class="fas fa-calendar-check"></i>
          {{ nextAppointmentDetails || 'Aucun rendez-vous programmé' }}
        </div>
        <div class="card-footer" v-if="nextTime">
          <button class="quick-action-btn" @click="viewNextAppointment">
            <i class="fas fa-arrow-right"></i>
            Voir détails
          </button>
        </div>
      </div>

      <!-- Patients du jour -->
      <div class="dashboard-card patients-today">
        <div class="card-header">
          <h3>
            <i class="fas fa-users"></i>
            Patients du jour
          </h3>
          <div class="card-badge patients">{{ uniquePatientsToday }}</div>
        </div>
        <div class="dashboard-value secondary">{{ uniquePatientsToday }}</div>
        <div class="dashboard-desc">
          <i class="fas fa-user-friends"></i>
          Patients uniques à recevoir aujourd'hui
        </div>
        <div class="card-footer">
          <button class="quick-action-btn" @click="viewPatients">
            <i class="fas fa-list"></i>
            Liste complète
          </button>
        </div>
      </div>
    </div>

    <!-- Actions rapides -->
    <div class="quick-actions">
      <h3>
        <i class="fas fa-bolt"></i>
        Actions rapides
      </h3>
      <div class="actions-grid">
        <button class="action-card" @click="newAppointment">
          <i class="fas fa-plus-circle"></i>
          <span>Nouveau RDV</span>
        </button>
        <button class="action-card" @click="viewCalendar">
          <i class="fas fa-calendar-alt"></i>
          <span>Calendrier</span>
        </button>
        <button class="action-card" @click="viewPatients">
          <i class="fas fa-users"></i>
          <span>Patients</span>
        </button>
        <button class="action-card" @click="viewAnalytics">
          <i class="fas fa-chart-bar"></i>
          <span>Analyses</span>
        </button>
      </div>
    </div>

    <!-- Modal de résolution des conflits -->
    <ConflictResolutionModal
      :show="showConflictModal"
      :conflicts="conflictDetails"
      @close="showConflictModal = false"
      @reschedule="handleReschedule"
      @extend="handleExtend"
      @contact="handleContact"
      @resolve-all="handleResolveAll"
    />
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useConflictStore } from '@/stores/conflictStore'
import { useAuthStore } from '@/stores/authStore'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import api from '@/services/api'
import ConflictResolutionModal from './ConflictResolutionModal.vue'

// Définir les événements émis
const emit = defineEmits([
  'view-today-appointments',
  'view-next-appointment',
  'view-patients',
  'new-appointment',
  'view-calendar',
  'view-analytics'
])

const store = useAppointmentStore()
const conflictStore = useConflictStore()
const authStore = useAuthStore()
const suggestedSlotStore = useSuggestedSlotStore()
const showConflictModal = ref(false)
const currentTime = ref('')

// Mettre à jour l'heure actuelle
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// Mettre à jour l'heure toutes les secondes
setInterval(updateCurrentTime, 1000)
updateCurrentTime()

// Charge les rendez-vous au montage si tu utilises une API
onMounted(async () => {
  if (authStore.user && authStore.user.role === 'doctor') {
    // Récupérer les rendez-vous spécifiques au médecin connecté
    try {
      console.log('Tentative de récupération du médecin pour user ID:', authStore.user.id)

      // D'abord récupérer l'ID du médecin depuis l'API
      const doctorResponse = await api.get(`/doctors/user/${authStore.user.id}`)
      console.log('Réponse API médecin:', doctorResponse.data)

      if (doctorResponse.data.status === 'success' && doctorResponse.data.data) {
        const doctorId = doctorResponse.data.data.id
        console.log('ID médecin trouvé:', doctorId)
        await Promise.all([
          store.fetchAppointmentsByDoctor(doctorId),
          conflictStore.loadConflicts(doctorId),
          conflictStore.loadStats(doctorId),
          suggestedSlotStore.loadSlotsByDoctor(doctorId)
        ])
      } else {
        console.log('Médecin non trouvé, chargement de tous les rendez-vous')
        // Fallback: charger tous les rendez-vous et filtrer côté client
        await Promise.all([
          store.fetchAppointments(),
          conflictStore.loadConflicts(),
          conflictStore.loadStats(),
          suggestedSlotStore.loadSlotsByDoctor(null)
        ])
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données médecin:', error)
      console.log('Fallback: chargement de tous les rendez-vous')
      // Fallback: charger tous les rendez-vous
      await store.fetchAppointments()
    }
  } else {
    console.log('Utilisateur non médecin, chargement de tous les rendez-vous')
    await store.fetchAppointments()
  }
})

const todayCount = computed(() => {
  console.log('Appointments:', store.appointments)
  console.log('Today appointments:', store.todayAppointments)
  return store.todayAppointments.length
})

const conflictCount = computed(() => {
  console.log('Conflicts from conflictStore:', conflictStore.activeConflicts)
  return conflictStore.conflictCount
})

const nextTime = computed(() => {
  const nextAppt = store.nextAppointment
  console.log('Next appointment:', nextAppt)
  if (!nextAppt) return null
  const date = new Date(nextAppt.date_rendez_vous || nextAppt.date)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
})

// Détails du prochain rendez-vous
const nextAppointmentDetails = computed(() => {
  const nextAppt = store.nextAppointment
  if (!nextAppt) return null
  return `${nextAppt.patient_nom || nextAppt.patient || 'Patient'} - ${nextAppt.type || 'Consultation'}`
})

// Nombre de patients uniques aujourd'hui
const uniquePatientsToday = computed(() => {
  const todayAppointments = store.todayAppointments
  const uniquePatients = new Set()

  todayAppointments.forEach(apt => {
    if (apt.id_patient) {
      uniquePatients.add(apt.id_patient)
    }
  })

  return uniquePatients.size
})

// Utiliser les conflits depuis la base de données
const conflictDetails = computed(() => {
  console.log('Conflits depuis la base de données:', conflictStore.activeConflicts)
  return conflictStore.activeConflicts
})

// Méthodes utilitaires
const formatTime = (dateString) => {
  if (!dateString) {
    console.warn('formatTime: dateString est vide ou undefined')
    return 'Heure non définie'
  }

  const date = new Date(dateString)

  if (isNaN(date.getTime())) {
    console.warn('formatTime: Date invalide pour:', dateString)
    return 'Date invalide'
  }

  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// Méthodes de gestion des conflits
const handleReschedule = (data) => {
  console.log('Reprogrammer le rendez-vous:', data)

  if (data.action === 'reschedule') {
    // Simuler la reprogrammation
    showNotification(`Rendez-vous de ${data.appointment.patient_nom || 'Patient'} reprogrammé`, 'success')

    // TODO: Appeler l'API pour reprogrammer
    // await store.rescheduleAppointment(data.appointment.id, newDateTime)

    showConflictModal.value = false
  }
}

const handleExtend = (data) => {
  console.log('Prolonger le rendez-vous:', data)

  if (data.action === 'extend') {
    showNotification(`Durée du rendez-vous étendue à ${data.newDuration} minutes`, 'success')

    // TODO: Appeler l'API pour modifier la durée
    // await store.updateAppointmentDuration(data.appointment.id, data.newDuration)

    showConflictModal.value = false
  }
}

const handleContact = (data) => {
  console.log('Contacter le patient:', data)

  if (data.action === 'contact') {
    showNotification(`${data.method} - Contact initié avec ${data.appointment.patient_nom || 'Patient'}`, 'info')

    // TODO: Implémenter la logique de contact selon la méthode
    switch(data.method) {
      case 'Appeler le patient':
        // Ouvrir l'application téléphone ou afficher le numéro
        break
      case 'Envoyer un SMS':
        // Intégration SMS
        break
      case 'Envoyer un email':
        // Intégration email
        break
    }
  }
}

const handleResolveAll = (conflicts) => {
  console.log('Résolution automatique de tous les conflits:', conflicts)

  if (confirm(`Résoudre automatiquement ${conflicts.length} conflit(s) ?\n\nCela reprogrammera automatiquement les rendez-vous en conflit.`)) {
    // Simuler la résolution automatique
    conflicts.forEach((conflict, index) => {
      setTimeout(() => {
        showNotification(`Conflit ${index + 1}/${conflicts.length} résolu automatiquement`, 'success')
      }, index * 500)
    })

    setTimeout(() => {
      showNotification('Tous les conflits ont été résolus !', 'success')
      showConflictModal.value = false

      // TODO: Recharger les données
      // await store.fetchAppointments()
    }, conflicts.length * 500)
  }
}

// Fonction pour afficher les notifications
const showNotification = (message, type = 'info') => {
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'}"></i>
    ${message}
  `

  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'success' ? '#27ae60' : type === 'info' ? '#3498db' : '#e74c3c',
    color: 'white',
    padding: '1rem 1.5rem',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    zIndex: '9999',
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    fontSize: '0.9rem',
    fontWeight: '500',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease'
  })

  document.body.appendChild(notification)

  setTimeout(() => notification.style.transform = 'translateX(0)', 100)

  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => notification.remove(), 300)
  }, 3000)
}

// Méthodes pour les actions des boutons
const viewTodayAppointments = () => {
  console.log('Navigation vers les rendez-vous d\'aujourd\'hui')
  emit('view-today-appointments')
}

const viewNextAppointment = () => {
  console.log('Navigation vers le prochain rendez-vous')
  emit('view-next-appointment')
}

const viewPatients = () => {
  console.log('Navigation vers la liste des patients')
  emit('view-patients')
}

const newAppointment = () => {
  console.log('Création d\'un nouveau rendez-vous')
  emit('new-appointment')
}

const viewCalendar = () => {
  console.log('Navigation vers le calendrier')
  emit('view-calendar')
}

const viewAnalytics = () => {
  console.log('Navigation vers les analyses')
  emit('view-analytics')
}
</script>






<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header du dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.dashboard-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.dashboard-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.dashboard-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
}

/* Styles spécifiques pour chaque type de carte */
.dashboard-card.today-appointments::before {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.dashboard-card.next-appointment::before {
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.dashboard-card.patients-today::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

/* Headers des cartes */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.card-badge {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.card-badge.today {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.card-badge.next {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.card-badge.patients {
  background: linear-gradient(135deg, #27ae60, #229954);
}

/* Styles spécifiques pour les conflits */
.dashboard-card.conflict {
  border: 2px solid #ff6b6b;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
}

.dashboard-card.conflict::before {
  background: linear-gradient(90deg, #ff6b6b, #e74c3c);
}

.dashboard-card.conflict.has-conflicts {
  animation: pulse-warning 2s infinite;
  border-color: #e74c3c;
  box-shadow: 0 8px 32px rgba(231, 76, 60, 0.2);
}

@keyframes pulse-warning {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(231, 76, 60, 0.2);
  }
  50% {
    box-shadow: 0 8px 32px rgba(231, 76, 60, 0.4);
  }
}

/* Header des conflits */
.conflict-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.conflict-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.conflict-icon {
  color: #ff6b6b;
  font-size: 1.2rem;
  animation: shake 1s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.conflict-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Valeurs du dashboard */
.dashboard-value {
  font-size: 3rem;
  font-weight: 700;
  margin: 1.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-value.primary {
  color: #3498db;
}

.dashboard-value.secondary {
  color: #27ae60;
}

.dashboard-value.accent {
  color: #9b59b6;
}

.dashboard-value.danger {
  color: #e74c3c;
  animation: pulse-number 1.5s infinite;
}

@keyframes pulse-number {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Footers des cartes */
.card-footer {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.quick-action-btn {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  width: 100%;
  justify-content: center;
}

.quick-action-btn:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Actions rapides */
.quick-actions {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.quick-actions h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.3rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
  color: #495057;
}

.action-card:hover {
  border-color: #3498db;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.action-card i {
  font-size: 2rem;
  transition: all 0.3s ease;
}

.action-card:hover i {
  transform: scale(1.1);
}

.action-card span {
  font-weight: 600;
  font-size: 1rem;
}

/* Descriptions */
.dashboard-desc {
  font-size: 1rem;
  color: #7f8c8d;
  margin-bottom: 1rem;
}

.no-conflict {
  color: #27ae60;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.has-conflict {
  color: #e74c3c;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Détails des conflits */
.conflict-details {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid rgba(231, 76, 60, 0.1);
}

.conflict-list {
  margin-bottom: 1rem;
}

.conflict-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.conflict-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(231, 76, 60, 0.4);
  transform: translateX(4px);
}

.conflict-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.conflict-time .time {
  background: linear-gradient(135deg, #ff6b6b, #e74c3c);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
}

.conflict-time .fas {
  color: #e74c3c;
  font-size: 1.1rem;
}

.conflict-patients {
  text-align: center;
  margin: 0.5rem 0;
  color: #7f8c8d;
  font-size: 0.8rem;
}

.conflict-gap {
  color: #e74c3c;
  font-weight: 600;
  font-size: 0.85rem;
  text-align: center;
}

/* Bouton de résolution */
.resolve-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.8rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  box-shadow: 0 4px 16px rgba(231, 76, 60, 0.3);
}

.resolve-btn:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.resolve-btn:active {
  transform: translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .dashboard-value {
    font-size: 2.5rem;
  }

  .conflict-time {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
