<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>
        <i class="fas fa-tachometer-alt"></i>
        Aperçu Rapide
      </h2>
      <div class="dashboard-time">
        <i class="fas fa-clock"></i>
        {{ currentTime }}
      </div>
    </div>

    <!-- Cartes principales du tableau de bord -->
    <div class="dashboard-cards">
      <!-- Rendez-vous aujourd'hui -->
      <div class="dashboard-card today-appointments">
        <div class="card-header">
          <h3>
            <i class="fas fa-calendar-day"></i>
            {{ realDashboardData.todayAppointments.isToday ? 'Rendez-vous aujourd\'hui' : 'Rendez-vous récents' }}
          </h3>
          <div class="card-badge today">{{ todayCount }}</div>
        </div>
        <div class="dashboard-value primary">{{ todayCount }}</div>
        <div class="dashboard-desc">
          <i class="fas fa-info-circle"></i>
          {{ realDashboardData.todayAppointments.isToday ?
             'Total des rendez-vous prévus aujourd\'hui' :
             `Rendez-vous des ${realDashboardData.todayAppointments.recentCount || 3} derniers jours` }}
        </div>
        <div class="card-footer">
          <button class="quick-action-btn" @click="viewTodayAppointments">
            <i class="fas fa-eye"></i>
            Voir la liste
          </button>
        </div>
      </div>

      <!-- Conflits détectés -->
      <div class="dashboard-card conflict" :class="{ 'has-conflicts': conflictCount > 0 }">
        <div class="conflict-header">
          <h3>
            <i class="fas fa-exclamation-triangle conflict-icon"></i>
            Conflits détectés
          </h3>
          <div class="conflict-badge" v-if="conflictCount > 0">{{ conflictCount }}</div>
        </div>
        <div class="dashboard-value" :class="{ 'danger': conflictCount > 0 }">
          {{ conflictCount }}
        </div>
        <div class="dashboard-desc">
          <span v-if="conflictCount === 0" class="no-conflict">
            <i class="fas fa-check-circle"></i>
            Aucun conflit de planning
          </span>
          <span v-else class="has-conflict">
            <i class="fas fa-clock"></i>
            {{ conflictCount === 1 ? 'Conflit' : 'Conflits' }} de planning à résoudre
          </span>
        </div>

        <!-- Détails des conflits -->
        <div v-if="conflictCount > 0" class="conflict-details">
          <div class="conflict-list">
            <div
              v-for="(conflict, index) in conflictDetails"
              :key="`conflict-${index}-${conflict.apt1?.id}-${conflict.apt2?.id}`"
              class="conflict-item"
            >
              <div class="conflict-time">
                <span class="time">
                  {{ formatTime(conflict.apt1?.date_rendez_vous || conflict.apt1?.date || conflict.time1) }}
                </span>
                <i class="fas fa-arrows-alt-h"></i>
                <span class="time">
                  {{ formatTime(conflict.apt2?.date_rendez_vous || conflict.apt2?.date || conflict.time2) }}
                </span>
              </div>
              <div class="conflict-patients" v-if="conflict.apt1?.patient_nom || conflict.apt2?.patient_nom">
                <small>
                  {{ conflict.apt1?.patient_nom || conflict.apt1?.patient || 'Patient 1' }}
                  ↔
                  {{ conflict.apt2?.patient_nom || conflict.apt2?.patient || 'Patient 2' }}
                </small>
              </div>
              <div class="conflict-gap">
                Écart: {{ conflict.gapMinutes || 0 }} min
              </div>
            </div>
          </div>
          <button class="resolve-btn" @click="showConflictModal = true">
            <i class="fas fa-tools"></i>
            Résoudre les conflits
          </button>
        </div>
      </div>

      <!-- Prochain rendez-vous -->
      <div class="dashboard-card next-appointment" :class="{ 'past-appointment': !isNextAppointmentUpcoming }">
        <div class="card-header">
          <h3>
            <i class="fas fa-clock"></i>
            {{ isNextAppointmentUpcoming ? 'Prochain rendez-vous' : 'Dernier rendez-vous' }}
          </h3>
          <div class="card-badge" :class="isNextAppointmentUpcoming ? 'next' : 'past'" v-if="nextTime">
            <i :class="isNextAppointmentUpcoming ? 'fas fa-arrow-right' : 'fas fa-history'"></i>
          </div>
        </div>
        <div class="dashboard-value" :class="isNextAppointmentUpcoming ? 'accent' : 'muted'">
          {{ nextTime || 'Aucun' }}
        </div>
        <div class="dashboard-desc">
          <i :class="isNextAppointmentUpcoming ? 'fas fa-calendar-check' : 'fas fa-calendar-times'"></i>
          {{ nextAppointmentDetails || (isNextAppointmentUpcoming ? 'Aucun rendez-vous programmé' : 'Aucun rendez-vous récent') }}
        </div>
        <div class="appointment-status" v-if="nextTime">
          <span :class="isNextAppointmentUpcoming ? 'status-upcoming' : 'status-past'">
            <i :class="isNextAppointmentUpcoming ? 'fas fa-clock' : 'fas fa-check-circle'"></i>
            {{ isNextAppointmentUpcoming ? 'À venir' : 'Terminé' }}
          </span>
        </div>
        <div class="card-footer" v-if="nextTime">
          <button class="quick-action-btn" @click="viewNextAppointment">
            <i :class="isNextAppointmentUpcoming ? 'fas fa-arrow-right' : 'fas fa-eye'"></i>
            {{ isNextAppointmentUpcoming ? 'Voir détails' : 'Voir historique' }}
          </button>
        </div>
      </div>

      <!-- Patients du jour -->
      <div class="dashboard-card patients-today">
        <div class="card-header">
          <h3>
            <i class="fas fa-users"></i>
            Patients du jour
          </h3>
          <div class="card-badge patients">{{ uniquePatientsToday }}</div>
        </div>
        <div class="dashboard-value secondary">{{ uniquePatientsToday }}</div>
        <div class="dashboard-desc">
          <i class="fas fa-user-friends"></i>
          Patients uniques à recevoir aujourd'hui
        </div>
        <div class="card-footer">
          <button class="quick-action-btn" @click="viewPatients">
            <i class="fas fa-list"></i>
            Liste complète
          </button>
        </div>
      </div>

      <!-- Nouvelle carte: Gestion intelligente -->
      <div class="dashboard-card smart-management">
        <div class="card-header">
          <h3>
            <i class="fas fa-brain"></i>
            Gestion intelligente
          </h3>
          <div class="card-badge smart">{{ cleanupStats.totalCleaned }}</div>
        </div>
        <div class="dashboard-value info">{{ cleanupStats.totalCleaned }}</div>
        <div class="dashboard-desc">
          <i class="fas fa-broom"></i>
          Conflits nettoyés automatiquement
        </div>
        <div class="smart-stats">
          <div class="smart-stat">
            <span class="stat-label">Dernier nettoyage</span>
            <span class="stat-value">
              {{ lastCleanupTime ? lastCleanupTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'Jamais' }}
            </span>
          </div>
        </div>
        <div class="card-footer">
          <button class="quick-action-btn" @click="performAutoCleanup" :disabled="!autoCleanupEnabled">
            <i class="fas fa-broom"></i>
            Nettoyer maintenant
          </button>
          <button class="quick-action-btn secondary" @click="showTrendsModal">
            <i class="fas fa-chart-line"></i>
            Voir tendances
          </button>
        </div>
      </div>
    </div>

    <!-- Actions rapides modernes -->
    <div class="quick-actions">
      <div class="quick-actions-header">
        <div class="header-content">
          <h3>
            <i class="fas fa-bolt"></i>
            Actions Rapides
          </h3>
          <p class="subtitle">Accès direct aux fonctionnalités principales</p>
        </div>
        <div class="header-decoration">
          <div class="decoration-circle"></div>
          <div class="decoration-circle"></div>
          <div class="decoration-circle"></div>
        </div>
      </div>

      <div class="actions-grid">
        <!-- Nouveau RDV - Bleu -->
        <button class="action-card primary" @click="newAppointment" data-action="appointment">
          <div class="card-background"></div>
          <div class="card-icon">
            <i class="fas fa-plus-circle"></i>
          </div>
          <div class="card-content">
            <h4>Nouveau RDV</h4>
            <p>Planifier un rendez-vous</p>
          </div>
          <div class="card-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>
        </button>

        <!-- Calendrier - Vert -->
        <button class="action-card success" @click="viewCalendar" data-action="calendar">
          <div class="card-background"></div>
          <div class="card-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="card-content">
            <h4>Calendrier</h4>
            <p>Voir le planning</p>
          </div>
          <div class="card-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>
        </button>

        <!-- Patients - Orange -->
        <button class="action-card warning" @click="viewPatients" data-action="patients">
          <div class="card-background"></div>
          <div class="card-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="card-content">
            <h4>Patients</h4>
            <p>Gérer les dossiers</p>
          </div>
          <div class="card-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>
        </button>

        <!-- Analyses - Violet -->
        <button class="action-card info" @click="viewAnalytics" data-action="analytics">
          <div class="card-background"></div>
          <div class="card-icon">
            <i class="fas fa-chart-bar"></i>
          </div>
          <div class="card-content">
            <h4>Analyses</h4>
            <p>Rapports et stats</p>
          </div>
          <div class="card-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>
        </button>
      </div>
    </div>

    <!-- Modal de résolution des conflits -->
    <ConflictResolutionModal
      :show="showConflictModal"
      :conflicts="conflictDetails"
      @close="showConflictModal = false"
      @reschedule="handleReschedule"
      @extend="handleExtend"
      @contact="handleContact"
      @resolve-all="handleResolveAll"
    />
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useConflictStore } from '@/stores/conflictStore'
import { useAuthStore } from '@/stores/authStore'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import api from '@/services/api'
import conflictService from '@/services/conflictService'
import dashboardService from '@/services/dashboardService'
import ConflictResolutionModal from './ConflictResolutionModal.vue'

// Définir les événements émis
const emit = defineEmits([
  'view-today-appointments',
  'view-next-appointment',
  'view-patients',
  'new-appointment',
  'view-calendar',
  'view-analytics'
])

const store = useAppointmentStore()
const conflictStore = useConflictStore()
const authStore = useAuthStore()
const suggestedSlotStore = useSuggestedSlotStore()
const showConflictModal = ref(false)
const currentTime = ref('')

// Nouvelles variables pour les fonctionnalités étendues
const autoCleanupEnabled = ref(true)
const lastCleanupTime = ref(null)
const conflictTrends = ref(null)
const cleanupStats = ref({
  totalCleaned: 0,
  lastCleanup: null
})

// Nouvelles variables pour les vraies données
const realDashboardData = ref({
  statistics: {
    totalPatients: 0,
    appointmentsToday: 0,
    upcomingAppointments: 0,
    totalAppointments: 0,
    suggestedSlots: 0
  },
  todayAppointments: { count: 0, appointments: [] },
  nextAppointment: null,
  uniquePatientsToday: 0,
  lastUpdated: null
})
const dashboardLoading = ref(false)
const dashboardError = ref(null)

// Mettre à jour l'heure actuelle
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// Mettre à jour l'heure toutes les secondes
setInterval(updateCurrentTime, 1000)
updateCurrentTime()

// Nouvelles fonctions pour les fonctionnalités étendues
const performAutoCleanup = async () => {
  if (!autoCleanupEnabled.value) return

  try {
    const doctorId = authStore.user?.id || 1
    const result = await conflictService.autoCleanupByAppointmentTime(doctorId)

    if (result.status === 'success' && result.data.cleaned_conflicts > 0) {
      cleanupStats.value.totalCleaned += result.data.cleaned_conflicts
      cleanupStats.value.lastCleanup = new Date()
      lastCleanupTime.value = new Date()

      showNotification(`🧹 ${result.data.cleaned_conflicts} conflit(s) expiré(s) nettoyé(s)`, 'success')

      // Recharger les conflits après nettoyage
      await conflictStore.loadConflicts(doctorId)
    }
  } catch (error) {
    console.error('Erreur lors du nettoyage automatique:', error)
    showNotification('❌ Erreur lors du nettoyage automatique', 'error')
  }
}

const loadConflictTrends = async () => {
  try {
    const doctorId = authStore.user?.id || 1
    conflictTrends.value = await conflictService.analyzeConflictTrends(doctorId, 30)
    console.log('Tendances de conflits chargées:', conflictTrends.value)
  } catch (error) {
    console.error('Erreur lors du chargement des tendances:', error)
  }
}

const showTrendsModal = () => {
  if (conflictTrends.value && conflictTrends.value.data) {
    const trends = conflictTrends.value.data
    const recommendations = conflictService.getPreventiveRecommendations(trends)

    let message = '📈 TENDANCES DES CONFLITS (30 derniers jours)\n\n'

    if (trends.peak_conflict_hours) {
      message += `🕐 Heures de pointe: ${trends.peak_conflict_hours.join(', ')}\n`
    }

    if (trends.most_common_types) {
      message += `🔍 Types fréquents: ${trends.most_common_types.join(', ')}\n`
    }

    if (trends.conflict_frequency) {
      const days = Object.entries(trends.conflict_frequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
      message += `📅 Jours les plus chargés: ${days.map(([day, count]) => `${day} (${count})`).join(', ')}\n`
    }

    if (recommendations.length > 0) {
      message += '\n💡 RECOMMANDATIONS:\n'
      recommendations.forEach((rec, index) => {
        message += `${index + 1}. ${rec.message}\n`
      })
    }

    alert(message)
  } else {
    showNotification('⚠️ Aucune donnée de tendance disponible', 'warning')
  }
}

// Fonction pour charger les vraies données du tableau de bord
const loadRealDashboardData = async () => {
  dashboardLoading.value = true
  dashboardError.value = null

  try {
    console.log('🔄 Chargement des vraies données du tableau de bord...')

    // Récupérer l'ID du médecin
    let doctorId = null

    if (authStore.user && authStore.user.role === 'doctor') {
      try {
        const doctorResponse = await api.get(`/doctors/user/${authStore.user.id}`)
        if (doctorResponse.data.status === 'success' && doctorResponse.data.data) {
          doctorId = doctorResponse.data.data.id
          console.log('✅ ID médecin trouvé:', doctorId)
        }
      } catch (error) {
        console.warn('⚠️ Impossible de récupérer l\'ID médecin, utilisation de l\'ID utilisateur')
        doctorId = authStore.user.id
      }
    }

    if (!doctorId) {
      console.warn('⚠️ Aucun ID médecin trouvé, utilisation de l\'ID par défaut')
      doctorId = 1 // Fallback
    }

    // Charger toutes les données du tableau de bord
    const result = await dashboardService.getDashboardData(doctorId)

    if (result.status === 'success') {
      realDashboardData.value = result.data
      console.log('✅ Données réelles du tableau de bord chargées:', realDashboardData.value)
    } else {
      throw new Error(result.message || 'Erreur lors du chargement des données')
    }

  } catch (error) {
    console.error('❌ Erreur lors du chargement des données du tableau de bord:', error)
    dashboardError.value = error.message

    // Utiliser les données des stores comme fallback
    console.log('📝 Utilisation des données des stores comme fallback')
  } finally {
    dashboardLoading.value = false
  }
}

// Charge les rendez-vous au montage si tu utilises une API
onMounted(async () => {
  // Charger d'abord les vraies données du tableau de bord
  await loadRealDashboardData()

  if (authStore.user && authStore.user.role === 'doctor') {
    // Récupérer les rendez-vous spécifiques au médecin connecté
    try {
      console.log('Tentative de récupération du médecin pour user ID:', authStore.user.id)

      // D'abord récupérer l'ID du médecin depuis l'API
      const doctorResponse = await api.get(`/doctors/user/${authStore.user.id}`)
      console.log('Réponse API médecin:', doctorResponse.data)

      if (doctorResponse.data.status === 'success' && doctorResponse.data.data) {
        const doctorId = doctorResponse.data.data.id
        console.log('ID médecin trouvé:', doctorId)
        await Promise.all([
          store.fetchAppointmentsByDoctor(doctorId),
          conflictStore.loadConflicts(doctorId),
          conflictStore.loadStats(doctorId),
          suggestedSlotStore.loadSlotsByDoctor(doctorId)
        ])

        // Charger les nouvelles fonctionnalités
        await loadConflictTrends()

        // Effectuer le nettoyage automatique si activé
        if (autoCleanupEnabled.value) {
          await performAutoCleanup()
        }
      } else {
        console.log('Médecin non trouvé, chargement de tous les rendez-vous')
        // Fallback: charger tous les rendez-vous et filtrer côté client
        await Promise.all([
          store.fetchAppointments(),
          conflictStore.loadConflicts(),
          conflictStore.loadStats(),
          suggestedSlotStore.loadSlotsByDoctor(null)
        ])
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données médecin:', error)
      console.log('Fallback: chargement de tous les rendez-vous')
      // Fallback: charger tous les rendez-vous
      await store.fetchAppointments()
    }
  } else {
    console.log('Utilisateur non médecin, chargement de tous les rendez-vous')
    await store.fetchAppointments()
  }
})

const todayCount = computed(() => {
  // Utiliser les vraies données si disponibles, sinon fallback sur le store
  if (realDashboardData.value.todayAppointments.count > 0) {
    console.log('📊 Utilisation des vraies données - RDV aujourd\'hui:', realDashboardData.value.todayAppointments.count)
    return realDashboardData.value.todayAppointments.count
  }

  console.log('📝 Fallback sur le store - Appointments:', store.appointments)
  console.log('📝 Fallback sur le store - Today appointments:', store.todayAppointments)
  return store.todayAppointments.length
})

const conflictCount = computed(() => {
  console.log('Conflicts from conflictStore:', conflictStore.activeConflicts)
  return conflictStore.conflictCount
})

const nextTime = computed(() => {
  // Utiliser les vraies données si disponibles
  if (realDashboardData.value.nextAppointment) {
    const nextAppt = realDashboardData.value.nextAppointment
    console.log('📊 Utilisation des vraies données - Prochain RDV:', nextAppt)
    return dashboardService.formatAppointmentTime(nextAppt.date_rendez_vous || nextAppt.date)
  }

  // Fallback sur le store
  const nextAppt = store.nextAppointment
  console.log('📝 Fallback sur le store - Next appointment:', nextAppt)
  if (!nextAppt) return null
  const date = new Date(nextAppt.date_rendez_vous || nextAppt.date)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
})

// Détails du prochain rendez-vous
const nextAppointmentDetails = computed(() => {
  // Utiliser les vraies données si disponibles
  if (realDashboardData.value.nextAppointment) {
    const nextAppt = realDashboardData.value.nextAppointment
    return dashboardService.formatAppointmentDetails(nextAppt)
  }

  // Fallback sur le store
  const nextAppt = store.nextAppointment
  if (!nextAppt) return null
  return `${nextAppt.patient_nom || nextAppt.patient || 'Patient'} - ${nextAppt.type || 'Consultation'}`
})

// Vérifier si le prochain rendez-vous est à venir ou passé
const isNextAppointmentUpcoming = computed(() => {
  // Utiliser les vraies données si disponibles
  if (realDashboardData.value.nextAppointment && realDashboardData.value.nextAppointmentIsUpcoming !== undefined) {
    console.log('📊 Statut du prochain RDV (vraies données):', realDashboardData.value.nextAppointmentIsUpcoming)
    return realDashboardData.value.nextAppointmentIsUpcoming
  }

  // Fallback sur le store - vérifier manuellement
  const nextAppt = store.nextAppointment
  if (!nextAppt) return true // Par défaut, considérer comme à venir si pas de données

  const now = new Date()
  const aptDate = new Date(nextAppt.date_rendez_vous || nextAppt.date)
  const isUpcoming = aptDate > now

  console.log('📝 Statut du prochain RDV (fallback):', {
    appointmentDate: aptDate.toISOString(),
    currentTime: now.toISOString(),
    isUpcoming
  })

  return isUpcoming
})

// Nombre de patients uniques aujourd'hui
const uniquePatientsToday = computed(() => {
  // Utiliser les vraies données si disponibles
  if (realDashboardData.value.uniquePatientsToday > 0) {
    console.log('📊 Utilisation des vraies données - Patients uniques:', realDashboardData.value.uniquePatientsToday)
    return realDashboardData.value.uniquePatientsToday
  }

  // Fallback sur le store
  const todayAppointments = store.todayAppointments
  const uniquePatients = new Set()

  todayAppointments.forEach(apt => {
    if (apt.id_patient) {
      uniquePatients.add(apt.id_patient)
    }
  })

  console.log('📝 Fallback sur le store - Patients uniques calculés:', uniquePatients.size)
  return uniquePatients.size
})

// Utiliser les conflits depuis la base de données
const conflictDetails = computed(() => {
  console.log('Conflits depuis la base de données:', conflictStore.activeConflicts)
  return conflictStore.activeConflicts
})

// Méthodes utilitaires
const formatTime = (dateString) => {
  if (!dateString) {
    console.warn('formatTime: dateString est vide ou undefined')
    return 'Heure non définie'
  }

  const date = new Date(dateString)

  if (isNaN(date.getTime())) {
    console.warn('formatTime: Date invalide pour:', dateString)
    return 'Date invalide'
  }

  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// Méthodes de gestion des conflits
const handleReschedule = (data) => {
  console.log('Reprogrammer le rendez-vous:', data)

  if (data.action === 'reschedule') {
    // Simuler la reprogrammation
    showNotification(`Rendez-vous de ${data.appointment.patient_nom || 'Patient'} reprogrammé`, 'success')

    // TODO: Appeler l'API pour reprogrammer
    // await store.rescheduleAppointment(data.appointment.id, newDateTime)

    showConflictModal.value = false
  }
}

const handleExtend = (data) => {
  console.log('Prolonger le rendez-vous:', data)

  if (data.action === 'extend') {
    showNotification(`Durée du rendez-vous étendue à ${data.newDuration} minutes`, 'success')

    // TODO: Appeler l'API pour modifier la durée
    // await store.updateAppointmentDuration(data.appointment.id, data.newDuration)

    showConflictModal.value = false
  }
}

const handleContact = (data) => {
  console.log('Contacter le patient:', data)

  if (data.action === 'contact') {
    showNotification(`${data.method} - Contact initié avec ${data.appointment.patient_nom || 'Patient'}`, 'info')

    // TODO: Implémenter la logique de contact selon la méthode
    switch(data.method) {
      case 'Appeler le patient':
        // Ouvrir l'application téléphone ou afficher le numéro
        break
      case 'Envoyer un SMS':
        // Intégration SMS
        break
      case 'Envoyer un email':
        // Intégration email
        break
    }
  }
}

const handleResolveAll = (conflicts) => {
  console.log('Résolution automatique de tous les conflits:', conflicts)

  if (confirm(`Résoudre automatiquement ${conflicts.length} conflit(s) ?\n\nCela reprogrammera automatiquement les rendez-vous en conflit.`)) {
    // Simuler la résolution automatique
    conflicts.forEach((conflict, index) => {
      setTimeout(() => {
        showNotification(`Conflit ${index + 1}/${conflicts.length} résolu automatiquement`, 'success')
      }, index * 500)
    })

    setTimeout(() => {
      showNotification('Tous les conflits ont été résolus !', 'success')
      showConflictModal.value = false

      // TODO: Recharger les données
      // await store.fetchAppointments()
    }, conflicts.length * 500)
  }
}

// Fonction pour afficher les notifications
const showNotification = (message, type = 'info') => {
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'}"></i>
    ${message}
  `

  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'success' ? '#27ae60' : type === 'info' ? '#3498db' : '#e74c3c',
    color: 'white',
    padding: '1rem 1.5rem',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    zIndex: '9999',
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    fontSize: '0.9rem',
    fontWeight: '500',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease'
  })

  document.body.appendChild(notification)

  setTimeout(() => notification.style.transform = 'translateX(0)', 100)

  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => notification.remove(), 300)
  }, 3000)
}

// Méthodes pour les actions des boutons
const viewTodayAppointments = () => {
  console.log('Navigation vers les rendez-vous d\'aujourd\'hui')
  emit('view-today-appointments')
}

const viewNextAppointment = () => {
  console.log('Navigation vers le prochain rendez-vous')
  emit('view-next-appointment')
}

const viewPatients = () => {
  console.log('Navigation vers la liste des patients')
  emit('view-patients')
}

const newAppointment = () => {
  console.log('Création d\'un nouveau rendez-vous')
  emit('new-appointment')
}

const viewCalendar = () => {
  console.log('Navigation vers le calendrier')
  emit('view-calendar')
}

const viewAnalytics = () => {
  console.log('Navigation vers les analyses')
  emit('view-analytics')
}
</script>






<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header du dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.dashboard-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.dashboard-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.dashboard-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
}

/* Styles spécifiques pour chaque type de carte */
.dashboard-card.today-appointments::before {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.dashboard-card.next-appointment::before {
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.dashboard-card.past-appointment::before {
  background: linear-gradient(90deg, #95a5a6, #7f8c8d);
}

.dashboard-card.past-appointment {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid rgba(149, 165, 166, 0.2);
}

.dashboard-card.patients-today::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.dashboard-card.smart-management::before {
  background: linear-gradient(90deg, #17a2b8, #138496);
}

/* Headers des cartes */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.card-badge {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.card-badge.today {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.card-badge.next {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.card-badge.patients {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.card-badge.smart {
  background: linear-gradient(135deg, #17a2b8, #138496);
}

.card-badge.past {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

/* Statut du rendez-vous */
.appointment-status {
  margin: 1rem 0;
  display: flex;
  justify-content: center;
}

.status-upcoming {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
}

.status-past {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(149, 165, 166, 0.3);
}

.smart-stats {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(23, 162, 184, 0.1);
  border-radius: 8px;
  border-left: 4px solid #17a2b8;
}

.smart-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.smart-stat .stat-label {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.smart-stat .stat-value {
  font-size: 1rem;
  color: #17a2b8;
  font-weight: 600;
}

.dashboard-value.info {
  color: #17a2b8;
}

.quick-action-btn.secondary {
  background: #6c757d;
  color: white;
  margin-top: 0.5rem;
}

.quick-action-btn.secondary:hover {
  background: #5a6268;
}

/* Styles spécifiques pour les conflits */
.dashboard-card.conflict {
  border: 2px solid #ff6b6b;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
}

.dashboard-card.conflict::before {
  background: linear-gradient(90deg, #ff6b6b, #e74c3c);
}

.dashboard-card.conflict.has-conflicts {
  animation: pulse-warning 2s infinite;
  border-color: #e74c3c;
  box-shadow: 0 8px 32px rgba(231, 76, 60, 0.2);
}

@keyframes pulse-warning {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(231, 76, 60, 0.2);
  }
  50% {
    box-shadow: 0 8px 32px rgba(231, 76, 60, 0.4);
  }
}

/* Header des conflits */
.conflict-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.conflict-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.conflict-icon {
  color: #ff6b6b;
  font-size: 1.2rem;
  animation: shake 1s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.conflict-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Valeurs du dashboard */
.dashboard-value {
  font-size: 3rem;
  font-weight: 700;
  margin: 1.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-value.primary {
  color: #3498db;
}

.dashboard-value.secondary {
  color: #27ae60;
}

.dashboard-value.accent {
  color: #9b59b6;
}

.dashboard-value.muted {
  color: #95a5a6;
}

.dashboard-value.danger {
  color: #e74c3c;
  animation: pulse-number 1.5s infinite;
}

@keyframes pulse-number {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Footers des cartes */
.card-footer {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.quick-action-btn {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  width: 100%;
  justify-content: center;
}

.quick-action-btn:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Actions rapides modernes */
.quick-actions {
  background: white;
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  margin-top: 2rem;
  overflow: hidden;
  position: relative;
}

.quick-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #8b5cf6);
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #8b5cf6); }
  50% { background: linear-gradient(90deg, #8b5cf6, #3b82f6, #10b981, #f59e0b); }
}

.quick-actions-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  padding: 2rem;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions-header .header-content h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
}

.quick-actions-header .header-content h3 i {
  color: #3b82f6;
  font-size: 1.25rem;
  animation: pulse-icon 2s infinite;
}

@keyframes pulse-icon {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.subtitle {
  margin: 0;
  color: #64748b;
  font-size: 0.95rem;
  font-weight: 500;
}

.header-decoration {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.decoration-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: float 2s ease-in-out infinite;
}

.decoration-circle:nth-child(1) {
  background: #3b82f6;
  animation-delay: 0s;
}

.decoration-circle:nth-child(2) {
  background: #10b981;
  animation-delay: 0.3s;
}

.decoration-circle:nth-child(3) {
  background: #f59e0b;
  animation-delay: 0.6s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

/* Cartes d'action modernes */
.action-card {
  background: white;
  border: 2px solid transparent;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  text-align: left;
  color: #1e293b;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

.action-card.primary::before {
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
}

.action-card.success::before {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.action-card.warning::before {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.action-card.info::before {
  background: linear-gradient(90deg, #8b5cf6, #a78bfa);
}

.card-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  opacity: 0.05;
  transition: all 0.4s ease;
  transform: translate(30%, -30%);
}

.action-card.primary .card-background {
  background: radial-gradient(circle, #3b82f6, transparent);
}

.action-card.success .card-background {
  background: radial-gradient(circle, #10b981, transparent);
}

.action-card.warning .card-background {
  background: radial-gradient(circle, #f59e0b, transparent);
}

.action-card.info .card-background {
  background: radial-gradient(circle, #8b5cf6, transparent);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.action-card.primary .card-icon {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-card.success .card-icon {
  background: linear-gradient(135deg, #10b981, #34d399);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.action-card.warning .card-icon {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.action-card.info .card-icon {
  background: linear-gradient(135deg, #8b5cf6, #a78bfa);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.card-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.card-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
}

.card-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.card-arrow {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: #64748b;
  transition: all 0.3s ease;
  opacity: 0.6;
}

/* Hover effects */
.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.action-card:hover::before {
  height: 6px;
}

.action-card:hover .card-background {
  opacity: 0.1;
  transform: translate(20%, -20%) scale(1.2);
}

.action-card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
}

.action-card.primary:hover .card-arrow {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  opacity: 1;
}

.action-card.success:hover .card-arrow {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  opacity: 1;
}

.action-card.warning:hover .card-arrow {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  opacity: 1;
}

.action-card.info:hover .card-arrow {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  opacity: 1;
}

/* Descriptions */
.dashboard-desc {
  font-size: 1rem;
  color: #7f8c8d;
  margin-bottom: 1rem;
}

.no-conflict {
  color: #27ae60;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.has-conflict {
  color: #e74c3c;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Détails des conflits */
.conflict-details {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid rgba(231, 76, 60, 0.1);
}

.conflict-list {
  margin-bottom: 1rem;
}

.conflict-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.conflict-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(231, 76, 60, 0.4);
  transform: translateX(4px);
}

.conflict-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.conflict-time .time {
  background: linear-gradient(135deg, #ff6b6b, #e74c3c);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
}

.conflict-time .fas {
  color: #e74c3c;
  font-size: 1.1rem;
}

.conflict-patients {
  text-align: center;
  margin: 0.5rem 0;
  color: #7f8c8d;
  font-size: 0.8rem;
}

.conflict-gap {
  color: #e74c3c;
  font-weight: 600;
  font-size: 0.85rem;
  text-align: center;
}

/* Bouton de résolution */
.resolve-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.8rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  box-shadow: 0 4px 16px rgba(231, 76, 60, 0.3);
}

.resolve-btn:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.resolve-btn:active {
  transform: translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .dashboard-value {
    font-size: 2.5rem;
  }

  .conflict-time {
    flex-direction: column;
    gap: 0.5rem;
  }

  .quick-actions-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1.5rem;
  }

  .header-decoration {
    align-self: center;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    padding: 1.5rem;
    gap: 1rem;
  }

  .action-card {
    padding: 1.5rem;
  }

  .card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .card-content h4 {
    font-size: 1.1rem;
  }

  .card-content p {
    font-size: 0.85rem;
  }
}
</style>
