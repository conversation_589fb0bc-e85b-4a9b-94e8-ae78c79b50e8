
// utils/validators.js
export const validators = {
  email(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  },

  phone(phone) {
    const re = /^(?:\+33|0)[1-9](?:[0-9]{8})$/
    return re.test(phone.replace(/\s/g, ''))
  },

  required(value) {
    return value !== null && value !== undefined && value !== ''
  },

  minLength(value, min) {
    return value && value.length >= min
  },

  isValidAppointmentData(data) {
    const errors = []

    if (!this.required(data.doctor_id)) {
      errors.push('Médecin requis')
    }

    if (!this.required(data.patient_id)) {
      errors.push('Patient requis')
    }

    if (!this.required(data.appointment_date)) {
      errors.push('Date et heure requises')
    } else {
      const appointmentDate = new Date(data.appointment_date)
      if (appointmentDate < new Date()) {
        errors.push('La date ne peut pas être dans le passé')
      }
    }

    if (!data.duration || data.duration < 15) {
      errors.push('Durée minimum 15 minutes')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}