<template>
  <div class="slot-type-selector">
    <h3 class="selector-title">
      <i class="fas fa-clock"></i>
      <PERSON><PERSON><PERSON><PERSON> le type de consultation
    </h3>
    
    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Chargement des types de consultation...</p>
    </div>
    
    <div v-else-if="error" class="error-state">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button @click="loadSlotTypes" class="retry-btn">R<PERSON>sayer</button>
    </div>
    
    <div v-else class="types-grid">
      <div 
        v-for="type in slotTypes" 
        :key="type.id"
        :class="['type-card', { selected: selectedType?.id === type.id }]"
        @click="selectType(type)"
      >
        <div class="type-header">
          <div 
            class="type-color" 
            :style="{ backgroundColor: type.couleur }"
          ></div>
          <h4 class="type-name">{{ type.nom }}</h4>
          <div class="type-duration">{{ type.duree }} min</div>
        </div>
        
        <div class="type-content">
          <p class="type-description">{{ type.description }}</p>
          
          <div class="type-details">
            <div class="detail-item">
              <i class="fas fa-clock"></i>
              <span>Durée : {{ type.duree }} minutes</span>
            </div>
            
            <div class="detail-item">
              <i class="fas fa-coins"></i>
              <span>Tarif : {{ formatPriceXOF(type.prix_base) }}</span>
            </div>
          </div>
        </div>
        
        <div class="type-footer">
          <div v-if="selectedType?.id === type.id" class="selected-indicator">
            <i class="fas fa-check-circle"></i>
            <span>Sélectionné</span>
          </div>
          <div v-else class="select-prompt">
            <span>Cliquer pour sélectionner</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Résumé de la sélection -->
    <div v-if="selectedType" class="selection-summary">
      <div class="summary-header">
        <i class="fas fa-info-circle"></i>
        <h4>Résumé de votre sélection</h4>
      </div>
      
      <div class="summary-content">
        <div class="summary-item">
          <span class="label">Type :</span>
          <span class="value">{{ selectedType.nom }}</span>
        </div>
        
        <div class="summary-item">
          <span class="label">Durée :</span>
          <span class="value">{{ selectedType.duree }} minutes</span>
        </div>
        
        <div class="summary-item">
          <span class="label">Tarif :</span>
          <span class="value">{{ formatPriceXOF(selectedType.prix_base) }}</span>
        </div>
        
        <div class="summary-item">
          <span class="label">Description :</span>
          <span class="value">{{ selectedType.description }}</span>
        </div>
      </div>
    </div>
    
    <!-- Recommandations -->
    <div class="recommendations">
      <h4>
        <i class="fas fa-lightbulb"></i>
        Recommandations
      </h4>
      
      <div class="recommendation-list">
        <div class="recommendation-item">
          <div class="rec-icon court">
            <i class="fas fa-bolt"></i>
          </div>
          <div class="rec-content">
            <h5>Consultation Courte (15 min)</h5>
            <p>Idéale pour : Renouvellement d'ordonnance, résultats d'analyses, suivi simple</p>
          </div>
        </div>
        
        <div class="recommendation-item">
          <div class="rec-icon standard">
            <i class="fas fa-user-md"></i>
          </div>
          <div class="rec-content">
            <h5>Consultation Standard (30 min)</h5>
            <p>Idéale pour : Consultation générale, symptômes nouveaux, examen de routine</p>
          </div>
        </div>
        
        <div class="recommendation-item">
          <div class="rec-icon long">
            <i class="fas fa-search"></i>
          </div>
          <div class="rec-content">
            <h5>Consultation Longue (60 min)</h5>
            <p>Idéale pour : Premier rendez-vous, problème complexe, bilan complet</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { slotTypeService } from '@/services/slotTypeService'

export default {
  name: 'SlotTypeSelector',
  
  props: {
    modelValue: {
      type: Object,
      default: null
    }
  },
  
  emits: ['update:modelValue', 'type-selected'],
  
  setup(props, { emit }) {
    const slotTypes = ref([])
    const selectedType = ref(props.modelValue)
    const loading = ref(false)
    const error = ref(null)
    
    const loadSlotTypes = async () => {
      loading.value = true
      error.value = null
      
      try {
        const response = await slotTypeService.getActive()
        slotTypes.value = response.data
      } catch (err) {
        error.value = 'Erreur lors du chargement des types de consultation'
        console.error('Erreur chargement types:', err)
      } finally {
        loading.value = false
      }
    }
    
    const selectType = (type) => {
      selectedType.value = type
      emit('update:modelValue', type)
      emit('type-selected', type)
    }

    const formatPriceXOF = (price) => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price) + ' XOF'
    }

    onMounted(() => {
      loadSlotTypes()
    })

    return {
      slotTypes,
      selectedType,
      loading,
      error,
      loadSlotTypes,
      selectType,
      formatPriceXOF
    }
  }
}
</script>

<style scoped>
.slot-type-selector {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selector-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.selector-title i {
  color: #3b82f6;
}

.loading-state, .error-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.error-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #ef4444;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.type-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.type-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.type-card.selected {
  border-color: #3b82f6;
  background: #f0f9ff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.type-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.type-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.type-name {
  flex: 1;
  margin: 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.type-duration {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.type-description {
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.type-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  font-size: 0.875rem;
}

.detail-item i {
  color: #6b7280;
  width: 16px;
}

.type-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.selected-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
}

.select-prompt {
  color: #6b7280;
  font-size: 0.875rem;
}

.selection-summary {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.summary-header i {
  color: #3b82f6;
}

.summary-header h4 {
  margin: 0;
  color: #1f2937;
  font-size: 1rem;
}

.summary-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item .label {
  color: #6b7280;
  font-weight: 500;
}

.summary-item .value {
  color: #1f2937;
  font-weight: 600;
}

.recommendations {
  background: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
}

.recommendations h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1rem;
}

.recommendations h4 i {
  color: #f59e0b;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.rec-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rec-icon.court {
  background: #dcfce7;
  color: #10b981;
}

.rec-icon.standard {
  background: #dbeafe;
  color: #3b82f6;
}

.rec-icon.long {
  background: #fef3c7;
  color: #f59e0b;
}

.rec-content h5 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-size: 0.875rem;
  font-weight: 600;
}

.rec-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .slot-type-selector {
    padding: 1rem;
  }
  
  .types-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .summary-content {
    grid-template-columns: 1fr;
  }
  
  .recommendation-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>
