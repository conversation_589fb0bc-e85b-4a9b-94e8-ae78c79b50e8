import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // State
  const isLoggedIn = ref(false)
  const user = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const returnUrl = ref(null)

  // Getters
  const userRole = computed(() => user.value?.role)
  const isAdmin = computed(() => userRole.value === 'admin')
  const isDoctor = computed(() => userRole.value === 'doctor')
  const isPatient = computed(() => userRole.value === 'patient')
  const fullName = computed(() => user.value ? `${user.value.nom} ${user.value.prenom}` : '')

  // Actions
  const login = async (credentials) => {
    try {
      isLoading.value = true
      error.value = null
      
      console.log('Store: Tentative de connexion avec:', credentials.email)
      const response = await api.post('/auth/login', credentials)
      console.log('Store: Réponse du serveur:', response)
      
      const data = response.data
      
      if (!data || !data.user || !data.token) {
        console.error('Store: Données utilisateur invalides:', data)
        error.value = 'Données utilisateur invalides'
        return false
      }

      // Mise à jour du state
      user.value = data.user
      isLoggedIn.value = true
      
      console.log('Store: Utilisateur connecté:', user.value)
      console.log('Store: Role:', user.value.role)
      
      // Stockage sécurisé
      localStorage.setItem('authToken', data.token)
      localStorage.setItem('userData', JSON.stringify(data.user))

      // Configuration du token dans l'API
      api.defaults.headers.common['Authorization'] = `Bearer ${data.token}`
      
      return true
    } catch (err) {
      console.error('Store: Erreur de connexion:', err)
      error.value = err.response?.data?.message || err.message || 'Email ou mot de passe incorrect'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const logout = (redirect = true) => {
    console.log('Store: Déconnexion', redirect ? 'avec redirection' : 'silencieuse')
    isLoggedIn.value = false
    user.value = null
    error.value = null
    localStorage.removeItem('authToken')
    localStorage.removeItem('userData')
    sessionStorage.clear()
    delete api.defaults.headers.common['Authorization']

    if (redirect) {
      router.push('/login')
    }
  }

  const checkAuth = async () => {
    const token = localStorage.getItem('authToken')
    if (!token) {
      console.log('Store: Aucun token trouvé, pas de vérification nécessaire')
      return false
    }

    try {
      console.log('Store: Vérification du token existant...')
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      const response = await api.get('/auth/verify')

      if (response.data.user) {
        console.log('Store: Token valide, utilisateur restauré:', response.data.user.email)
        user.value = response.data.user
        isLoggedIn.value = true
        localStorage.setItem('userData', JSON.stringify(response.data.user))
        return true
      }
    } catch (err) {
      console.log('Store: Token invalide ou expiré, nettoyage silencieux')
      // Nettoyage silencieux sans redirection
      isLoggedIn.value = false
      user.value = null
      localStorage.removeItem('authToken')
      localStorage.removeItem('userData')
      delete api.defaults.headers.common['Authorization']
    }

    return false
  }

  // Initialisation automatique
  const initialize = async () => {
    console.log('Store: Initialisation de l\'authentification...')
    const token = localStorage.getItem('authToken')
    const userData = localStorage.getItem('userData')

    if (token && userData) {
      try {
        // Restaurer les données utilisateur depuis le localStorage
        const parsedUserData = JSON.parse(userData)
        user.value = parsedUserData
        isLoggedIn.value = true
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`

        console.log('Store: Session restaurée depuis le localStorage:', parsedUserData.email)

        // Vérification en arrière-plan (optionnelle et silencieuse)
        setTimeout(() => {
          checkAuth().catch(() => {
            console.log('Store: Vérification en arrière-plan échouée, mais pas de déconnexion forcée')
          })
        }, 1000)

      } catch (error) {
        console.log('Store: Erreur lors de la restauration, nettoyage')
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')
      }
    } else {
      console.log('Store: Aucun token ou données utilisateur, démarrage en mode non connecté')
    }
  }

  return {
    isLoggedIn,
    user,
    isLoading,
    error,
    returnUrl,
    userRole,
    isAdmin,
    isDoctor,
    isPatient,
    fullName,
    login,
    logout,
    checkAuth,
    initialize
  }
})