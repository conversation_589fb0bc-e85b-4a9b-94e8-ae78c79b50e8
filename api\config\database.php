<?php
class Database {
    private static $instance = null;
    private $conn;
    
    private $host = "localhost";
    private $db_name = "agenda_medical";
    private $username = "root";
    private $password = "";
    
    private function __construct() {
        try {
            error_log('Tentative de connexion à la base de données');
            error_log('Configuration: host=' . $this->host . ', db=' . $this->db_name . ', user=' . $this->username);
            
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
            error_log('Connexion à la base de données réussie');
        } catch(PDOException $e) {
            error_log("ERREUR DE CONNEXION: " . $e->getMessage());
            error_log("Code d'erreur: " . $e->getCode());
            throw new Exception("Erreur de connexion à la base de données: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance == null) {
            error_log('Création d\'une nouvelle instance de Database');
            self::$instance = new Database();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->conn;
    }
}

// Créer l'instance de la base de données et exposer la connexion PDO globalement
$database = Database::getInstance();
$GLOBALS['pdo'] = $database->getConnection();
?>