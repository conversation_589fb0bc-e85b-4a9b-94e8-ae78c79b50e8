-- Création de la table conflits pour enregistrer les conflits de planning
CREATE TABLE IF NOT EXISTS conflits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Rendez-vous en conflit
    rdv_1_id INT NOT NULL,
    rdv_2_id INT NOT NULL,
    
    -- Médecin concerné
    medecin_id INT NOT NULL,
    
    -- Type de conflit
    type_conflit ENUM('chevauchement', 'duree_insuffisante', 'hors_horaires', 'double_reservation') NOT NULL,
    
    -- Sévérité du conflit
    severite ENUM('faible', 'moyenne', 'elevee', 'critique') NOT NULL DEFAULT 'moyenne',
    
    -- Détails du conflit
    description TEXT,
    ecart_minutes INT, -- Écart en minutes (négatif pour chevauchement)
    
    -- Statut du conflit
    statut ENUM('actif', 'resolu', 'ignore', 'expire') NOT NULL DEFAULT 'actif',
    
    -- Résolution
    methode_resolution ENUM('reprogrammation', 'annulation', 'extension_duree', 'ignore', 'auto_expire') NULL,
    resolu_par INT NULL, -- ID de l'utilisateur qui a résolu
    resolu_le TIMESTAMP NULL,
    notes_resolution TEXT,
    
    -- Métadonnées
    detecte_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expire_le TIMESTAMP NULL, -- Calculé automatiquement quand les RDV sont passés
    
    -- Index et contraintes
    INDEX idx_rdv_1 (rdv_1_id),
    INDEX idx_rdv_2 (rdv_2_id),
    INDEX idx_medecin (medecin_id),
    INDEX idx_statut (statut),
    INDEX idx_severite (severite),
    INDEX idx_detecte_le (detecte_le),
    INDEX idx_expire_le (expire_le),
    
    -- Contraintes de clés étrangères
    FOREIGN KEY (rdv_1_id) REFERENCES rendez_vous(id) ON DELETE CASCADE,
    FOREIGN KEY (rdv_2_id) REFERENCES rendez_vous(id) ON DELETE CASCADE,
    FOREIGN KEY (medecin_id) REFERENCES medecins(id) ON DELETE CASCADE,
    FOREIGN KEY (resolu_par) REFERENCES utilisateur(id) ON DELETE SET NULL,
    
    -- Contrainte d'unicité pour éviter les doublons
    UNIQUE KEY unique_conflict (rdv_1_id, rdv_2_id, type_conflit)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Trigger pour calculer automatiquement l'expiration des conflits
DELIMITER $$

CREATE TRIGGER calculate_conflict_expiry 
BEFORE INSERT ON conflits
FOR EACH ROW
BEGIN
    DECLARE rdv1_end DATETIME;
    DECLARE rdv2_end DATETIME;
    DECLARE latest_end DATETIME;
    
    -- Calculer la fin du premier rendez-vous
    SELECT DATE_ADD(date_rendez_vous, INTERVAL duree MINUTE) 
    INTO rdv1_end 
    FROM rendez_vous 
    WHERE id = NEW.rdv_1_id;
    
    -- Calculer la fin du deuxième rendez-vous
    SELECT DATE_ADD(date_rendez_vous, INTERVAL duree MINUTE) 
    INTO rdv2_end 
    FROM rendez_vous 
    WHERE id = NEW.rdv_2_id;
    
    -- Prendre la fin la plus tardive
    SET latest_end = GREATEST(rdv1_end, rdv2_end);
    
    -- Définir l'expiration du conflit
    SET NEW.expire_le = latest_end;
END$$

DELIMITER ;

-- Trigger pour marquer automatiquement les conflits comme expirés
DELIMITER $$

CREATE EVENT IF NOT EXISTS expire_old_conflicts
ON SCHEDULE EVERY 5 MINUTE
DO
BEGIN
    UPDATE conflits 
    SET statut = 'expire',
        methode_resolution = 'auto_expire',
        resolu_le = NOW()
    WHERE statut = 'actif' 
    AND expire_le IS NOT NULL 
    AND expire_le < NOW();
END$$

DELIMITER ;

-- Activer l'event scheduler si ce n'est pas déjà fait
SET GLOBAL event_scheduler = ON;

-- Vue pour les conflits actifs avec détails des rendez-vous
CREATE OR REPLACE VIEW v_conflits_actifs AS
SELECT 
    c.id,
    c.type_conflit,
    c.severite,
    c.description,
    c.ecart_minutes,
    c.statut,
    c.detecte_le,
    c.expire_le,
    
    -- Détails du premier rendez-vous
    rdv1.id as rdv1_id,
    rdv1.date_rendez_vous as rdv1_date,
    rdv1.duree as rdv1_duree,
    rdv1.type as rdv1_type,
    CONCAT(p1.nom, ' ', p1.prenom) as rdv1_patient,
    
    -- Détails du deuxième rendez-vous
    rdv2.id as rdv2_id,
    rdv2.date_rendez_vous as rdv2_date,
    rdv2.duree as rdv2_duree,
    rdv2.type as rdv2_type,
    CONCAT(p2.nom, ' ', p2.prenom) as rdv2_patient,
    
    -- Détails du médecin
    CONCAT(m_user.nom, ' ', m_user.prenom) as medecin_nom,
    med.specialite
    
FROM conflits c
INNER JOIN rendez_vous rdv1 ON c.rdv_1_id = rdv1.id
INNER JOIN rendez_vous rdv2 ON c.rdv_2_id = rdv2.id
INNER JOIN patients pat1 ON rdv1.id_patient = pat1.id
INNER JOIN patients pat2 ON rdv2.id_patient = pat2.id
INNER JOIN utilisateur p1 ON pat1.user_id = p1.id
INNER JOIN utilisateur p2 ON pat2.user_id = p2.id
INNER JOIN medecins med ON c.medecin_id = med.id
INNER JOIN utilisateur m_user ON med.user_id = m_user.id
WHERE c.statut = 'actif'
ORDER BY c.severite DESC, c.detecte_le ASC;
