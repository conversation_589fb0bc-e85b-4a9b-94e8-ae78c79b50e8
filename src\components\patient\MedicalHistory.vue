<template>
  <div class="medical-history">
    <h2 class="section-title">
      <i class="fas fa-history"></i>
      Historique Médical
    </h2>
    <div class="history-content">
      <div v-if="loading" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Chargement de l'historique...
      </div>
      <div v-else-if="history.length === 0" class="empty-state">
        <i class="fas fa-notes-medical"></i>
        <p>Aucun historique médical disponible</p>
      </div>
      <div v-else class="history-list">
        <div v-for="item in history" :key="item.id" class="history-item">
          <div class="item-header">
            <span class="date">{{ formatDate(item.date) }}</span>
            <span :class="['status', item.status]">{{ item.status }}</span>
          </div>
          <h3>{{ item.title }}</h3>
          <p>{{ item.description }}</p>
          <div class="doctor-info">
            <i class="fas fa-user-md"></i>
            Dr. {{ item.doctorName }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loading = ref(true)
const history = ref([])

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

onMounted(async () => {
  try {
    // Simulation d'appel API
    await new Promise(resolve => setTimeout(resolve, 1000))
    history.value = [
      {
        id: 1,
        date: '2025-06-01',
        title: 'Consultation générale',
        description: 'Contrôle de routine annuel',
        status: 'terminé',
        doctorName: 'Martin'
      },
      // Autres entrées à ajouter plus tard
    ]
  } catch (error) {
    console.error('Erreur lors du chargement de l\'historique:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.medical-history {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e293b;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.section-title i {
  color: #3b82f6;
}

.loading, .empty-state {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.loading i, .empty-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  transition: transform 0.2s ease;
}

.history-item:hover {
  transform: translateY(-2px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.date {
  color: #64748b;
  font-size: 0.9rem;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.terminé {
  background: #dcfce7;
  color: #166534;
}

.status.en-cours {
  background: #fef3c7;
  color: #92400e;
}

.doctor-info {
  margin-top: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .medical-history {
    padding: 1rem;
  }
}
</style> 