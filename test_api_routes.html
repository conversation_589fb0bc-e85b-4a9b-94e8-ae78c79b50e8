<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Routes API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .success {
            background: #27ae60;
        }
        .error {
            background: #e74c3c;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 Test des Routes API - Agenda Médical</h1>
    
    <div class="test-container">
        <h2>📊 Routes des Conflits</h2>
        <button class="test-button" onclick="testRoute('/api/conflicts?medecin_id=1', 'GET', 'conflicts-list')">
            GET /api/conflicts
            <span id="status-conflicts-list" class="status"></span>
        </button>
        <button class="test-button" onclick="testRoute('/api/conflicts/stats?medecin_id=1', 'GET', 'conflicts-stats')">
            GET /api/conflicts/stats
            <span id="status-conflicts-stats" class="status"></span>
        </button>
        <button class="test-button" onclick="testRoute('/api/conflicts/trends?medecin_id=1&days=30', 'GET', 'conflicts-trends')">
            GET /api/conflicts/trends
            <span id="status-conflicts-trends" class="status"></span>
        </button>
        <button class="test-button" onclick="testRoute('/api/conflicts/auto-cleanup?medecin_id=1', 'DELETE', 'conflicts-cleanup')">
            DELETE /api/conflicts/auto-cleanup
            <span id="status-conflicts-cleanup" class="status"></span>
        </button>
        <div id="result-conflicts-list" class="result" style="display:none;"></div>
        <div id="result-conflicts-stats" class="result" style="display:none;"></div>
        <div id="result-conflicts-trends" class="result" style="display:none;"></div>
        <div id="result-conflicts-cleanup" class="result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <h2>🕒 Routes des Créneaux Suggérés</h2>
        <button class="test-button" onclick="testRoute('/api/suggested-slots?medecin_id=1', 'GET', 'slots-list')">
            GET /api/suggested-slots
            <span id="status-slots-list" class="status"></span>
        </button>
        <button class="test-button" onclick="testRoute('/api/suggested-slots/doctor/1', 'GET', 'slots-doctor')">
            GET /api/suggested-slots/doctor/1
            <span id="status-slots-doctor" class="status"></span>
        </button>
        <button class="test-button" onclick="testRoute('/api/suggested-slots/stats?medecin_id=1', 'GET', 'slots-stats')">
            GET /api/suggested-slots/stats
            <span id="status-slots-stats" class="status"></span>
        </button>
        <div id="result-slots-list" class="result" style="display:none;"></div>
        <div id="result-slots-doctor" class="result" style="display:none;"></div>
        <div id="result-slots-stats" class="result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <h2>📅 Routes des Rendez-vous</h2>
        <button class="test-button" onclick="testRoute('/api/appointments', 'GET', 'appointments-list')">
            GET /api/appointments
            <span id="status-appointments-list" class="status"></span>
        </button>
        <button class="test-button" onclick="testRoute('/api/appointments/doctor/1', 'GET', 'appointments-doctor')">
            GET /api/appointments/doctor/1
            <span id="status-appointments-doctor" class="status"></span>
        </button>
        <div id="result-appointments-list" class="result" style="display:none;"></div>
        <div id="result-appointments-doctor" class="result" style="display:none;"></div>
    </div>

    <div class="test-container">
        <h2>🎯 Test Global</h2>
        <button class="test-button" onclick="testAllRoutes()" style="background: #9b59b6;">
            🚀 Tester Toutes les Routes
        </button>
        <div id="global-summary" style="margin-top: 15px;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        async function testRoute(endpoint, method = 'GET', resultId) {
            const url = API_BASE + endpoint;
            const statusElement = document.getElementById(`status-${resultId}`);
            const resultElement = document.getElementById(`result-${resultId}`);
            
            try {
                statusElement.textContent = 'Testing...';
                statusElement.className = 'status';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.text();
                
                if (response.ok) {
                    statusElement.textContent = `✅ ${response.status}`;
                    statusElement.className = 'status success';
                    
                    try {
                        const jsonData = JSON.parse(data);
                        resultElement.textContent = JSON.stringify(jsonData, null, 2);
                    } catch (e) {
                        resultElement.textContent = data;
                    }
                } else {
                    statusElement.textContent = `❌ ${response.status}`;
                    statusElement.className = 'status error';
                    resultElement.textContent = `Error ${response.status}: ${data}`;
                }
                
                resultElement.style.display = 'block';
                
            } catch (error) {
                statusElement.textContent = `❌ Error`;
                statusElement.className = 'status error';
                resultElement.textContent = `Network Error: ${error.message}`;
                resultElement.style.display = 'block';
            }
        }
        
        async function testAllRoutes() {
            const routes = [
                ['/api/conflicts?medecin_id=1', 'GET', 'conflicts-list'],
                ['/api/conflicts/stats?medecin_id=1', 'GET', 'conflicts-stats'],
                ['/api/conflicts/trends?medecin_id=1&days=30', 'GET', 'conflicts-trends'],
                ['/api/suggested-slots?medecin_id=1', 'GET', 'slots-list'],
                ['/api/suggested-slots/doctor/1', 'GET', 'slots-doctor'],
                ['/api/appointments/doctor/1', 'GET', 'appointments-doctor']
            ];
            
            const summary = document.getElementById('global-summary');
            summary.innerHTML = '<p>🔄 Test en cours...</p>';
            
            let successCount = 0;
            let totalCount = routes.length;
            
            for (const [endpoint, method, resultId] of routes) {
                await testRoute(endpoint, method, resultId);
                const statusElement = document.getElementById(`status-${resultId}`);
                if (statusElement.textContent.includes('✅')) {
                    successCount++;
                }
                // Petite pause entre les tests
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            const successRate = Math.round((successCount / totalCount) * 100);
            summary.innerHTML = `
                <h3>📊 Résumé Global</h3>
                <p><strong>Routes testées:</strong> ${totalCount}</p>
                <p><strong>Succès:</strong> ${successCount}</p>
                <p><strong>Échecs:</strong> ${totalCount - successCount}</p>
                <p><strong>Taux de réussite:</strong> ${successRate}%</p>
                <div style="background: ${successRate >= 80 ? '#d4edda' : '#f8d7da'}; padding: 10px; border-radius: 4px; margin-top: 10px;">
                    ${successRate >= 80 ? '🎉 API fonctionnelle !' : '⚠️ Problèmes détectés'}
                </div>
            `;
        }
        
        // Test automatique au chargement
        window.addEventListener('load', () => {
            console.log('🔧 Page de test des routes API chargée');
            console.log('Cliquez sur les boutons pour tester les routes individuellement');
            console.log('Ou utilisez "Tester Toutes les Routes" pour un test global');
        });
    </script>
</body>
</html>
