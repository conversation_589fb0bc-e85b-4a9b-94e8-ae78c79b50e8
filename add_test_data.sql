-- Script SQL pour ajouter des données de test à l'agenda médical
-- Exécutez ce script si vous voulez ajouter des données de test directement

-- Ajouter des médecins de test (seulement s'ils n'existent pas)
INSERT IGNORE INTO utilisateur (nom, prenom, email, mot_de_passe, role, specialite, telephone, adresse, date_creation)
VALUES 
('<PERSON><PERSON>', '<PERSON>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'medecin', 'Cardiologie', '**********', '123 Rue de l\'Hôpital', NOW()),
('<PERSON>', '<PERSON>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'medecin', 'Dermatologie', '**********', '123 Rue de l\'Hôpital', NOW()),
('<PERSON>', '<PERSON>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'medecin', 'Médecine générale', '**********', '123 Rue de l\'Hôpital', NOW());

-- Ajouter des patients de test (seulement s'ils n'existent pas)
INSERT IGNORE INTO utilisateur (nom, prenom, email, mot_de_passe, role, telephone, adresse, date_creation)
VALUES 
('Durand', 'Sophie', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'patient', '**********', '456 Rue des Patients', NOW()),
('Moreau', 'Paul', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'patient', '**********', '456 Rue des Patients', NOW()),
('Petit', 'Claire', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'patient', '**********', '456 Rue des Patients', NOW()),
('Roux', 'Michel', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'patient', '**********', '456 Rue des Patients', NOW());

-- Ajouter des rendez-vous de test
INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, type, statut, duree, notes, cree_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 1 DAY) + INTERVAL 9 HOUR as date_rendez_vous,
    'consultation' as type,
    'confirme' as statut,
    30 as duree,
    'Rendez-vous de test' as notes,
    NOW() as cree_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM rendez_vous rv 
    WHERE rv.id_medecin = m.id AND rv.id_patient = p.id
  );

INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, type, statut, duree, notes, cree_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 1 DAY) + INTERVAL 10 HOUR + INTERVAL 30 MINUTE as date_rendez_vous,
    'controle' as type,
    'confirme' as statut,
    30 as duree,
    'Rendez-vous de test' as notes,
    NOW() as cree_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM rendez_vous rv 
    WHERE rv.id_medecin = m.id AND rv.id_patient = p.id
  );

INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, type, statut, duree, notes, cree_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 2 DAY) + INTERVAL 14 HOUR as date_rendez_vous,
    'consultation' as type,
    'en_attente' as statut,
    30 as duree,
    'Rendez-vous de test' as notes,
    NOW() as cree_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM rendez_vous rv 
    WHERE rv.id_medecin = m.id AND rv.id_patient = p.id
  );

INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, type, statut, duree, notes, cree_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 3 DAY) + INTERVAL 16 HOUR as date_rendez_vous,
    'urgence' as type,
    'confirme' as statut,
    30 as duree,
    'Rendez-vous de test' as notes,
    NOW() as cree_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM rendez_vous rv 
    WHERE rv.id_medecin = m.id AND rv.id_patient = p.id
  );

-- Ajouter des créneaux suggérés de test (si la table existe)
INSERT INTO creneaux_suggeres (id_medecin, id_patient, date_heure_suggeree, duree, score_confiance, raison, accepte, cree_le, expire_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 4 DAY) + INTERVAL 9 HOUR as date_heure_suggeree,
    30 as duree,
    0.8 as score_confiance,
    'Créneau disponible' as raison,
    NULL as accepte,
    NOW() as cree_le,
    DATE_ADD(NOW(), INTERVAL 7 DAY) as expire_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres')
  AND NOT EXISTS (
    SELECT 1 FROM creneaux_suggeres cs 
    WHERE cs.id_medecin = m.id AND cs.id_patient = p.id
  );

INSERT INTO creneaux_suggeres (id_medecin, id_patient, date_heure_suggeree, duree, score_confiance, raison, accepte, cree_le, expire_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 4 DAY) + INTERVAL 11 HOUR as date_heure_suggeree,
    45 as duree,
    0.8 as score_confiance,
    'Consultation de suivi' as raison,
    NULL as accepte,
    NOW() as cree_le,
    DATE_ADD(NOW(), INTERVAL 7 DAY) as expire_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres')
  AND NOT EXISTS (
    SELECT 1 FROM creneaux_suggeres cs 
    WHERE cs.id_medecin = m.id AND cs.id_patient = p.id
  );

INSERT INTO creneaux_suggeres (id_medecin, id_patient, date_heure_suggeree, duree, score_confiance, raison, accepte, cree_le, expire_le)
SELECT 
    m.id as id_medecin,
    p.id as id_patient,
    DATE_ADD(NOW(), INTERVAL 5 DAY) + INTERVAL 15 HOUR as date_heure_suggeree,
    30 as duree,
    0.8 as score_confiance,
    'Nouveau patient' as raison,
    NULL as accepte,
    NOW() as cree_le,
    DATE_ADD(NOW(), INTERVAL 7 DAY) as expire_le
FROM utilisateur m, utilisateur p 
WHERE m.role = 'medecin' AND m.email = '<EMAIL>'
  AND p.role = 'patient' AND p.email = '<EMAIL>'
  AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres')
  AND NOT EXISTS (
    SELECT 1 FROM creneaux_suggeres cs 
    WHERE cs.id_medecin = m.id AND cs.id_patient = p.id
  );

-- Afficher un résumé des données ajoutées
SELECT 'RÉSUMÉ DES DONNÉES AJOUTÉES' as message;
SELECT 'Médecins:' as type, COUNT(*) as count FROM utilisateur WHERE role = 'medecin';
SELECT 'Patients:' as type, COUNT(*) as count FROM utilisateur WHERE role = 'patient';
SELECT 'Rendez-vous:' as type, COUNT(*) as count FROM rendez_vous;
SELECT 'Créneaux suggérés:' as type, COUNT(*) as count FROM creneaux_suggeres WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres');

-- Afficher quelques exemples de rendez-vous
SELECT 
    'EXEMPLES DE RENDEZ-VOUS' as message,
    rv.date_rendez_vous,
    CONCAT(p.prenom, ' ', p.nom) as patient,
    CONCAT('Dr. ', m.prenom, ' ', m.nom) as medecin,
    rv.type,
    rv.statut
FROM rendez_vous rv
JOIN utilisateur p ON rv.id_patient = p.id
JOIN utilisateur m ON rv.id_medecin = m.id
ORDER BY rv.date_rendez_vous
LIMIT 5;
