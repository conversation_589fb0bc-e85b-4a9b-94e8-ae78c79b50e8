<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <i class="fas fa-clinic-medical"></i>
        <h1>Connexion</h1>
        <p>Bienvenue sur votre espace médical</p>
      </div>

      <form @submit.prevent="handleLogin" class="auth-form">
        <div class="form-group">
          <label for="email">
            <i class="fas fa-envelope"></i>
            Email
          </label>
          <input
            id="email"
            v-model="form.email"
            type="email"
            required
            placeholder="<EMAIL>"
            :class="{ 'error': errors.email }"
          >
          <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
        </div>

        <div class="form-group">
          <label for="password">
            <i class="fas fa-lock"></i>
            Mot de passe
          </label>
          <div class="password-input">
            <input
              id="password"
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              required
              placeholder="Votre mot de passe"
              :class="{ 'error': errors.password }"
            >
            <button 
              type="button"
              class="toggle-password"
              @click="showPassword = !showPassword"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
        </div>

        <div class="form-options">
          <label class="remember-me">
            <input type="checkbox" v-model="form.remember">
            <span>Se souvenir de moi</span>
          </label>
          <router-link to="/forgot-password" class="forgot-password">
            Mot de passe oublié ?
          </router-link>
        </div>

        <button type="submit" class="submit-button" :disabled="isLoading">
          <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
          <span v-else>Se connecter</span>
        </button>

        <div class="auth-footer">
          <p>Vous n'avez pas de compte ?</p>
          <router-link to="/register" class="register-link">
            Créer un compte
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
  email: '',
  password: '',
  remember: false
})

const errors = reactive({
  email: '',
  password: ''
})

const isLoading = ref(false)
const showPassword = ref(false)

const handleLogin = async () => {
  // Réinitialiser les erreurs
  errors.email = ''
  errors.password = ''

  // Validation basique
  if (!form.email) {
    errors.email = 'L\'email est requis'
    return
  }
  if (!form.password) {
    errors.password = 'Le mot de passe est requis'
    return
  }

  try {
    console.log('Tentative de connexion avec:', { email: form.email })
    isLoading.value = true

    const success = await authStore.login({
      email: form.email,
      password: form.password
    })

    console.log('Résultat de la connexion:', {
      success,
      isLoggedIn: authStore.isLoggedIn,
      user: authStore.user,
      userRole: authStore.userRole,
      error: authStore.error
    })

    if (success) {
      console.log('Connexion réussie, redirection...')
      const role = authStore.userRole
      let redirectPath = '/'

      switch(role) {
        case 'doctor':
          redirectPath = '/doctor'
          break
        case 'patient':
          redirectPath = '/patient'
          break
        case 'admin':
          redirectPath = '/admin'
          break
        default:
          console.error('Role non reconnu:', role)
          errors.password = 'Rôle utilisateur non reconnu'
          return
      }

      console.log('Redirection vers:', redirectPath)
      await router.push(redirectPath)
    } else {
      // Utiliser le message d'erreur du store s'il existe
      errors.password = authStore.error || 'Email ou mot de passe incorrect'
    }
  } catch (err) {
    console.error('Erreur lors de la connexion:', err)
    // Utiliser le message d'erreur de l'erreur si disponible
    errors.password = err.message || "Une erreur s'est produite lors de la connexion."
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f6f8fc 0%, #e9effd 100%);
  padding: 1rem;
}

.auth-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header i {
  font-size: 2.5rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.auth-header h1 {
  font-size: 1.75rem;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.auth-header p {
  color: #64748b;
  font-size: 0.95rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #475569;
  font-size: 0.95rem;
}

.form-group label i {
  color: #64748b;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  color: #1e293b;
  transition: all 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error {
  border-color: #ef4444;
}

.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 0;
}

.error-message {
  color: #ef4444;
  font-size: 0.85rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #475569;
  cursor: pointer;
}

.forgot-password {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: #2563eb;
}

.submit-button {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.auth-footer {
  text-align: center;
  margin-top: 1rem;
  color: #64748b;
}

.register-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.5rem;
  transition: color 0.2s ease;
}

.register-link:hover {
  color: #2563eb;
}

@media (max-width: 640px) {
  .auth-card {
    padding: 1.5rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
</style> 