<template>
  <div class="conflicts-container">
    <div class="conflicts-header">
      <h3>
        <i class="fas fa-exclamation-triangle"></i>
        Conflits de Planning
      </h3>
      <div class="header-actions">
        <span class="conflict-count">{{ conflicts.length }}</span>
        <button
          v-if="autoCleanupEnabled"
          @click="performAutoCleanup"
          class="cleanup-btn"
          title="Nettoyage automatique"
        >
          <i class="fas fa-broom"></i>
        </button>
      </div>
    </div>

    <!-- Statistiques rapides -->
    <div v-if="conflictStats?.data" class="conflict-stats">
      <div class="stat-item">
        <span class="stat-value">{{ conflictStats.data.critical_conflicts }}</span>
        <span class="stat-label">Critiques</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">{{ conflictStats.data.time_lost_minutes }}</span>
        <span class="stat-label">Min perdues</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">{{ conflictStats.data.resolved_conflicts }}</span>
        <span class="stat-label">Résolus</span>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Chargement des conflits...</p>
    </div>

    <div v-else-if="conflicts.length === 0" class="no-conflicts">
      <i class="fas fa-check-circle"></i>
      <p>Aucun conflit détecté</p>

      <!-- Recommandations préventives -->
      <div v-if="preventiveRecommendations.length > 0" class="preventive-recommendations">
        <h4><i class="fas fa-lightbulb"></i> Recommandations préventives</h4>
        <div class="recommendations-list">
          <div
            v-for="(rec, index) in preventiveRecommendations"
            :key="index"
            class="recommendation-item"
            :class="rec.priority"
          >
            <i class="fas fa-info-circle"></i>
            <span>{{ rec.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="conflicts-list">
      <div
        v-for="conflict in conflicts"
        :key="conflict.id"
        :class="['conflict-item', `severity-${conflict.severite || conflict.severity || 'moyenne'}`]"
      >
        <div class="conflict-header">
          <div class="conflict-time">
            <i class="fas fa-clock"></i>
            {{ formatDateTime(conflict.rdv1_date || conflict.detecte_le || conflict.date) }}
          </div>
          <div class="conflict-severity">
            <span :class="['severity-badge', conflict.severite || conflict.severity || 'moyenne']">
              {{ conflict.severite || conflict.severity || 'moyenne' }}
            </span>
          </div>
        </div>

        <div class="conflict-details">
          <div class="conflict-reason">
            {{ getConflictReason(conflict) }}
          </div>

          <div v-if="conflict.rdv1_patient && conflict.rdv2_patient" class="appointments-info">
            <div class="appointment-item">
              <strong>RDV 1:</strong> {{ conflict.rdv1_patient }}
              <span class="time">{{ formatDateTime(conflict.rdv1_date) }}</span>
            </div>
            <div class="appointment-item">
              <strong>RDV 2:</strong> {{ conflict.rdv2_patient }}
              <span class="time">{{ formatDateTime(conflict.rdv2_date) }}</span>
            </div>
          </div>

          <div v-if="conflict.ecart_minutes" class="gap-info">
            <i class="fas fa-exclamation-triangle"></i>
            Écart: {{ Math.abs(conflict.ecart_minutes) }} minutes
          </div>
        </div>

        <div class="conflict-actions">
          <button
            @click="resolveConflict(conflict.id, 'reschedule')"
            class="action-btn reschedule"
          >
            <i class="fas fa-calendar-alt"></i>
            Reprogrammer
          </button>

          <button
            @click="resolveConflict(conflict.id, 'cancel')"
            class="action-btn cancel"
          >
            <i class="fas fa-times"></i>
            Annuler
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useConflictStore } from '@/stores/conflictStore'
import { useAuthStore } from '@/stores/authStore'
import conflictService from '@/services/conflictService'

const conflictStore = useConflictStore()
const authStore = useAuthStore()

const conflicts = computed(() => conflictStore.activeConflicts)
const loading = computed(() => conflictStore.loading)

// Nouvelles variables réactives
const autoCleanupEnabled = ref(true)
const lastCleanupTime = ref(null)
const conflictStats = ref(null)
const preventiveRecommendations = ref([])

// Nouvelles fonctions pour les fonctionnalités étendues
async function performAutoCleanup() {
  try {
    const doctorId = authStore.user?.id || 1
    const result = await conflictService.autoCleanupByAppointmentTime(doctorId)

    if (result.status === 'success' && result.data.cleaned_conflicts > 0) {
      console.log(`${result.data.cleaned_conflicts} conflit(s) nettoyé(s)`)
      lastCleanupTime.value = new Date()

      // Recharger les conflits après nettoyage
      await conflictStore.loadConflicts(doctorId)
    }
  } catch (error) {
    console.error('Erreur lors du nettoyage automatique:', error)
  }
}

async function loadConflictStats() {
  try {
    const doctorId = authStore.user?.id || 1
    conflictStats.value = await conflictService.getConflictStats(doctorId)
  } catch (error) {
    console.error('Erreur lors du chargement des statistiques:', error)
  }
}

async function loadPreventiveRecommendations() {
  try {
    const doctorId = authStore.user?.id || 1
    const trends = await conflictService.analyzeConflictTrends(doctorId, 7) // 7 derniers jours

    if (trends.status === 'success') {
      preventiveRecommendations.value = conflictService.getPreventiveRecommendations(trends.data)
    }
  } catch (error) {
    console.error('Erreur lors du chargement des recommandations:', error)
  }
}

// Charger les conflits au montage avec nouvelles fonctionnalités
onMounted(async () => {
  try {
    // Récupérer l'ID du médecin connecté
    const doctorId = authStore.user?.id || 1 // Fallback pour les tests

    // Charger les conflits
    await conflictStore.loadConflicts(doctorId)

    // Effectuer le nettoyage automatique si activé
    if (autoCleanupEnabled.value) {
      await performAutoCleanup()
    }

    // Charger les statistiques
    await loadConflictStats()

    // Charger les recommandations préventives
    await loadPreventiveRecommendations()

  } catch (error) {
    console.error('Erreur lors du chargement des conflits:', error)
  }
})

function formatDateTime(date) {
  const options = {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    hour: '2-digit',
    minute: '2-digit'
  }
  return new Date(date).toLocaleDateString('fr-FR', options)
}

function getConflictReason(conflict) {
  // Utiliser la description de la base de données ou mapper les types
  if (conflict.description) {
    return conflict.description
  }

  const reasons = {
    'chevauchement': 'Chevauchement avec un autre rendez-vous',
    'duree_insuffisante': 'Durée insuffisante entre les rendez-vous',
    'hors_horaires': 'En dehors des heures de consultation',
    'double_reservation': 'Double réservation détectée'
  }
  return reasons[conflict.type_conflit] || 'Conflit de planning'
}

async function resolveConflict(conflictId, action) {
  try {
    const resolutionData = {
      resolu_par: authStore.user?.id,
      timestamp: new Date().toISOString()
    }

    if (action === 'reschedule') {
      // Utiliser le nouveau service pour résoudre le conflit
      resolutionData.methode = 'reprogrammation'
      resolutionData.notes = 'Résolu via reprogrammation depuis ConflictAlerts'

      await conflictService.resolveConflict(conflictId, resolutionData)

    } else if (action === 'cancel') {
      // Utiliser le nouveau service pour résoudre le conflit
      resolutionData.methode = 'annulation'
      resolutionData.notes = 'Résolu via annulation depuis ConflictAlerts'

      await conflictService.resolveConflict(conflictId, resolutionData)
    }

    // Recharger les conflits après résolution
    const doctorId = authStore.user?.id || 1
    await conflictStore.loadConflicts(doctorId)

    // Recharger les statistiques
    await loadConflictStats()

  } catch (error) {
    console.error('Erreur lors de la résolution du conflit:', error)
  }
}
</script>

<style scoped>
.conflicts-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.conflicts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cleanup-btn {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cleanup-btn:hover {
  background: #2980b9;
  transform: scale(1.1);
}

.conflict-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  text-transform: uppercase;
}

.preventive-recommendations {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #e8f5e8;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
}

.preventive-recommendations h4 {
  margin: 0 0 1rem 0;
  color: #27ae60;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border-left: 3px solid #27ae60;
}

.recommendation-item.high {
  border-left-color: #e74c3c;
}

.recommendation-item.medium {
  border-left-color: #f39c12;
}

.conflicts-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e74c3c;
  margin: 0;
}

.conflicts-header i {
  font-size: 1.25rem;
}

.conflict-count {
  background: #e74c3c;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-weight: 500;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conflict-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  background: #fef2f2;
}

.conflict-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.conflict-details {
  margin-bottom: 1rem;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.appointment-type {
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #64748b;
}

.conflict-reason {
  font-size: 0.875rem;
  color: #ef4444;
}

.conflict-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn.reschedule {
  background: #3b82f6;
  color: white;
}

.action-btn.reschedule:hover {
  background: #2563eb;
}

.action-btn.cancel {
  background: #ef4444;
  color: white;
}

.action-btn.cancel:hover {
  background: #dc2626;
}

/* Nouveaux styles pour les données de la DB */
.loading-state,
.no-conflicts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #64748b;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.no-conflicts i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #10b981;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.severity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-badge.faible {
  background: #dcfce7;
  color: #166534;
}

.severity-badge.moyenne {
  background: #fef3c7;
  color: #92400e;
}

.severity-badge.elevee {
  background: #fed7aa;
  color: #c2410c;
}

.severity-badge.critique {
  background: #fecaca;
  color: #dc2626;
}

.appointments-info {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
}

.appointment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.appointment-item:last-child {
  margin-bottom: 0;
}

.appointment-item .time {
  font-size: 0.875rem;
  color: #64748b;
}

.gap-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #fef2f2;
  border-radius: 4px;
  color: #dc2626;
  font-size: 0.875rem;
}

.severity-faible .conflict-item {
  border-left: 4px solid #10b981;
}

.severity-moyenne .conflict-item {
  border-left: 4px solid #f59e0b;
}

.severity-elevee .conflict-item {
  border-left: 4px solid #f97316;
}

.severity-critique .conflict-item {
  border-left: 4px solid #ef4444;
}

@media (max-width: 768px) {
  .conflict-item {
    padding: 0.75rem;
  }

  .conflict-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
