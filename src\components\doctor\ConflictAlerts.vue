<template>
  <div class="conflicts-container">
    <div class="conflicts-header">
      <h3>
        <i class="fas fa-exclamation-triangle"></i>
        Conflits de Planning
      </h3>
      <span class="conflict-count">{{ conflicts.length }}</span>
    </div>

    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Chargement des conflits...</p>
    </div>

    <div v-else-if="conflicts.length === 0" class="no-conflicts">
      <i class="fas fa-check-circle"></i>
      <p>Aucun conflit détecté</p>
    </div>

    <div v-else class="conflicts-list">
      <div
        v-for="conflict in conflicts"
        :key="conflict.id"
        :class="['conflict-item', `severity-${conflict.severite || conflict.severity || 'moyenne'}`]"
      >
        <div class="conflict-header">
          <div class="conflict-time">
            <i class="fas fa-clock"></i>
            {{ formatDateTime(conflict.rdv1_date || conflict.detecte_le || conflict.date) }}
          </div>
          <div class="conflict-severity">
            <span :class="['severity-badge', conflict.severite || conflict.severity || 'moyenne']">
              {{ conflict.severite || conflict.severity || 'moyenne' }}
            </span>
          </div>
        </div>

        <div class="conflict-details">
          <div class="conflict-reason">
            {{ getConflictReason(conflict) }}
          </div>

          <div v-if="conflict.rdv1_patient && conflict.rdv2_patient" class="appointments-info">
            <div class="appointment-item">
              <strong>RDV 1:</strong> {{ conflict.rdv1_patient }}
              <span class="time">{{ formatDateTime(conflict.rdv1_date) }}</span>
            </div>
            <div class="appointment-item">
              <strong>RDV 2:</strong> {{ conflict.rdv2_patient }}
              <span class="time">{{ formatDateTime(conflict.rdv2_date) }}</span>
            </div>
          </div>

          <div v-if="conflict.ecart_minutes" class="gap-info">
            <i class="fas fa-exclamation-triangle"></i>
            Écart: {{ Math.abs(conflict.ecart_minutes) }} minutes
          </div>
        </div>

        <div class="conflict-actions">
          <button
            @click="resolveConflict(conflict.id, 'reschedule')"
            class="action-btn reschedule"
          >
            <i class="fas fa-calendar-alt"></i>
            Reprogrammer
          </button>

          <button
            @click="resolveConflict(conflict.id, 'cancel')"
            class="action-btn cancel"
          >
            <i class="fas fa-times"></i>
            Annuler
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useConflictStore } from '@/stores/conflictStore'
import { useAuthStore } from '@/stores/authStore'

const conflictStore = useConflictStore()
const authStore = useAuthStore()

const conflicts = computed(() => conflictStore.activeConflicts)
const loading = computed(() => conflictStore.loading)

// Charger les conflits au montage
onMounted(async () => {
  try {
    // Récupérer l'ID du médecin connecté
    const doctorId = authStore.user?.id || 1 // Fallback pour les tests
    await conflictStore.loadConflicts(doctorId)
  } catch (error) {
    console.error('Erreur lors du chargement des conflits:', error)
  }
})

function formatDateTime(date) {
  const options = {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    hour: '2-digit',
    minute: '2-digit'
  }
  return new Date(date).toLocaleDateString('fr-FR', options)
}

function getConflictReason(conflict) {
  // Utiliser la description de la base de données ou mapper les types
  if (conflict.description) {
    return conflict.description
  }

  const reasons = {
    'chevauchement': 'Chevauchement avec un autre rendez-vous',
    'duree_insuffisante': 'Durée insuffisante entre les rendez-vous',
    'hors_horaires': 'En dehors des heures de consultation',
    'double_reservation': 'Double réservation détectée'
  }
  return reasons[conflict.type_conflit] || 'Conflit de planning'
}

async function resolveConflict(conflictId, action) {
  try {
    if (action === 'reschedule') {
      // Résoudre le conflit avec la méthode reprogrammation
      await conflictStore.resolveConflict(conflictId, {
        methode: 'reprogrammation',
        resolu_par: authStore.user?.id,
        notes: 'Résolu via reprogrammation depuis ConflictAlerts'
      })

      // Recharger les conflits
      const doctorId = authStore.user?.id || 1
      await conflictStore.loadConflicts(doctorId)

    } else if (action === 'cancel') {
      // Résoudre le conflit avec la méthode annulation
      await conflictStore.resolveConflict(conflictId, {
        methode: 'annulation',
        resolu_par: authStore.user?.id,
        notes: 'Résolu via annulation depuis ConflictAlerts'
      })

      // Recharger les conflits
      const doctorId = authStore.user?.id || 1
      await conflictStore.loadConflicts(doctorId)
    }
  } catch (error) {
    console.error('Erreur lors de la résolution du conflit:', error)
  }
}
</script>

<style scoped>
.conflicts-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.conflicts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.conflicts-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e74c3c;
  margin: 0;
}

.conflicts-header i {
  font-size: 1.25rem;
}

.conflict-count {
  background: #e74c3c;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-weight: 500;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conflict-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  background: #fef2f2;
}

.conflict-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.conflict-details {
  margin-bottom: 1rem;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.appointment-type {
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #64748b;
}

.conflict-reason {
  font-size: 0.875rem;
  color: #ef4444;
}

.conflict-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn.reschedule {
  background: #3b82f6;
  color: white;
}

.action-btn.reschedule:hover {
  background: #2563eb;
}

.action-btn.cancel {
  background: #ef4444;
  color: white;
}

.action-btn.cancel:hover {
  background: #dc2626;
}

/* Nouveaux styles pour les données de la DB */
.loading-state,
.no-conflicts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #64748b;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #3b82f6;
}

.no-conflicts i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #10b981;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.severity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-badge.faible {
  background: #dcfce7;
  color: #166534;
}

.severity-badge.moyenne {
  background: #fef3c7;
  color: #92400e;
}

.severity-badge.elevee {
  background: #fed7aa;
  color: #c2410c;
}

.severity-badge.critique {
  background: #fecaca;
  color: #dc2626;
}

.appointments-info {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
}

.appointment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.appointment-item:last-child {
  margin-bottom: 0;
}

.appointment-item .time {
  font-size: 0.875rem;
  color: #64748b;
}

.gap-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #fef2f2;
  border-radius: 4px;
  color: #dc2626;
  font-size: 0.875rem;
}

.severity-faible .conflict-item {
  border-left: 4px solid #10b981;
}

.severity-moyenne .conflict-item {
  border-left: 4px solid #f59e0b;
}

.severity-elevee .conflict-item {
  border-left: 4px solid #f97316;
}

.severity-critique .conflict-item {
  border-left: 4px solid #ef4444;
}

@media (max-width: 768px) {
  .conflict-item {
    padding: 0.75rem;
  }

  .conflict-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
