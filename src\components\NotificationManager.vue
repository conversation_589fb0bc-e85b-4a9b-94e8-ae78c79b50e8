<template>
  <div class="notification-manager">
    <!-- En-tête avec statistiques -->
    <div class="stats-header">
      <h2 class="title">📧 Gestion des Notifications</h2>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.total || 0 }}</div>
            <div class="stat-label">Total</div>
          </div>
        </div>
        
        <div class="stat-card pending">
          <div class="stat-icon">⏳</div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.en_attente || 0 }}</div>
            <div class="stat-label">En attente</div>
          </div>
        </div>
        
        <div class="stat-card sent">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.envoyes || 0 }}</div>
            <div class="stat-label">Envoyées</div>
          </div>
        </div>
        
        <div class="stat-card errors">
          <div class="stat-icon">❌</div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.erreurs || 0 }}</div>
            <div class="stat-label">Erreurs</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions rapides -->
    <div class="actions-section">
      <h3>🔧 Actions Rapides</h3>
      
      <div class="actions-grid">
        <button 
          @click="processNotifications" 
          :disabled="processing"
          class="action-btn process"
        >
          <span class="btn-icon">📧</span>
          <span class="btn-text">
            {{ processing ? 'Traitement...' : 'Traiter les notifications' }}
          </span>
        </button>
        
        <button 
          @click="createReminders" 
          :disabled="creatingReminders"
          class="action-btn reminders"
        >
          <span class="btn-icon">⏰</span>
          <span class="btn-text">
            {{ creatingReminders ? 'Création...' : 'Créer les rappels' }}
          </span>
        </button>
        
        <button 
          @click="cleanOldNotifications" 
          :disabled="cleaning"
          class="action-btn clean"
        >
          <span class="btn-icon">🧹</span>
          <span class="btn-text">
            {{ cleaning ? 'Nettoyage...' : 'Nettoyer anciennes' }}
          </span>
        </button>
        
        <button 
          @click="refreshStats" 
          :disabled="loading"
          class="action-btn refresh"
        >
          <span class="btn-icon">🔄</span>
          <span class="btn-text">
            {{ loading ? 'Actualisation...' : 'Actualiser' }}
          </span>
        </button>
      </div>
    </div>

    <!-- Détails par type -->
    <div class="details-section">
      <h3>📋 Détails par Type</h3>
      
      <div class="type-stats">
        <div class="type-card">
          <div class="type-header">
            <span class="type-icon">✅</span>
            <span class="type-name">Confirmations</span>
          </div>
          <div class="type-count">{{ stats.confirmations || 0 }}</div>
        </div>
        
        <div class="type-card">
          <div class="type-header">
            <span class="type-icon">⏰</span>
            <span class="type-name">Rappels</span>
          </div>
          <div class="type-count">{{ stats.rappels || 0 }}</div>
        </div>
        
        <div class="type-card">
          <div class="type-header">
            <span class="type-icon">❌</span>
            <span class="type-name">Annulations</span>
          </div>
          <div class="type-count">{{ stats.annulations || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- Logs récents -->
    <div class="logs-section">
      <h3>📝 Activité Récente</h3>
      
      <div class="logs-container">
        <div v-if="logs.length === 0" class="no-logs">
          Aucune activité récente
        </div>
        
        <div v-for="log in logs" :key="log.id" class="log-item">
          <div class="log-time">{{ formatTime(log.timestamp) }}</div>
          <div class="log-message">{{ log.message }}</div>
          <div class="log-status" :class="log.status">{{ log.status }}</div>
        </div>
      </div>
    </div>

    <!-- Configuration -->
    <div class="config-section">
      <h3>⚙️ Configuration</h3>
      
      <div class="config-info">
        <div class="config-item">
          <span class="config-label">📧 Service Email:</span>
          <span class="config-value">{{ emailConfig.service || 'Simulation (développement)' }}</span>
        </div>
        
        <div class="config-item">
          <span class="config-label">⏰ Cron Job:</span>
          <span class="config-value">{{ cronStatus }}</span>
        </div>
        
        <div class="config-item">
          <span class="config-label">🕒 Dernière exécution:</span>
          <span class="config-value">{{ lastExecution || 'Jamais' }}</span>
        </div>
      </div>
      
      <div class="config-instructions">
        <h4>💡 Instructions de configuration :</h4>
        <ul>
          <li>Configurer les paramètres SMTP dans <code>EmailService.php</code></li>
          <li>Installer PHPMailer ou un service d'email</li>
          <li>Configurer le cron job : <code>*/5 * * * * php api/cron/process-notifications.php</code></li>
          <li>Tester l'envoi d'emails réels</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { notificationService } from '@/services/notificationService'

export default {
  name: 'NotificationManager',
  data() {
    return {
      stats: {},
      logs: [],
      loading: false,
      processing: false,
      creatingReminders: false,
      cleaning: false,
      emailConfig: {
        service: 'Simulation (développement)'
      },
      cronStatus: 'Non configuré',
      lastExecution: null
    }
  },
  
  async mounted() {
    await this.refreshStats()
    this.initializeLogs()
  },
  
  methods: {
    async refreshStats() {
      this.loading = true
      try {
        const response = await notificationService.getStats()
        this.stats = response.data
        this.addLog('Statistiques actualisées', 'success')
      } catch (error) {
        console.error('Erreur lors de la récupération des stats:', error)
        this.addLog('Erreur lors de l\'actualisation', 'error')
      } finally {
        this.loading = false
      }
    },
    
    async processNotifications() {
      this.processing = true
      try {
        const response = await notificationService.processNotifications()
        this.addLog(`${response.processed} notifications traitées`, 'success')
        await this.refreshStats()
      } catch (error) {
        console.error('Erreur lors du traitement:', error)
        this.addLog('Erreur lors du traitement', 'error')
      } finally {
        this.processing = false
      }
    },
    
    async createReminders() {
      this.creatingReminders = true
      try {
        const response = await notificationService.createReminders()
        this.addLog(`${response.created} rappels créés`, 'success')
        await this.refreshStats()
      } catch (error) {
        console.error('Erreur lors de la création des rappels:', error)
        this.addLog('Erreur lors de la création des rappels', 'error')
      } finally {
        this.creatingReminders = false
      }
    },
    
    async cleanOldNotifications() {
      this.cleaning = true
      try {
        const response = await notificationService.cleanOldNotifications()
        this.addLog(`${response.deleted_count} notifications supprimées`, 'success')
        await this.refreshStats()
      } catch (error) {
        console.error('Erreur lors du nettoyage:', error)
        this.addLog('Erreur lors du nettoyage', 'error')
      } finally {
        this.cleaning = false
      }
    },
    
    addLog(message, status = 'info') {
      const log = {
        id: Date.now(),
        message,
        status,
        timestamp: new Date()
      }
      
      this.logs.unshift(log)
      
      // Garder seulement les 10 derniers logs
      if (this.logs.length > 10) {
        this.logs = this.logs.slice(0, 10)
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('fr-FR')
    },
    
    initializeLogs() {
      this.addLog('Gestionnaire de notifications initialisé', 'info')
    }
  }
}
</script>

<style scoped>
.notification-manager {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  color: #1f2937;
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
}

.stats-header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background: #f8fafc;
  border-left: 4px solid #3b82f6;
}

.stat-card.pending { border-left-color: #f59e0b; }
.stat-card.sent { border-left-color: #10b981; }
.stat-card.errors { border-left-color: #ef4444; }

.stat-icon {
  font-size: 24px;
  margin-right: 12px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
}

.actions-section, .details-section, .logs-section, .config-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn.process { background: #3b82f6; color: white; }
.action-btn.reminders { background: #f59e0b; color: white; }
.action-btn.clean { background: #ef4444; color: white; }
.action-btn.refresh { background: #10b981; color: white; }

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-icon {
  margin-right: 8px;
  font-size: 16px;
}

.type-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.type-card {
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
}

.type-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.type-icon {
  margin-right: 8px;
  font-size: 18px;
}

.type-count {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 15px;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

.log-time {
  color: #6b7280;
  margin-right: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
  color: #374151;
}

.log-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.log-status.success { background: #d1fae5; color: #065f46; }
.log-status.error { background: #fee2e2; color: #991b1b; }
.log-status.info { background: #dbeafe; color: #1e40af; }

.config-info {
  margin-top: 15px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.config-label {
  font-weight: 500;
  color: #374151;
}

.config-value {
  color: #6b7280;
}

.config-instructions {
  margin-top: 20px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.config-instructions h4 {
  margin: 0 0 10px 0;
  color: #1e40af;
}

.config-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.config-instructions li {
  margin-bottom: 5px;
  color: #374151;
}

.config-instructions code {
  background: #e5e7eb;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.no-logs {
  text-align: center;
  color: #6b7280;
  padding: 20px;
  font-style: italic;
}

h3 {
  color: #1f2937;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 600;
}
</style>
