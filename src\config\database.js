import mysql from 'mysql2/promise'

export const dbConfig = {
  host: '127.0.0.1',
  user: 'root',
  password: '',
  database: 'agenda_medical',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
}

// Création du pool de connexions
const pool = mysql.createPool(dbConfig)

// Fonction pour tester la connexion
async function testConnection() {
  try {
    const connection = await pool.getConnection()
    console.log('Connexion à la base de données réussie !')
    connection.release()
    return true
  } catch (error) {
    console.error('Erreur de connexion à la base de données:', error)
    return false
  }
}

export { pool, testConnection } 