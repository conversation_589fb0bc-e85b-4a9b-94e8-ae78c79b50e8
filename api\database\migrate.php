<?php
require_once __DIR__ . '/../config/database.php';

try {
    $database = Database::getInstance();
    $db = $database->getConnection();
    
    // Lecture du fichier SQL
    $sql = file_get_contents(__DIR__ . '/schema.sql');
    
    // Exécution des requêtes SQL
    $result = $db->exec($sql);
    
    echo "Migration réussie !\n";
    echo "Tables créées avec succès.\n";
    
} catch (PDOException $e) {
    die("Erreur lors de la migration : " . $e->getMessage() . "\n");
} 