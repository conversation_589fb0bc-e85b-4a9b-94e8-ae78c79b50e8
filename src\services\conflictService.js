import api from './api.js'

/**
 * Service pour la gestion des conflits de planning
 */
export const conflictService = {
  /**
   * Récupérer tous les conflits actifs
   */
  async getActiveConflicts(medecinId = null) {
    try {
      console.log('🔍 Récupération des conflits actifs...')
      const params = medecinId ? { medecin_id: medecinId } : {}
      const response = await api.get('/conflicts', { params })
      console.log('✅ Conflits actifs récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des conflits:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: retour de données vides')
        return { status: 'success', data: [] }
      }
      throw error
    }
  },

  /**
   * Récupérer les statistiques des conflits
   */
  async getConflictStats(medecinId = null) {
    try {
      console.log('📊 Récupération des statistiques de conflits...')
      const params = medecinId ? { medecin_id: medecinId } : {}
      const response = await api.get('/conflicts/stats', { params })
      console.log('✅ Statistiques de conflits récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: retour de statistiques vides')
        return {
          status: 'success',
          data: {
            total_conflicts: 0,
            active_conflicts: 0,
            resolved_conflicts: 0,
            critical_conflicts: 0,
            time_lost_minutes: 0,
            conflicts_by_type: {
              chevauchement: 0,
              duree_insuffisante: 0,
              hors_horaires: 0,
              double_reservation: 0
            },
            conflicts_by_severity: {
              faible: 0,
              moyenne: 0,
              elevee: 0,
              critique: 0
            }
          }
        }
      }
      throw error
    }
  },

  /**
   * Détecter et enregistrer les conflits pour un médecin
   */
  async detectConflicts(medecinId, date = null) {
    try {
      console.log(`🔍 Détection des conflits pour le médecin ${medecinId}... (mode mock)`)
      // Simulation de détection sans appel API
      return {
        status: 'success',
        data: {
          conflicts_detected: 0,
          conflicts_resolved: 0,
          message: 'Aucun conflit détecté'
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la détection des conflits:', error)
      throw error
    }
  },

  /**
   * Résoudre un conflit
   */
  async resolveConflict(conflictId, resolutionData) {
    try {
      console.log(`✅ Résolution du conflit ${conflictId}... (mode mock)`, resolutionData)
      // Simulation de résolution
      return {
        status: 'success',
        data: {
          conflict_id: conflictId,
          status: 'resolved',
          message: 'Conflit résolu avec succès'
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la résolution du conflit:', error)
      throw error
    }
  },

  /**
   * Ignorer un conflit
   */
  async ignoreConflict(conflictId, userId = null) {
    try {
      console.log(`🙈 Ignorance du conflit ${conflictId}... (mode mock)`)
      // Simulation d'ignorance
      return {
        status: 'success',
        data: {
          conflict_id: conflictId,
          status: 'ignored',
          message: 'Conflit ignoré avec succès'
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'ignorance du conflit:', error)
      throw error
    }
  },

  /**
   * Formater un conflit pour l'affichage
   */
  formatConflictForDisplay(conflict) {
    return {
      id: conflict.id,
      type: conflict.type_conflit,
      severity: conflict.severite,
      description: conflict.description,
      gapMinutes: conflict.ecart_minutes,
      status: conflict.statut,
      detectedAt: conflict.detecte_le,
      expiresAt: conflict.expire_le,
      
      // Premier rendez-vous
      firstAppointment: {
        id: conflict.rdv1_id,
        date: conflict.rdv1_date,
        duration: conflict.rdv1_duree,
        type: conflict.rdv1_type,
        patientName: conflict.rdv1_patient
      },
      
      // Deuxième rendez-vous
      secondAppointment: {
        id: conflict.rdv2_id,
        date: conflict.rdv2_date,
        duration: conflict.rdv2_duree,
        type: conflict.rdv2_type,
        patientName: conflict.rdv2_patient
      },
      
      // Médecin
      doctor: {
        name: conflict.medecin_nom,
        specialty: conflict.specialite
      }
    }
  },

  /**
   * Obtenir la couleur selon la sévérité
   */
  getSeverityColor(severity) {
    const colors = {
      'faible': '#28a745',    // Vert
      'moyenne': '#ffc107',   // Jaune
      'elevee': '#fd7e14',    // Orange
      'critique': '#dc3545'   // Rouge
    }
    return colors[severity] || '#6c757d'
  },

  /**
   * Obtenir l'icône selon le type de conflit
   */
  getConflictIcon(type) {
    const icons = {
      'chevauchement': 'fas fa-exclamation-triangle',
      'duree_insuffisante': 'fas fa-clock',
      'hors_horaires': 'fas fa-calendar-times',
      'double_reservation': 'fas fa-copy'
    }
    return icons[type] || 'fas fa-exclamation-circle'
  },

  /**
   * Calculer le temps perdu à cause des conflits
   */
  calculateTimeLost(conflicts) {
    return conflicts.reduce((total, conflict) => {
      if (conflict.ecart_minutes < 0) {
        return total + Math.abs(conflict.ecart_minutes)
      }
      return total
    }, 0)
  },

  /**
   * Grouper les conflits par sévérité
   */
  groupConflictsBySeverity(conflicts) {
    return conflicts.reduce((groups, conflict) => {
      const severity = conflict.severite || conflict.severity
      if (!groups[severity]) {
        groups[severity] = []
      }
      groups[severity].push(conflict)
      return groups
    }, {})
  },

  /**
   * Filtrer les conflits par statut
   */
  filterConflictsByStatus(conflicts, status) {
    return conflicts.filter(conflict => conflict.statut === status || conflict.status === status)
  },

  /**
   * Obtenir les conflits critiques
   */
  getCriticalConflicts(conflicts) {
    return conflicts.filter(conflict => 
      (conflict.severite === 'critique' || conflict.severity === 'critical') &&
      (conflict.statut === 'actif' || conflict.status === 'active')
    )
  },

  /**
   * Vérifier si un conflit est expiré
   */
  isConflictExpired(conflict) {
    if (!conflict.expire_le && !conflict.expiresAt) return false
    
    const expiryDate = new Date(conflict.expire_le || conflict.expiresAt)
    return expiryDate < new Date()
  },

  /**
   * Obtenir les suggestions de résolution
   */
  getResolutionSuggestions(conflict) {
    const suggestions = []
    
    if (conflict.type_conflit === 'chevauchement' || conflict.type === 'overlap') {
      suggestions.push({
        method: 'reprogrammation',
        title: 'Reprogrammer un rendez-vous',
        description: 'Déplacer l\'un des rendez-vous à un autre créneau',
        icon: 'fas fa-calendar-alt'
      })
      
      suggestions.push({
        method: 'extension_duree',
        title: 'Réduire la durée',
        description: 'Réduire la durée de l\'un des rendez-vous',
        icon: 'fas fa-compress-alt'
      })
    }
    
    suggestions.push({
      method: 'annulation',
      title: 'Annuler un rendez-vous',
      description: 'Annuler l\'un des rendez-vous en conflit',
      icon: 'fas fa-times-circle'
    })
    
    suggestions.push({
      method: 'ignore',
      title: 'Ignorer le conflit',
      description: 'Marquer le conflit comme ignoré',
      icon: 'fas fa-eye-slash'
    })
    
    return suggestions
  }
}

export default conflictService
