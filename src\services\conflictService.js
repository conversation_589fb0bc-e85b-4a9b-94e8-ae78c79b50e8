import api from './api.js'

/**
 * Service pour la gestion des conflits de planning
 */
export const conflictService = {
  /**
   * Récupérer tous les conflits actifs
   */
  async getActiveConflicts(medecinId = null) {
    try {
      console.log('🔍 Récupération des conflits actifs...')
      const params = medecinId ? { medecin_id: medecinId } : {}
      const response = await api.get('/conflicts', { params })
      console.log('✅ Conflits actifs récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des conflits:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: retour de données vides')
        return { status: 'success', data: [] }
      }
      throw error
    }
  },

  /**
   * Récupérer les statistiques des conflits
   */
  async getConflictStats(medecinId = null) {
    try {
      console.log('📊 Récupération des statistiques de conflits...')
      const params = medecinId ? { medecin_id: medecinId } : {}
      const response = await api.get('/conflicts/stats', { params })
      console.log('✅ Statistiques de conflits récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: retour de statistiques vides')
        return {
          status: 'success',
          data: {
            total_conflicts: 0,
            active_conflicts: 0,
            resolved_conflicts: 0,
            critical_conflicts: 0,
            time_lost_minutes: 0,
            conflicts_by_type: {
              chevauchement: 0,
              duree_insuffisante: 0,
              hors_horaires: 0,
              double_reservation: 0
            },
            conflicts_by_severity: {
              faible: 0,
              moyenne: 0,
              elevee: 0,
              critique: 0
            }
          }
        }
      }
      throw error
    }
  },

  /**
   * Détecter et enregistrer les conflits pour un médecin
   */
  async detectConflicts(medecinId, date = null) {
    try {
      console.log(`🔍 Détection des conflits pour le médecin ${medecinId}...`)
      const params = { medecin_id: medecinId }
      if (date) {
        params.date = date
      }

      const response = await api.post('/conflicts/detect', params)
      console.log('✅ Détection des conflits terminée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la détection des conflits:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: simulation de détection')
        return {
          status: 'success',
          data: {
            conflicts_detected: 0,
            conflicts_resolved: 0,
            message: 'Aucun conflit détecté (mode fallback)'
          }
        }
      }
      throw error
    }
  },

  /**
   * Résoudre un conflit
   */
  async resolveConflict(conflictId, resolutionData) {
    try {
      console.log(`✅ Résolution du conflit ${conflictId}...`, resolutionData)
      const response = await api.put(`/conflicts/${conflictId}/resolve`, resolutionData)
      console.log('✅ Conflit résolu avec succès:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la résolution du conflit:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: simulation de résolution')
        return {
          status: 'success',
          data: {
            conflict_id: conflictId,
            status: 'resolved',
            message: 'Conflit résolu avec succès (mode fallback)'
          }
        }
      }
      throw error
    }
  },

  /**
   * Ignorer un conflit
   */
  async ignoreConflict(conflictId, userId = null) {
    try {
      console.log(`🙈 Ignorance du conflit ${conflictId}...`)
      const requestData = { conflict_id: conflictId }
      if (userId) {
        requestData.user_id = userId
      }

      const response = await api.put(`/conflicts/${conflictId}/ignore`, requestData)
      console.log('✅ Conflit ignoré avec succès:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de l\'ignorance du conflit:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: simulation d\'ignorance')
        return {
          status: 'success',
          data: {
            conflict_id: conflictId,
            status: 'ignored',
            message: 'Conflit ignoré avec succès (mode fallback)'
          }
        }
      }
      throw error
    }
  },

  /**
   * Formater un conflit pour l'affichage
   */
  formatConflictForDisplay(conflict) {
    return {
      id: conflict.id,
      type: conflict.type_conflit,
      severity: conflict.severite,
      description: conflict.description,
      gapMinutes: conflict.ecart_minutes,
      status: conflict.statut,
      detectedAt: conflict.detecte_le,
      expiresAt: conflict.expire_le,
      
      // Premier rendez-vous
      firstAppointment: {
        id: conflict.rdv1_id,
        date: conflict.rdv1_date,
        duration: conflict.rdv1_duree,
        type: conflict.rdv1_type,
        patientName: conflict.rdv1_patient
      },
      
      // Deuxième rendez-vous
      secondAppointment: {
        id: conflict.rdv2_id,
        date: conflict.rdv2_date,
        duration: conflict.rdv2_duree,
        type: conflict.rdv2_type,
        patientName: conflict.rdv2_patient
      },
      
      // Médecin
      doctor: {
        name: conflict.medecin_nom,
        specialty: conflict.specialite
      }
    }
  },

  /**
   * Obtenir la couleur selon la sévérité
   */
  getSeverityColor(severity) {
    const colors = {
      'faible': '#28a745',    // Vert
      'moyenne': '#ffc107',   // Jaune
      'elevee': '#fd7e14',    // Orange
      'critique': '#dc3545'   // Rouge
    }
    return colors[severity] || '#6c757d'
  },

  /**
   * Obtenir l'icône selon le type de conflit
   */
  getConflictIcon(type) {
    const icons = {
      'chevauchement': 'fas fa-exclamation-triangle',
      'duree_insuffisante': 'fas fa-clock',
      'hors_horaires': 'fas fa-calendar-times',
      'double_reservation': 'fas fa-copy',
      'patient_absent': 'fas fa-user-times',
      'medecin_indisponible': 'fas fa-user-md-times',
      'salle_occupee': 'fas fa-door-closed',
      'equipement_indisponible': 'fas fa-tools',
      'urgence_medicale': 'fas fa-ambulance',
      'retard_precedent': 'fas fa-hourglass-half'
    }
    return icons[type] || 'fas fa-exclamation-circle'
  },

  /**
   * Obtenir tous les types de conflits disponibles
   */
  getConflictTypes() {
    return {
      'chevauchement': {
        label: 'Chevauchement de créneaux',
        description: 'Deux rendez-vous se chevauchent dans le temps',
        severity_default: 'elevee',
        icon: 'fas fa-exclamation-triangle',
        color: '#fd7e14'
      },
      'duree_insuffisante': {
        label: 'Durée insuffisante',
        description: 'Le temps alloué est insuffisant pour le type de consultation',
        severity_default: 'moyenne',
        icon: 'fas fa-clock',
        color: '#ffc107'
      },
      'hors_horaires': {
        label: 'Hors horaires de travail',
        description: 'Rendez-vous programmé en dehors des horaires du médecin',
        severity_default: 'elevee',
        icon: 'fas fa-calendar-times',
        color: '#fd7e14'
      },
      'double_reservation': {
        label: 'Double réservation',
        description: 'Même patient avec plusieurs rendez-vous simultanés',
        severity_default: 'critique',
        icon: 'fas fa-copy',
        color: '#dc3545'
      },
      'patient_absent': {
        label: 'Patient absent',
        description: 'Patient marqué comme absent mais créneau toujours réservé',
        severity_default: 'faible',
        icon: 'fas fa-user-times',
        color: '#28a745'
      },
      'medecin_indisponible': {
        label: 'Médecin indisponible',
        description: 'Médecin en congé ou indisponible pendant le créneau',
        severity_default: 'critique',
        icon: 'fas fa-user-md-times',
        color: '#dc3545'
      },
      'salle_occupee': {
        label: 'Salle occupée',
        description: 'Salle de consultation déjà occupée',
        severity_default: 'elevee',
        icon: 'fas fa-door-closed',
        color: '#fd7e14'
      },
      'equipement_indisponible': {
        label: 'Équipement indisponible',
        description: 'Équipement médical nécessaire non disponible',
        severity_default: 'moyenne',
        icon: 'fas fa-tools',
        color: '#ffc107'
      },
      'urgence_medicale': {
        label: 'Urgence médicale',
        description: 'Urgence médicale perturbant le planning',
        severity_default: 'critique',
        icon: 'fas fa-ambulance',
        color: '#dc3545'
      },
      'retard_precedent': {
        label: 'Retard du rendez-vous précédent',
        description: 'Retard causé par le rendez-vous précédent',
        severity_default: 'moyenne',
        icon: 'fas fa-hourglass-half',
        color: '#ffc107'
      }
    }
  },

  /**
   * Analyser et catégoriser un conflit selon sa complexité
   */
  analyzeConflictComplexity(conflict) {
    const analysis = {
      complexity: 'simple',
      factors: [],
      recommendations: [],
      estimated_resolution_time: 5 // minutes
    }

    // Analyser les facteurs de complexité
    if (conflict.type_conflit === 'chevauchement' || conflict.type === 'overlap') {
      analysis.factors.push('temporal_overlap')
      analysis.estimated_resolution_time += 10
    }

    if (conflict.severite === 'critique' || conflict.severity === 'critical') {
      analysis.factors.push('critical_severity')
      analysis.complexity = 'complex'
      analysis.estimated_resolution_time += 15
    }

    // Vérifier si plusieurs patients sont impliqués
    if (conflict.rdv1_patient !== conflict.rdv2_patient) {
      analysis.factors.push('multiple_patients')
      analysis.estimated_resolution_time += 10
    }

    // Vérifier la proximité temporelle
    const rdv1Date = new Date(conflict.rdv1_date)
    const rdv2Date = new Date(conflict.rdv2_date)
    const timeDiff = Math.abs(rdv1Date - rdv2Date) / (1000 * 60) // en minutes

    if (timeDiff < 30) {
      analysis.factors.push('close_temporal_proximity')
      analysis.complexity = 'complex'
      analysis.estimated_resolution_time += 20
    }

    // Générer des recommandations
    if (analysis.factors.includes('critical_severity')) {
      analysis.recommendations.push('Résolution immédiate requise')
    }

    if (analysis.factors.includes('multiple_patients')) {
      analysis.recommendations.push('Contacter tous les patients concernés')
    }

    if (analysis.factors.includes('close_temporal_proximity')) {
      analysis.recommendations.push('Envisager une reprogrammation avec plus d\'écart')
    }

    // Déterminer la complexité finale
    if (analysis.factors.length >= 3) {
      analysis.complexity = 'very_complex'
      analysis.estimated_resolution_time += 30
    } else if (analysis.factors.length >= 2) {
      analysis.complexity = 'complex'
    }

    return analysis
  },

  /**
   * Calculer le temps perdu à cause des conflits
   */
  calculateTimeLost(conflicts) {
    return conflicts.reduce((total, conflict) => {
      if (conflict.ecart_minutes < 0) {
        return total + Math.abs(conflict.ecart_minutes)
      }
      return total
    }, 0)
  },

  /**
   * Grouper les conflits par sévérité
   */
  groupConflictsBySeverity(conflicts) {
    return conflicts.reduce((groups, conflict) => {
      const severity = conflict.severite || conflict.severity
      if (!groups[severity]) {
        groups[severity] = []
      }
      groups[severity].push(conflict)
      return groups
    }, {})
  },

  /**
   * Filtrer les conflits par statut
   */
  filterConflictsByStatus(conflicts, status) {
    return conflicts.filter(conflict => conflict.statut === status || conflict.status === status)
  },

  /**
   * Obtenir les conflits critiques
   */
  getCriticalConflicts(conflicts) {
    return conflicts.filter(conflict => 
      (conflict.severite === 'critique' || conflict.severity === 'critical') &&
      (conflict.statut === 'actif' || conflict.status === 'active')
    )
  },

  /**
   * Vérifier si un conflit est expiré
   */
  isConflictExpired(conflict) {
    if (!conflict.expire_le && !conflict.expiresAt) return false

    const expiryDate = new Date(conflict.expire_le || conflict.expiresAt)
    return expiryDate < new Date()
  },

  /**
   * Nettoyer automatiquement les conflits expirés
   */
  async cleanupExpiredConflicts(medecinId = null) {
    try {
      console.log('🧹 Nettoyage des conflits expirés...')
      const params = medecinId ? { medecin_id: medecinId } : {}

      const response = await api.delete('/conflicts/expired', { params })
      console.log('✅ Conflits expirés nettoyés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage des conflits expirés:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: simulation de nettoyage')
        return {
          status: 'success',
          data: {
            cleaned_conflicts: 0,
            message: 'Aucun conflit expiré à nettoyer (mode fallback)'
          }
        }
      }
      throw error
    }
  },

  /**
   * Vérifier et nettoyer automatiquement les conflits selon l'heure des rendez-vous
   */
  async autoCleanupByAppointmentTime(medecinId = null) {
    try {
      console.log('⏰ Nettoyage automatique basé sur l\'heure des rendez-vous...')
      const params = medecinId ? { medecin_id: medecinId } : {}

      const response = await api.delete('/conflicts/auto-cleanup', { params })
      console.log('✅ Nettoyage automatique terminé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage automatique:', error)
      // Fallback local si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: nettoyage local')
        // Récupérer les conflits actifs et les filtrer localement
        const activeConflicts = await this.getActiveConflicts(medecinId)
        const conflictsToClean = []

        if (activeConflicts.data && Array.isArray(activeConflicts.data)) {
          for (const conflict of activeConflicts.data) {
            // Vérifier si les heures de rendez-vous sont passées
            const rdv1Date = new Date(conflict.rdv1_date)
            const rdv2Date = new Date(conflict.rdv2_date)
            const now = new Date()

            if (rdv1Date < now && rdv2Date < now) {
              conflictsToClean.push(conflict.id)
            }
          }
        }

        return {
          status: 'success',
          data: {
            cleaned_conflicts: conflictsToClean.length,
            conflict_ids: conflictsToClean,
            message: `${conflictsToClean.length} conflit(s) nettoyé(s) localement`
          }
        }
      }
      throw error
    }
  },

  /**
   * Notifier les utilisateurs des nouveaux conflits
   */
  async notifyConflicts(conflicts, medecinId = null) {
    try {
      console.log('📢 Notification des conflits...')
      const notificationData = {
        conflicts: conflicts.map(conflict => ({
          id: conflict.id,
          type: conflict.type_conflit || conflict.type,
          severity: conflict.severite || conflict.severity,
          description: conflict.description,
          medecin_id: conflict.medecin_id || medecinId
        })),
        medecin_id: medecinId
      }

      const response = await api.post('/conflicts/notify', notificationData)
      console.log('✅ Notifications envoyées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi des notifications:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: simulation de notification')
        return {
          status: 'success',
          data: {
            notifications_sent: conflicts.length,
            message: `${conflicts.length} notification(s) simulée(s)`
          }
        }
      }
      throw error
    }
  },

  /**
   * Obtenir les conflits nécessitant une notification urgente
   */
  getUrgentConflictsForNotification(conflicts) {
    return conflicts.filter(conflict => {
      const severity = conflict.severite || conflict.severity
      const status = conflict.statut || conflict.status

      // Notifier les conflits critiques et élevés qui sont actifs
      return (severity === 'critique' || severity === 'elevee' ||
              severity === 'critical' || severity === 'high') &&
             (status === 'actif' || status === 'active')
    })
  },

  /**
   * Programmer des notifications automatiques
   */
  async scheduleConflictNotifications(medecinId = null, intervalMinutes = 30) {
    try {
      console.log(`⏰ Programmation des notifications automatiques (${intervalMinutes} min)...`)
      const params = {
        medecin_id: medecinId,
        interval_minutes: intervalMinutes
      }

      const response = await api.post('/conflicts/schedule-notifications', params)
      console.log('✅ Notifications programmées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la programmation des notifications:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: simulation de programmation')
        return {
          status: 'success',
          data: {
            scheduled: true,
            interval_minutes: intervalMinutes,
            message: 'Notifications programmées (mode fallback)'
          }
        }
      }
      throw error
    }
  },

  /**
   * Obtenir les suggestions de résolution
   */
  getResolutionSuggestions(conflict) {
    const suggestions = []
    
    if (conflict.type_conflit === 'chevauchement' || conflict.type === 'overlap') {
      suggestions.push({
        method: 'reprogrammation',
        title: 'Reprogrammer un rendez-vous',
        description: 'Déplacer l\'un des rendez-vous à un autre créneau',
        icon: 'fas fa-calendar-alt'
      })
      
      suggestions.push({
        method: 'extension_duree',
        title: 'Réduire la durée',
        description: 'Réduire la durée de l\'un des rendez-vous',
        icon: 'fas fa-compress-alt'
      })
    }
    
    suggestions.push({
      method: 'annulation',
      title: 'Annuler un rendez-vous',
      description: 'Annuler l\'un des rendez-vous en conflit',
      icon: 'fas fa-times-circle'
    })
    
    suggestions.push({
      method: 'ignore',
      title: 'Ignorer le conflit',
      description: 'Marquer le conflit comme ignoré',
      icon: 'fas fa-eye-slash'
    })
    
    return suggestions
  },

  /**
   * Valider un créneau avant création pour prévenir les conflits
   */
  async validateTimeSlot(slotData) {
    try {
      console.log('🔍 Validation du créneau...', slotData)
      const response = await api.post('/conflicts/validate-slot', slotData)
      console.log('✅ Validation terminée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la validation:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: validation locale')
        return this.validateTimeSlotLocal(slotData)
      }
      throw error
    }
  },

  /**
   * Validation locale d'un créneau (fallback)
   */
  async validateTimeSlotLocal(slotData) {
    const validation = {
      is_valid: true,
      conflicts: [],
      warnings: [],
      suggestions: []
    }

    try {
      // Récupérer les conflits existants pour le médecin
      const existingConflicts = await this.getActiveConflicts(slotData.medecin_id)

      // Vérifier les chevauchements potentiels
      const slotStart = new Date(slotData.date_heure)
      const slotEnd = new Date(slotStart.getTime() + (slotData.duree * 60000))

      if (existingConflicts.data && Array.isArray(existingConflicts.data)) {
        for (const conflict of existingConflicts.data) {
          const conflictStart = new Date(conflict.rdv1_date)
          const conflictEnd = new Date(conflictStart.getTime() + (conflict.rdv1_duree * 60000))

          // Vérifier le chevauchement
          if (slotStart < conflictEnd && slotEnd > conflictStart) {
            validation.is_valid = false
            validation.conflicts.push({
              type: 'chevauchement',
              message: 'Chevauchement détecté avec un rendez-vous existant',
              existing_appointment: conflict
            })
          }
        }
      }

      // Vérifier les horaires de travail (exemple: 8h-18h)
      const hour = slotStart.getHours()
      if (hour < 8 || hour >= 18) {
        validation.warnings.push({
          type: 'hors_horaires',
          message: 'Créneau en dehors des horaires habituels (8h-18h)'
        })
      }

      // Vérifier la durée minimale (exemple: 15 minutes)
      if (slotData.duree < 15) {
        validation.warnings.push({
          type: 'duree_courte',
          message: 'Durée très courte, vérifiez si c\'est suffisant'
        })
      }

      // Suggestions d'amélioration
      if (validation.conflicts.length > 0) {
        validation.suggestions.push({
          type: 'reprogrammation',
          message: 'Proposer des créneaux alternatifs',
          alternative_slots: this.generateAlternativeSlots(slotData)
        })
      }

    } catch (error) {
      console.error('Erreur lors de la validation locale:', error)
      validation.is_valid = false
      validation.conflicts.push({
        type: 'erreur_validation',
        message: 'Erreur lors de la validation, vérification manuelle recommandée'
      })
    }

    return {
      status: 'success',
      data: validation
    }
  },

  /**
   * Générer des créneaux alternatifs
   */
  generateAlternativeSlots(originalSlot) {
    const alternatives = []
    const baseDate = new Date(originalSlot.date_heure)

    // Proposer des créneaux +/- 30 minutes, 1 heure, 2 heures
    const offsets = [-120, -60, -30, 30, 60, 120] // en minutes

    for (const offset of offsets) {
      const newDate = new Date(baseDate.getTime() + (offset * 60000))

      // Vérifier que c'est dans les horaires de travail
      const hour = newDate.getHours()
      if (hour >= 8 && hour < 18) {
        alternatives.push({
          date_heure: newDate.toISOString(),
          duree: originalSlot.duree,
          medecin_id: originalSlot.medecin_id,
          offset_minutes: offset
        })
      }
    }

    return alternatives.slice(0, 3) // Limiter à 3 suggestions
  },

  /**
   * Prévenir les conflits en analysant les tendances
   */
  async analyzeConflictTrends(medecinId = null, days = 30) {
    try {
      console.log(`📈 Analyse des tendances de conflits (${days} jours)...`)
      const params = {
        medecin_id: medecinId,
        days: days
      }

      const response = await api.get('/conflicts/trends', { params })
      console.log('✅ Analyse des tendances terminée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de l\'analyse des tendances:', error)
      // Fallback en mode mock si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: analyse locale')
        return {
          status: 'success',
          data: {
            peak_conflict_hours: ['09:00', '14:00', '16:00'],
            most_common_types: ['chevauchement', 'duree_insuffisante'],
            recommendations: [
              'Éviter les créneaux de 9h et 14h pour les consultations longues',
              'Prévoir des tampons de 15 minutes entre les rendez-vous',
              'Vérifier la disponibilité des salles avant confirmation'
            ],
            conflict_frequency: {
              monday: 12,
              tuesday: 8,
              wednesday: 15,
              thursday: 10,
              friday: 6
            }
          }
        }
      }
      throw error
    }
  },

  /**
   * Obtenir des recommandations préventives
   */
  getPreventiveRecommendations(conflictTrends) {
    const recommendations = []

    if (conflictTrends.peak_conflict_hours) {
      recommendations.push({
        type: 'horaires',
        priority: 'high',
        message: `Éviter les créneaux ${conflictTrends.peak_conflict_hours.join(', ')} pour les consultations complexes`
      })
    }

    if (conflictTrends.most_common_types?.includes('chevauchement')) {
      recommendations.push({
        type: 'planning',
        priority: 'medium',
        message: 'Prévoir des tampons de 10-15 minutes entre les rendez-vous'
      })
    }

    if (conflictTrends.conflict_frequency) {
      const busiestDay = Object.entries(conflictTrends.conflict_frequency)
        .sort(([,a], [,b]) => b - a)[0]

      if (busiestDay && busiestDay[1] > 10) {
        recommendations.push({
          type: 'charge_travail',
          priority: 'medium',
          message: `Attention: ${busiestDay[0]} est le jour avec le plus de conflits (${busiestDay[1]})`
        })
      }
    }

    return recommendations
  }
}

export default conflictService
