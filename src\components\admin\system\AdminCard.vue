<!-- src/components/AdminCard.vue -->
<template>
  <div class="admin-card">
    <div
      class="admin-card-header"
      :style="{ backgroundColor: headerBgColor }"
      v-if="title"
    >
      <h2 class="admin-card-title">{{ title }}</h2>
    </div>
    <div class="admin-card-body">
      <slot />
    </div>
    <div class="admin-card-footer" v-if="$slots.footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  headerColor: {
    type: String,
    default: 'gray'
  }
})

const headerColors = {
  gray: '#f3f4f6',
  blue: '#e0e7ff',
  green: '#d1fae5',
  red: '#fee2e2',
  indigo: '#e0e7ff'
}
const headerBgColor = computed(() =>
  headerColors[props.headerColor] || headerColors.gray
)
</script>

<style scoped>
.admin-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  margin-bottom: 24px;
  border: 1px solid #ececec;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.admin-card-header {
  padding: 16px 24px;
  border-bottom: 1px solid #ececec;
}
.admin-card-title {
  font-size: 1.15rem;
  font-weight: 600;
  margin: 0;
  color: #374151;
}
.admin-card-body {
  padding: 24px;
  flex: 1 1 auto;
}
.admin-card-footer {
  padding: 16px 24px;
  border-top: 1px solid #ececec;
  background: #fafbfc;
}
</style>
