<?php

require_once __DIR__ . '/../models/Appointment.php';
require_once __DIR__ . '/../services/ConflictService.php';
require_once __DIR__ . '/../models/Doctor.php';

class AppointmentController {
    private $db;

    public function __construct($db = null) {
        $this->db = $db ?? Database::getInstance()->getConnection();
    }

    public function index() {
        try {
            $query = "
                SELECT 
                    rdv.*,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom,
                    CONCAT(u.nom, ' ', u.prenom) as medecin_nom,
                    m.specialite
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                INNER JOIN medecins m ON rdv.id_medecin = m.id
                INNER JOIN utilisateur u ON m.user_id = u.id
                ORDER BY rdv.date_rendez_vous DESC
            ";

            if (isset($_GET['limit']) && is_numeric($_GET['limit'])) {
                $query .= " LIMIT " . intval($_GET['limit']);
            }

            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $appointments
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans AppointmentController->index: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des rendez-vous'
            ]);
        }
    }

    public function store() {
        try {
            $rawData = file_get_contents("php://input");
            $data = json_decode($rawData, true);

            // Log des données reçues pour debug
            error_log("Données reçues pour création RDV: " . $rawData);

            // Validation des données requises avec mapping flexible
            $patientId = $data['patient_id'] ?? $data['id_patient'] ?? $data['patientId'] ?? null;
            $doctorId = $data['doctor_id'] ?? $data['id_medecin'] ?? $data['doctorId'] ?? null;
            $dateRendezVous = $data['date'] ?? $data['date_rendez_vous'] ?? $data['start_time'] ?? null;
            $type = $data['type'] ?? $data['reason'] ?? 'consultation';

            // Validation des champs obligatoires
            if (!$patientId || !$doctorId || !$dateRendezVous) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Champs obligatoires manquants',
                    'required' => ['patient_id', 'doctor_id', 'date'],
                    'received' => array_keys($data),
                    'debug' => [
                        'patient_id' => $patientId,
                        'doctor_id' => $doctorId,
                        'date' => $dateRendezVous
                    ]
                ]);
                return;
            }

            $query = "
                INSERT INTO rendez_vous
                (id_patient, id_medecin, date_rendez_vous, type, statut, notes, priorite, id_salle, duree)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $patientId,
                $doctorId,
                $dateRendezVous,
                $type,
                $data['statut'] ?? $data['status'] ?? 'planifie',
                $data['notes'] ?? null,
                $data['priorite'] ?? $data['priority'] ?? 'normale',
                $data['id_salle'] ?? $data['room_id'] ?? null,
                $data['duree'] ?? $data['duration'] ?? 30
            ]);

            $id = $this->db->lastInsertId();

            // Récupérer le rendez-vous créé avec toutes ses données
            $query = "
                SELECT
                    rdv.*,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom,
                    CONCAT(u.nom, ' ', u.prenom) as medecin_nom,
                    m.specialite
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                INNER JOIN medecins m ON rdv.id_medecin = m.id
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE rdv.id = ?
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            $createdAppointment = $stmt->fetch(PDO::FETCH_ASSOC);

            // Détecter et enregistrer automatiquement les conflits
            require_once __DIR__ . '/../models/Conflict.php';
            $conflictModel = new Conflict($this->db);
            $conflictsCreated = $conflictModel->detectAndSaveConflicts(
                $doctorId,
                date('Y-m-d', strtotime($dateRendezVous))
            );

            // Créer automatiquement les notifications
            $notificationsCreated = 0;
            try {
                require_once __DIR__ . '/../models/Notification.php';
                $notificationModel = new Notification($this->db);

                $confirmationIds = $notificationModel->createConfirmationNotifications($id);
                $reminderIds = $notificationModel->createReminderNotifications($id);

                $notificationsCreated = count($confirmationIds) + count($reminderIds);
                error_log("Notifications créées pour RDV $id: " . count($confirmationIds) . " confirmations, " . count($reminderIds) . " rappels");
            } catch (Exception $e) {
                error_log("Erreur lors de la création des notifications pour RDV $id: " . $e->getMessage());
                // Ne pas faire échouer la création du RDV si les notifications échouent
            }

            http_response_code(201);
            echo json_encode([
                'status' => 'success',
                'message' => 'Rendez-vous créé avec succès',
                'data' => $createdAppointment,
                'conflicts_detected' => $conflictsCreated,
                'notifications_created' => $notificationsCreated
            ]);
        } catch (PDOException $e) {
            error_log("Erreur PDO dans AppointmentController->store: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création du rendez-vous: ' . $e->getMessage()
            ]);
        } catch (Exception $e) {
            error_log("Erreur générale dans AppointmentController->store: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création du rendez-vous'
            ]);
        }
    }

    public function update($id) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            $query = "
                UPDATE rendez_vous 
                SET id_patient = ?, 
                    id_medecin = ?, 
                    date_rendez_vous = ?, 
                    type = ?, 
                    statut = ?, 
                    notes = ?,
                    priorite = ?,
                    id_salle = ?
                WHERE id = ?
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $data['id_patient'],
                $data['id_medecin'],
                $data['date_rendez_vous'],
                $data['type'],
                $data['statut'],
                $data['notes'],
                $data['priorite'],
                $data['id_salle'],
                $id
            ]);

            echo json_encode([
                'status' => 'success',
                'message' => 'Rendez-vous mis à jour avec succès'
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans AppointmentController->update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour du rendez-vous'
            ]);
        }
    }

    public function delete($id) {
        try {
            $query = "DELETE FROM rendez_vous WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);

            echo json_encode([
                'status' => 'success',
                'message' => 'Rendez-vous supprimé avec succès'
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans AppointmentController->delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression du rendez-vous'
            ]);
        }
    }

    public function show($id) {
        try {
            $query = "
                SELECT
                    rdv.*,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom,
                    CONCAT(u.nom, ' ', u.prenom) as medecin_nom,
                    m.specialite
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                INNER JOIN medecins m ON rdv.id_medecin = m.id
                INNER JOIN utilisateur u ON m.user_id = u.id
                WHERE rdv.id = ?
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            $appointment = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$appointment) {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Rendez-vous non trouvé'
                ]);
                return;
            }

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $appointment
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans AppointmentController->show: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération du rendez-vous'
            ]);
        }
    }

    public function getByDoctor($doctorId) {
        try {
            $query = "
                SELECT
                    rdv.*,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom,
                    p.telephone as patient_telephone,
                    p.email as patient_email
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                WHERE rdv.id_medecin = ?
                ORDER BY rdv.date_rendez_vous ASC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$doctorId]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données pour le frontend
            $formattedAppointments = array_map(function($apt) {
                return [
                    'id' => $apt['id'],
                    'date' => $apt['date_rendez_vous'],
                    'time' => date('H:i', strtotime($apt['date_rendez_vous'])),
                    'patient' => $apt['patient_nom'],
                    'type' => $apt['type'],
                    'status' => $apt['statut'],
                    'notes' => $apt['notes'],
                    'priorite' => $apt['priorite'],
                    'duree' => $apt['duree'],
                    'patient_telephone' => $apt['patient_telephone'],
                    'patient_email' => $apt['patient_email']
                ];
            }, $appointments);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $formattedAppointments
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans AppointmentController->getByDoctor: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des rendez-vous du médecin'
            ]);
        }
    }

    public function checkAvailability($doctorId) {
        try {
            $date = $_GET['date'] ?? date('Y-m-d');

            // Récupérer les rendez-vous
            $query = "
                SELECT
                    rdv.*,
                    CONCAT(p.nom, ' ', p.prenom) as patient_nom
                FROM rendez_vous rdv
                INNER JOIN patients p ON rdv.id_patient = p.id
                WHERE rdv.id_medecin = ?
                AND DATE(rdv.date_rendez_vous) = ?
                AND rdv.statut != 'annule'
                ORDER BY rdv.date_rendez_vous ASC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$doctorId, $date]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Détecter et enregistrer les conflits en base
            require_once __DIR__ . '/../models/Conflict.php';
            $conflictModel = new Conflict($this->db);
            $conflictsCreated = $conflictModel->detectAndSaveConflicts($doctorId, $date);

            // Récupérer les conflits depuis la base de données
            $activeConflicts = $conflictModel->getActiveConflicts($doctorId);

            // Filtrer les conflits pour la date demandée
            $dateConflicts = array_filter($activeConflicts, function($conflict) use ($date) {
                $conflictDate = date('Y-m-d', strtotime($conflict['rdv1_date']));
                return $conflictDate === $date;
            });

            // Récupérer les statistiques
            $stats = $conflictModel->getConflictStats($doctorId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'date' => $date,
                    'appointments' => $appointments,
                    'conflicts' => array_values($dateConflicts),
                    'total_conflicts' => count($dateConflicts),
                    'conflicts_created' => $conflictsCreated,
                    'stats' => $stats
                ]
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans AppointmentController->checkAvailability: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la vérification des disponibilités'
            ]);
        }
    }
}
?>