<template>
  <div class="admin-layout">
    <!-- Bouton hamburger pour mobile -->
    <button class="hamburger-menu" @click="toggleMobileMenu">
      <i class="fas fa-bars"></i>
    </button>

    <!-- Background gradient overlay -->
    <div class="bg-gradient"></div>

    <!-- Sidebar de navigation -->
    <nav class="admin-sidebar" :class="{ 'mobile-open': isMobileMenuOpen }">
      <div class="sidebar-header">
        <div class="logo-container">
          <div class="logo-icon">
            <i class="fas fa-clinic-medical"></i>
          </div>
          <span>Administration</span>
        </div>
      </div>

      <div class="sidebar-menu">
        <div
          v-for="(section, key) in adminComponents"
          :key="key"
          class="menu-section"
        >
          <div
            class="section-header"
            @click="toggleSection(key)"
            :class="{ active: activeSection === key }"
          >
            <i :class="['fas', 'fa-' + section.icon]"></i>
            <span>{{ section.name }}</span>
            <i
              class="fas fa-chevron-right"
              :class="{ rotated: activeSection === key }"
            ></i>
          </div>

          <div
            class="section-components"
            :class="{ expanded: activeSection === key }"
          >
            <button
              v-for="(component, compKey) in section.components"
              :key="compKey"
              class="component-button"
              :class="{ active: activeComponentKey === compKey && activeSection === key }"
              @click="loadComponent(key, compKey)"
            >
              <i class="fas fa-angle-right"></i>
              <span>{{ formatComponentName(compKey) }}</span>
            </button>
          </div>
        </div>
      </div>
      
      <div class="sidebar-user">
        <div class="user-avatar">
          <i class="fas fa-user-shield"></i>
        </div>
        <span class="user-name">{{ fullName }}</span>
        <LogoutButton class="logout-btn" />
      </div>
    </nav>

    <main class="admin-main">
      <header class="admin-header">
        <div class="header-content">
          <h1>{{ currentComponentTitle }}</h1>
          <div class="breadcrumb">
            <span>{{ activeSection ? adminComponents[activeSection].name : '' }}</span>
            <i v-if="activeComponentKey" class="fas fa-chevron-right"></i>
            <span>{{ formatComponentName(activeComponentKey) }}</span>
          </div>
        </div>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="Rechercher...">
          </div>
        </div>
      </header>

      <div class="admin-content">
        <Suspense>
          <template #default>
            <component
              :is="currentComponent"
              v-if="currentComponent"
              :patient="selectedPatient"
              @view-details="handleViewDetails"
            ></component>
          </template>
          <template #fallback>
            <div class="loading-container">
              <div class="loading-spinner">
                <div class="spinner-inner"></div>
              </div>
              <span>Chargement...</span>
            </div>
          </template>
        </Suspense>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, shallowRef, onMounted } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import { useAdminStore } from '@/stores/adminStore'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useStatsStore } from '@/stores/statsStore'
import { storeToRefs } from 'pinia'
import { adminComponents } from '@/config/adminComponents'
import LogoutButton from '@/components/shared/LogoutButton.vue'
import DashboardStats from '@/components/admin/dashboard/DashboardStats.vue'

const authStore = useAuthStore()
const { fullName } = storeToRefs(authStore)
const selectedPatient = ref(null)
const handleViewDetails = (patient) => {
  selectedPatient.value = patient;
  loadComponent('patients', 'details');
};
const isMobileMenuOpen = ref(false)
const activeSection = ref('dashboard')
const activeComponentKey = ref('stats')
const currentComponent = shallowRef(DashboardStats)

const currentComponentTitle = computed(() => {
  if (!activeSection.value || !activeComponentKey.value) return 'Tableau de bord'
  const section = adminComponents[activeSection.value]
  if (section && section.components[activeComponentKey.value]) {
    return formatComponentName(activeComponentKey.value)
  }
  return 'Tableau de bord'
})

function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

function toggleSection(sectionKey) {
  activeSection.value = activeSection.value === sectionKey ? null : sectionKey
}

async function loadComponent(sectionKey, componentKey) {
  try {
    activeSection.value = sectionKey
    activeComponentKey.value = componentKey
    const module = await adminComponents[sectionKey].components[componentKey]()
    currentComponent.value = module.default
    if (window.innerWidth <= 1024) {
      isMobileMenuOpen.value = false
    }
  } catch (error) {
    console.error('Erreur lors du chargement du composant:', error)
  }
}
function formatComponentName(name) {
  if (!name) return '';
  // Gère les noms avec points (comme "detailModal")
  return name
    .split('.')
    .map(part => part
      .split(/(?=[A-Z])/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
    )
    .join(' > ');
}
onMounted(() => {
  // Pre-fetch data for the dashboard
  const adminStore = useAdminStore()
  const appointmentStore = useAppointmentStore()
  const statsStore = useStatsStore()

  adminStore.fetchUsers()
  appointmentStore.fetchAppointments()
  statsStore.fetchDashboardStats()
})
onMounted(async () => {
  // Testez le chargement de chaque composant
  for (const [section, config] of Object.entries(adminComponents)) {
    for (const [compName, compImport] of Object.entries(config.components)) {
      try {
        const module = await compImport();
        console.log(`${section}.${compName}`, module.default);
      } catch (e) {
        console.error(`ECHEC ${section}.${compName}`, e);
      }
    }
  }
});


</script>
<style scoped>
:root {
  --primary-green: #10b981;
  --primary-green-dark: #059669;
  --primary-green-light: #34d399;
  --secondary-green: #065f46;
  --accent-green: #6ee7b7;
  --success-green: #22c55e;
  --text-primary: #0f172a;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-glass: rgba(221, 161, 161, 0.25);
  --border-color: rgba(16, 185, 129, 0.1);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

* {
  box-sizing: border-box;
}

.admin-layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg,rgb(163, 238, 204) 0%,rgb(157, 225, 157) 50%,rgb(141, 219, 165) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(19, 81, 60, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(103, 188, 157, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(39, 79, 64, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.hamburger-menu {
  display: none;
  position: fixed;
  top: 1.5rem;
  left: 1.5rem;
  z-index: 1000;
  background:  rgb(37, 145, 60);
  color: white;
  border: none;
  border-radius: 1rem;
  padding: 1rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
}

.hamburger-menu:hover {
  background: rgb(34, 169, 66);
  transform: translateY(-2px);
  box-shadow: var(--shadow-large);
}

.admin-sidebar {
  width: 280px;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-medium);
  position: relative;
}

.admin-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(186, 235, 197);
  z-index: -1;
}

.sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background:rgb(162, 232, 177)
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.3rem;
  font-weight: 800;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.logo-icon {
  margin-top: 57px;
  width: 3rem;
  height: 3rem;
  background: green;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

.logo-icon i {
  font-size: 1.4rem;
}

.sidebar-menu {
  padding: 1.5rem 1rem;
  flex-grow: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-green) transparent;
}

.sidebar-menu::-webkit-scrollbar {
  width: 6px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 3px;
}

.menu-section {
  margin-bottom: 1rem;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  gap: 1rem;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.6s;
}

.section-header:hover::before {
  left: 100%;
}

.section-header:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.05) 100%);
  color: var(--primary-green-dark);
  transform: translateX(4px);
  box-shadow: var(--shadow-soft);
}

.section-header.active {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
  color: white;
  font-weight: 700;
  box-shadow: var(--shadow-medium);
  transform: translateX(4px);
}

.section-header i:first-child {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.section-header i:last-child {
  margin-left: auto;
  font-size: 0.8rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-header i.rotated {
  transform: rotate(90deg);
}

.section-components {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding-left: 1rem;
  opacity: 0;
}

.section-components.expanded {
  max-height: 600px;
  opacity: 1;
  padding-top: 0.5rem;
}

.component-button {
  width: 100%;
  padding: 0.75rem 1.25rem 0.75rem 2.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  text-align: left;
  font-size: 0.9rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  position: relative;
  margin-bottom: 0.25rem;
}

.component-button::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-green);
  transition: width 0.3s ease;
  transform: translateY(-50%);
}

.component-button:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(52, 211, 153, 0.04) 100%);
  color: var(--primary-green-dark);
  transform: translateX(6px);
  padding-left: 3rem;
}

.component-button:hover::before {
  width: 8px;
}

.component-button.active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(52, 211, 153, 0.1) 100%);
  color: var(--primary-green);
  font-weight: 600;
  transform: translateX(6px);
  padding-left: 3rem;
  box-shadow: inset 3px 0 0 var(--primary-green);
}

.component-button.active::before {
  width: 8px;
}

.sidebar-user {
  margin-top: auto;
  padding: 2rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  text-align: center;
  background: rgb(154, 241, 163);
}

.user-avatar {
  width: 4rem;
  height: 4rem;
  background:rgb(27, 160, 40);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

.user-name {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.logout-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(52, 211, 153, 0.08) 100%);
  color: var(--primary-green-dark);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: transparent;
}

.admin-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 3rem;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-soft);
  position: relative;
}

.admin-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  z-index: -1;
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-green-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.breadcrumb i {
  font-size: 0.75rem;
  color: var(--primary-green);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 1rem;
  color: var(--text-light);
  font-size: 0.9rem;
  z-index: 1;
}

.search-box input {
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 0.75rem;
  background: rgba(255, 255, 255, 0.8);
  color: var(--text-primary);
  font-size: 0.9rem;
  width: 300px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-green);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.search-box input::placeholder {
  color: var(--text-light);
}

.admin-content {
  padding: 3rem;
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-green) transparent;
}

.admin-content::-webkit-scrollbar {
  width: 8px;
}

.admin-content::-webkit-scrollbar-track {
  background: transparent;
}

.admin-content::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 4px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 2rem;
  color: var(--text-secondary);
}

.loading-spinner {
  position: relative;
  width: 4rem;
  height: 4rem;
}

.spinner-inner {
  width: 100%;
  height: 100%;
  border: 3px solid rgba(16, 185, 129, 0.3);
  border-top: 3px solid var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container span {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-green-dark);
}

@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: -280px;
    height: 100vh;
    z-index: 100;
    width: 280px;
  }

  .admin-sidebar.mobile-open {
    transform: translateX(280px);
    box-shadow: var(--shadow-large);
  }

  .hamburger-menu {
    display: block;
  }

  .admin-main {
    width: 100%;
  }

  .admin-header {
    padding: 1.5rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
  }

  .search-box input {
    width: 100%;
  }

  .admin-content {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .admin-header {
    padding: 1rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .admin-content {
    padding: 1.5rem 1rem;
  }
}
</style>