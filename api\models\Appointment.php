<?php
class Appointment {
    private $pdo;
    
    public function __construct() {
        $this->pdo = Database::getInstance()->getConnection();
    }
    
    public function findAll() {
        try {
            error_log("Début de la récupération des rendez-vous");
            
            $query = "
                SELECT 
                    rv.*, 
                    m.nom as medecin_nom, 
                    m.prenom as medecin_prenom,
                    m.specialite, 
                    p.nom as patient_nom,
                    p.prenom as patient_prenom 
                FROM rendez_vous rv 
                JOIN medecins m ON rv.id_medecin = m.id 
                JOIN patients p ON rv.id_patient = p.id 
                ORDER BY rv.date_rendez_vous DESC
            ";
            
            error_log("Requête SQL: " . $query);
            
            $stmt = $this->pdo->query($query);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Formater les données pour le frontend
            $formatted = array_map(function($row) {
                $dateTime = new DateTime($row['date_rendez_vous']);
                
                return [
                    'id' => $row['id'],
                    'date' => $dateTime->format('Y-m-d'),
                    'time' => $dateTime->format('H:i'),
                    'patient' => [
                        'id' => $row['id_patient'],
                        'nom' => $row['patient_nom'],
                        'prenom' => $row['patient_prenom']
                    ],
                    'doctor' => [
                        'id' => $row['id_medecin'],
                        'nom' => $row['medecin_nom'],
                        'prenom' => $row['medecin_prenom'],
                        'specialite' => $row['specialite']
                    ],
                    'duree' => $row['duree'],
                    'type' => $row['type'],
                    'statut' => $row['statut'],
                    'notes' => $row['notes'],
                    'priorite' => $row['priorite']
                ];
            }, $results);
            
            error_log("Nombre de rendez-vous trouvés: " . count($formatted));
            
            return $formatted;
        } catch (PDOException $e) {
            error_log("Erreur PDO dans findAll: " . $e->getMessage());
            throw new Exception("Erreur lors de la récupération des rendez-vous: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Erreur générale dans findAll: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function create($data) {
        $stmt = $this->pdo->prepare("
            INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, duree, type, statut, notes, priorite) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        // Calculer la date de fin basée sur la durée
        $dateDebut = new DateTime($data['start_time']);
        $duree = $data['duree'] ?? 30; // 30 minutes par défaut
        
        $stmt->execute([
            $data['doctor_id'],
            $data['patient_id'],
            $data['start_time'],
            $duree,
            $data['type'] ?? 'consultation',
            'planifie',
            $data['notes'] ?? '',
            $data['priorite'] ?? 'moyenne'
        ]);
        
        return [
            'id' => $this->pdo->lastInsertId(),
            'message' => 'Rendez-vous créé avec succès'
        ];
    }
    
    public function findByMedecinAndDate($medecinId, $date) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM rendez_vous 
            WHERE id_medecin = ? AND DATE(date_rendez_vous) = ? 
            AND statut IN ('planifie', 'confirme')
        ");
        $stmt->execute([$medecinId, $date]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $data) {
        $stmt = $this->pdo->prepare("
            UPDATE rendez_vous 
            SET id_medecin = ?, id_patient = ?, date_rendez_vous = ?, 
                duree = ?, type = ?, statut = ?, notes = ?, priorite = ?
            WHERE id = ?
        ");
        
        return $stmt->execute([
            $data['doctor_id'],
            $data['patient_id'],
            $data['start_time'],
            $data['duree'] ?? 30,
            $data['type'] ?? 'consultation',
            $data['statut'] ?? 'planifie',
            $data['notes'] ?? '',
            $data['priorite'] ?? 'moyenne',
            $id
        ]);
    }
    
    public function delete($id) {
        $stmt = $this->pdo->prepare("DELETE FROM rendez_vous WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    public function getById($id) {
        $stmt = $this->pdo->prepare("
            SELECT rv.*, 
                   m.nom as nom_medecin,
                   m.prenom as prenom_medecin,
                   m.specialite, 
                   p.nom as nom_patient,
                   p.prenom as prenom_patient 
            FROM rendez_vous rv 
            JOIN medecins m ON rv.id_medecin = m.id 
            JOIN patients p ON rv.id_patient = p.id 
            WHERE rv.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>