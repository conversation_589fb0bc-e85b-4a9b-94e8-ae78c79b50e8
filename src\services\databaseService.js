import api from './api'

const databaseService = {
  /**
   * Obtenir les statistiques générales de la base de données
   */
  async getOverview() {
    try {
      console.log('📊 Récupération des statistiques de la base de données...')
      const response = await api.get('/database/overview')
      console.log('✅ Statistiques récupérées depuis la vraie base de données:', response.data)

      // Marquer la source comme base de données réelle
      if (response.data && response.data.data) {
        response.data.source = 'database'
      }

      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback avec des données simulées seulement en cas d'erreur grave
      if (error.response?.status === 404 || error.response?.status === 500) {
        console.log('📝 Mode fallback: données simulées (API non disponible)')
        return this.getFallbackOverview()
      }
      throw error
    }
  },

  /**
   * Obtenir la liste des tables
   */
  async getTables() {
    try {
      console.log('📋 Récupération de la liste des tables...')
      const response = await api.get('/database/tables')
      console.log('✅ Tables récupérées depuis la vraie base de données:', response.data)

      // Marquer la source comme base de données réelle
      if (response.data && response.data.data) {
        response.data.source = 'database'
      }

      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des tables:', error)
      if (error.response?.status === 404 || error.response?.status === 500) {
        console.log('📝 Mode fallback: tables simulées (API non disponible)')
        return this.getFallbackTables()
      }
      throw error
    }
  },

  /**
   * Obtenir la structure d'une table
   */
  async getTableStructure(tableName) {
    try {
      console.log(`🔍 Récupération de la structure de la table ${tableName}...`)
      const response = await api.get(`/database/tables/${tableName}/structure`)
      console.log('✅ Structure récupérée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de la structure:', error)
      throw error
    }
  },

  /**
   * Obtenir les données d'une table
   */
  async getTableData(tableName, page = 1, limit = 50) {
    try {
      console.log(`📄 Récupération des données de la table ${tableName}...`)
      const response = await api.get(`/database/tables/${tableName}/data`, {
        params: { page, limit }
      })
      console.log('✅ Données récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des données:', error)
      throw error
    }
  },

  /**
   * Optimiser la base de données
   */
  async optimize() {
    try {
      console.log('⚡ Optimisation de la base de données...')
      const response = await api.post('/database/optimize')
      console.log('✅ Optimisation terminée:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de l\'optimisation:', error)
      throw error
    }
  },

  /**
   * Créer une sauvegarde (simulation)
   */
  async createBackup() {
    try {
      console.log('💾 Création d\'une sauvegarde...')
      // Simulation d'une sauvegarde
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const backupName = `backup_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.sql`
      
      return {
        status: 'success',
        data: {
          backup_name: backupName,
          size_mb: Math.round(Math.random() * 50 + 10),
          created_at: new Date().toISOString(),
          message: 'Sauvegarde créée avec succès'
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la création de la sauvegarde:', error)
      throw error
    }
  },

  /**
   * Vérifier l'intégrité de la base de données
   */
  async checkIntegrity() {
    try {
      console.log('🔍 Vérification de l\'intégrité...')
      // Simulation d'une vérification
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      return {
        status: 'success',
        data: {
          tables_checked: 8,
          errors_found: 0,
          warnings: 1,
          issues: [
            {
              type: 'warning',
              table: 'rendez_vous',
              message: 'Index manquant sur la colonne date_rendez_vous'
            }
          ],
          message: 'Vérification terminée'
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la vérification:', error)
      throw error
    }
  },

  /**
   * Données de fallback pour les statistiques
   */
  getFallbackOverview() {
    return {
      status: 'success',
      data: {
        tables: {
          utilisateur: { label: 'Utilisateurs', count: 25 },
          medecins: { label: 'Médecins', count: 8 },
          patients: { label: 'Patients', count: 150 },
          rendez_vous: { label: 'Rendez-vous', count: 342 },
          conflits: { label: 'Conflits', count: 12 },
          types_conflits: { label: 'Types de conflits', count: 10 },
          creneaux_suggeres: { label: 'Créneaux suggérés', count: 45 },
          types_creneaux: { label: 'Types de créneaux', count: 5 }
        },
        database: {
          name: 'agenda_medical',
          size_mb: 15.7,
          table_count: 8
        },
        performance: {
          recent_appointments: 23,
          recent_conflicts: 3,
          active_users: 158
        }
      },
      source: 'fallback'
    }
  },

  /**
   * Données de fallback pour les tables
   */
  getFallbackTables() {
    return {
      status: 'success',
      data: [
        {
          table_name: 'utilisateur',
          table_rows: 25,
          size_mb: 0.5,
          table_comment: 'Table des utilisateurs du système',
          create_time: '2024-01-15 10:30:00',
          update_time: '2024-06-18 14:22:00'
        },
        {
          table_name: 'medecins',
          table_rows: 8,
          size_mb: 0.2,
          table_comment: 'Informations des médecins',
          create_time: '2024-01-15 10:35:00',
          update_time: '2024-06-10 09:15:00'
        },
        {
          table_name: 'patients',
          table_rows: 150,
          size_mb: 2.1,
          table_comment: 'Informations des patients',
          create_time: '2024-01-15 10:40:00',
          update_time: '2024-06-18 16:45:00'
        },
        {
          table_name: 'rendez_vous',
          table_rows: 342,
          size_mb: 5.8,
          table_comment: 'Rendez-vous médicaux',
          create_time: '2024-01-15 11:00:00',
          update_time: '2024-06-18 17:30:00'
        },
        {
          table_name: 'conflits',
          table_rows: 12,
          size_mb: 0.3,
          table_comment: 'Conflits de planning',
          create_time: '2024-02-01 14:20:00',
          update_time: '2024-06-18 12:10:00'
        },
        {
          table_name: 'types_conflits',
          table_rows: 10,
          size_mb: 0.1,
          table_comment: 'Types de conflits configurables',
          create_time: '2024-02-01 14:25:00',
          update_time: '2024-06-15 11:30:00'
        }
      ],
      source: 'fallback'
    }
  },

  /**
   * Formater la taille en octets
   */
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  },

  /**
   * Formater une date
   */
  formatDate(dateString) {
    if (!dateString) return 'N/A'
    
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  /**
   * Obtenir la couleur selon le type de table
   */
  getTableColor(tableName) {
    const colors = {
      'utilisateur': '#3498db',
      'medecins': '#2ecc71',
      'patients': '#e74c3c',
      'rendez_vous': '#f39c12',
      'conflits': '#e67e22',
      'types_conflits': '#9b59b6',
      'creneaux_suggeres': '#1abc9c',
      'types_creneaux': '#34495e'
    }
    
    return colors[tableName] || '#95a5a6'
  }
}

export default databaseService
