<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Historique du Patient</h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="filters">
        <div class="filter-group">
          <label>Type d'événement :</label>
          <select v-model="selectedType">
            <option value="">Tous</option>
            <option value="appointment">Rendez-vous</option>
            <option value="prescription">Prescriptions</option>
            <option value="document">Documents</option>
            <option value="allergy">Allergies</option>
          </select>
        </div>
        <div class="filter-group">
          <label>Période :</label>
          <select v-model="selectedPeriod">
            <option value="all">Tout</option>
            <option value="month">Dernier mois</option>
            <option value="3months">3 derniers mois</option>
            <option value="6months">6 derniers mois</option>
            <option value="year">Dernière année</option>
          </select>
        </div>
      </div>

      <div class="timeline">
        <div v-if="loading" class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          Chargement de l'historique...
        </div>
        <div v-else-if="error" class="error">
          <i class="fas fa-exclamation-circle"></i>
          {{ error }}
        </div>
        <div v-else-if="filteredEvents.length === 0" class="no-events">
          <i class="fas fa-info-circle"></i>
          Aucun événement trouvé pour cette période
        </div>
        <div v-else class="timeline-events">
          <div v-for="event in filteredEvents" :key="event.id" class="timeline-event">
            <div class="event-icon" :class="event.type">
              <i :class="getEventIcon(event.type)"></i>
            </div>
            <div class="event-content">
              <div class="event-header">
                <h3>{{ event.title }}</h3>
                <span class="event-date">{{ formatDate(event.date) }}</span>
              </div>
              <p class="event-description">{{ event.description }}</p>
              <div v-if="event.details" class="event-details">
                <template v-if="event.type === 'appointment'">
                  <p><strong>Médecin :</strong> {{ event.details.doctor }}</p>
                  <p><strong>Spécialité :</strong> {{ event.details.specialite }}</p>
                  <p><strong>Statut :</strong> {{ event.details.status }}</p>
                  <p><strong>Type :</strong> {{ event.details.type }}</p>
                  <p><strong>Durée :</strong> {{ event.details.duree }} minutes</p>
                </template>
                <template v-else-if="event.type === 'prescription'">
                  <p><strong>Médicaments :</strong> {{ event.details.medicaments }}</p>
                  <p><strong>Posologie :</strong> {{ event.details.posologie }}</p>
                  <p><strong>Durée :</strong> {{ event.details.duree }}</p>
                  <p><strong>Médecin :</strong> {{ event.details.doctor }}</p>
                </template>
                <template v-else-if="event.type === 'document'">
                  <p><strong>Type :</strong> {{ event.details.type }}</p>
                  <p><strong>Taille :</strong> {{ formatFileSize(event.details.taille) }}</p>
                  <button class="btn-link" @click="downloadDocument(event.details.id)">
                    <i class="fas fa-download"></i> Télécharger
                  </button>
                </template>
                <template v-else-if="event.type === 'allergy'">
                  <p><strong>Allergie :</strong> {{ event.details.allergie }}</p>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { formatDate } from '@/utils/dateHelpers'
import { patientService } from '@/services/api'

const props = defineProps({
  patient: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

const history = ref([])
const loading = ref(false)
const error = ref(null)
const selectedType = ref('')
const selectedPeriod = ref('all')

const filteredEvents = computed(() => {
  return history.value.filter(event => {
    const matchesType = !selectedType.value || event.type === selectedType.value
    const matchesPeriod = filterByPeriod(event.date, selectedPeriod.value)
    return matchesType && matchesPeriod
  }).sort((a, b) => b.date - a.date)
})

const loadHistory = async () => {
  if (!props.patient?.id) return
  
  loading.value = true
  error.value = null
  try {
    const data = await patientService.getPatientHistory(props.patient.id)
    history.value = data.map(item => ({
      ...item,
      date: new Date(item.date),
      type: item.type || 'autre'
    })).sort((a, b) => b.date - a.date)
  } catch (err) {
    console.error('Erreur chargement historique:', err)
    error.value = 'Erreur lors du chargement de l\'historique'
  } finally {
    loading.value = false
  }
}

const getEventIcon = (type) => {
  switch (type.toLowerCase()) {
    case 'appointment':
      return 'fas fa-calendar-alt'
    case 'prescription':
      return 'fas fa-prescription'
    case 'document':
      return 'fas fa-file-alt'
    case 'allergy':
      return 'fas fa-allergies'
    default:
      return 'fas fa-info-circle'
  }
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const filterByPeriod = (date, period) => {
  if (period === 'all') return true
  
  const eventDate = new Date(date)
  const now = new Date()
  const periods = {
    month: 30,
    '3months': 90,
    '6months': 180,
    year: 365
  }
  
  const diffDays = Math.floor((now - eventDate) / (1000 * 60 * 60 * 24))
  return diffDays <= periods[period]
}

const downloadDocument = async (documentId) => {
  try {
    const blob = await patientService.downloadDocument(documentId)
    const url = window.URL.createObjectURL(new Blob([blob]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `document-${documentId}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  } catch (err) {
    console.error('Erreur téléchargement:', err)
    error.value = err.response?.data?.message || 'Erreur lors du téléchargement du document'
  }
}

onMounted(() => {
  loadHistory()
})

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
}

.timeline {
  position: relative;
  padding: 1rem 0;
}

.timeline-event {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.event-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.event-icon.appointment { background: #3b82f6; }
.event-icon.prescription { background: #10b981; }
.event-icon.document { background: #f59e0b; }
.event-icon.allergy { background: #ef4444; }

.event-content {
  flex: 1;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.event-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #1e293b;
}

.event-date {
  color: #64748b;
  font-size: 0.9rem;
}

.event-description {
  color: #475569;
  margin: 0.5rem 0;
}

.event-details {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e2e8f0;
}

.event-details p {
  margin: 0.25rem 0;
  color: #475569;
}

.loading, .error, .no-events {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.loading i, .error i, .no-events i {
  margin-right: 0.5rem;
}

.btn-link {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  padding: 0;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-link:hover {
  text-decoration: underline;
}

@media (max-width: 640px) {
  .filters {
    flex-direction: column;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    width: 100%;
  }
}
</style> 