{"name": "agenda_medical", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "dev:debug": "vite --port 3000 --debug", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:unit": "vitest --environment jsdom", "api": "php -S localhost:8000 -t api api/index.php", "start": "npm run api"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@popperjs/core": "^2.11.8", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bootstrap": "^5.3.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^4.21.2", "mysql2": "^3.14.1", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "@vue/test-utils": "^2.4.6", "vite": "^5.0.11", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.2.1"}}