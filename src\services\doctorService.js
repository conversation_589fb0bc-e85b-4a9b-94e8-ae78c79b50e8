import api from './api';

export const doctorService = {
    /**
     * Récupère tous les médecins
     */
    async getAllDoctors() {
        try {
            const response = await api.get('/doctors');
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération des médecins:', error);
            throw error;
        }
    },

    /**
     * Récupère un médecin par son ID
     */
    async getDoctorById(id) {
        try {
            const response = await api.get(`/doctors/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erreur lors de la récupération du médecin ${id}:`, error);
            throw error;
        }
    },

    /**
     * Crée un nouveau médecin
     */
    async createDoctor(doctorData) {
    try {
        // Validation côté client
        const requiredFields = ['nom', 'prenom', 'email', 'specialite', 'password'];
        const missingFields = requiredFields.filter(field => !doctorData[field]);
        
        if (missingFields.length > 0) {
            throw new Error(`Champs requis manquants: ${missingFields.join(', ')}`);
        }

        // Formatage des données
        const payload = {
            nom: doctorData.nom.trim(),
            prenom: doctorData.prenom.trim(),
            email: doctorData.email.trim().toLowerCase(),
            specialite: doctorData.specialite.trim(),
            telephone: doctorData.telephone?.trim() || null,
            password: doctorData.password
        };

        const response = await api.post('/doctors', payload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        // Vérification de la réponse
        if (!response.data) {
            throw new Error('Réponse serveur vide');
        }

        if (response.data.status === 'error') {
            const error = new Error(response.data.message || 'Erreur serveur');
            error.details = response.data.errors || {};
            throw error;
        }

        return response.data;

    } catch (error) {
        console.error('Erreur détaillée:', {
            config: error.config,
            response: error.response?.data,
            message: error.message
        });

        if (error.response?.status === 400) {
            // Traitement spécifique des erreurs 400
            const serverMessage = error.response.data?.message || 'Données invalides';
            const serverErrors = error.response.data?.errors || {};
            
            const err = new Error(serverMessage);
            err.details = serverErrors;
            throw err;
        }

        throw error;
    }
},

    /**
     * Met à jour un médecin
     */
    async updateDoctor(id, doctorData) {
        try {
            const response = await api.put(`/doctors/${id}`, doctorData);
            
            if (!response.data) {
                throw new Error('Réponse serveur invalide');
            }

            if (response.data.status !== 'success') {
                const error = new Error(response.data.message || 'Erreur lors de la mise à jour');
                error.details = {
                    field: response.data.field,
                    code: response.data.code,
                    errors: response.data.errors
                };
                throw error;
            }
            
            return response.data;
            
        } catch (error) {
            console.error('Erreur mise à jour médecin:', error);
            
            if (error.response?.status === 409) {
                const err = new Error('Cet email est déjà utilisé par un autre médecin');
                err.details = {
                    field: 'email',
                    code: 'EMAIL_EXISTS',
                    message: 'Cet email est déjà utilisé par un autre médecin'
                };
                throw err;
            }
            
            if (error.response?.data) {
                const err = new Error(error.response.data.message || 'Erreur serveur');
                err.details = {
                    field: error.response.data.field,
                    errors: error.response.data.errors
                };
                throw err;
            }
            
            throw new Error(error.message || 'Erreur lors de la mise à jour du médecin');
        }
    },

    /**
     * Supprime un médecin
     */
    async deleteDoctor(id) {
        try {
            const response = await api.delete(`/doctors/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erreur lors de la suppression du médecin ${id}:`, error);
            throw error;
        }
    },

    /**
     * Récupère les horaires de travail d'un médecin
     */
    async getWorkingHours(id) {
        try {
            const response = await api.get(`/doctors/${id}/working-hours`);
            return response.data;
        } catch (error) {
            console.error(`Erreur lors de la récupération des horaires du médecin ${id}:`, error);
            throw error;
        }
    },

    /**
     * Met à jour les horaires de travail d'un médecin
     */
    async updateWorkingHours(id, hours) {
        try {
            const response = await api.put(`/doctors/${id}/working-hours`, hours);
            return response.data;
        } catch (error) {
            console.error(`Erreur lors de la mise à jour des horaires du médecin ${id}:`, error);
            throw error;
        }
    },

    /**
     * Vérifie la disponibilité d'un médecin
     */
    async checkAvailability(id, dateTime) {
        try {
            const response = await api.get(`/doctors/${id}/availability`, {
                params: { datetime: dateTime }
            });
            return response.data;
        } catch (error) {
            console.error(`Erreur lors de la vérification de la disponibilité du médecin ${id}:`, error);
            throw error;
        }
    },

    /**
     * Récupère les créneaux disponibles d'un médecin
     */
    async getAvailableSlots(id, date) {
        try {
          const response = await api.get(`/doctors/${id}/available-slots`, {
            params: { date }
          });
          
          console.log('Raw API response:', response.data); // Debug logging
          
          // Handle multiple possible response formats
          let slots = [];
          
          // Case 1: Direct array response
          if (Array.isArray(response.data)) {
            slots = response.data;
          }
          // Case 2: { slots: [...] } format
          else if (response.data?.slots && Array.isArray(response.data.slots)) {
            slots = response.data.slots;
          }
          // Case 3: { data: [...] } format
          else if (response.data?.data && Array.isArray(response.data.data)) {
            slots = response.data.data;
          }
          // Case 4: { success: true, data: [...] } format
          else if (response.data?.success && Array.isArray(response.data.data)) {
            slots = response.data.data;
          }
          else {
            throw new Error(`Invalid slots data format. Received: ${JSON.stringify(response.data)}`);
          }
          
          // Normalize slot objects
          const normalizedSlots = slots.map(slot => {
            // Handle string-only slots ("09:00")
            if (typeof slot === 'string') {
              return { time: slot };
            }
            // Handle object slots ({time: "09:00", available: true})
            return {
              time: slot.time || slot.start || slot.slot,
              available: slot.available !== false,
              // Add other necessary properties
            };
          });
          
          return {
            success: true,
            data: normalizedSlots
          };
          
        } catch (error) {
          console.error(`Error fetching slots for doctor ${id}:`, error);
          throw new Error(error.response?.data?.message || 
                        'Failed to fetch available slots');
        }
      }
}; 