<template>
  <div class="system-health">
    <h2>État du système</h2>
    <div v-if="loading" class="loading">Vérification en cours...</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <ul v-else>
      <li>
        <span class="label">API&nbsp;:</span>
        <span :class="statusClass(health.api)">{{ health.api ? 'OK' : 'Hors service' }}</span>
      </li>
      <li>
        <span class="label">Base de données&nbsp;:</span>
        <span :class="statusClass(health.db)">{{ health.db ? 'OK' : 'Hors service' }}</span>
      </li>
      <li>
        <span class="label">Stockage&nbsp;:</span>
        <span :class="statusClass(health.storage)">{{ health.storage ? 'OK' : 'Hors service' }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
// Remplace cette ligne par ton vrai appel API si besoin
// import apiClient from '@/services/api'

const loading = ref(true)
const error = ref('')
const health = ref({
  api: false,
  db: false,
  storage: false
})

function statusClass(ok) {
  return ok ? 'ok' : 'down'
}

async function fetchHealth() {
  loading.value = true
  error.value = ''
  try {
    // Exemple de données simulées (remplace par ton vrai endpoint)
    // const res = await apiClient.get('/system/health')
    // health.value = res.data
    await new Promise(r => setTimeout(r, 800))
    health.value = { api: true, db: true, storage: false }
  } catch (e) {
    error.value = "Impossible de vérifier l'état du système."
  } finally {
    loading.value = false
  }
}

onMounted(fetchHealth)
</script>

<style scoped>
.system-health {
  background: #f8fafc;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px #2563eb22;
  max-width: 400px;
  margin: 0 auto;
}
h2 {
  margin-bottom: 1rem;
  color: #2563eb;
  font-size: 1.2rem;
}
ul {
  list-style: none;
  padding: 0;
}
li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.7em 0;
  border-bottom: 1px solid #e2e8f0;
}
li:last-child {
  border-bottom: none;
}
.label {
  font-weight: 500;
  color: #334155;
}
.ok {
  color: #22c55e;
  font-weight: bold;
}
.down {
  color: #dc2626;
  font-weight: bold;
}
.loading, .error {
  text-align: center;
  margin: 1.5em 0;
}
.error {
  color: #dc2626;
}
</style>
