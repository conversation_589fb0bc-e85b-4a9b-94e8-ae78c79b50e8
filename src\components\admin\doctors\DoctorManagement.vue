<template>
  <div class="doctor-management">
    <div class="section-header">
      <h2>Gestion des Médecins</h2>
      <button @click="addDoctor" class="btn btn-primary">
        <i class="fas fa-plus"></i> Ajouter un Médecin
      </button>
    </div>

    <div v-if="loading" class="loading">
      <i class="fas fa-spinner fa-spin"></i> Chargement des médecins...
    </div>

    <div v-else-if="error" class="error-message">
      <i class="fas fa-exclamation-triangle"></i> {{ error }}
    </div>

    <div v-else class="doctors-list">
      <table class="table">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Prénom</th>
            <th>Spécialité</th>
            <th>Email</th>
            <th>Téléphone</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="doctor in doctors" :key="doctor.id">
            <td>{{ doctor.nom }}</td>
            <td>{{ doctor.prenom }}</td>
            <td>{{ doctor.specialite }}</td>
            <td>{{ doctor.email }}</td>
            <td>{{ doctor.telephone || '-' }}</td>
            <td class="actions">
              <button @click="viewSchedule(doctor)" class="btn-icon" title="Voir le planning">
                <i class="fas fa-calendar-alt"></i>
              </button>
              <button @click="editDoctor(doctor)" class="btn-icon" title="Modifier">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="confirmDelete(doctor)" class="btn-icon delete" title="Supprimer">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Modal Ajout/Modification Médecin -->
    <div v-if="showAddDoctorModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ selectedDoctor ? 'Modifier le Médecin' : 'Ajouter un Médecin' }}</h3>
          <button @click="closeModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveDoctor" class="doctor-form">
            <div v-if="formError" class="error-message">
              <i class="fas fa-exclamation-circle"></i> {{ formError }}
            </div>
            
            <div class="form-group">
              <label for="nom">Nom *</label>
              <input
                type="text"
                id="nom"
                v-model="formData.nom"
                required
                class="form-control"
                :class="{ 'error': formErrors.nom }"
              />
              <span v-if="formErrors.nom" class="error-text">{{ formErrors.nom }}</span>
            </div>

            <div class="form-group">
              <label for="prenom">Prénom *</label>
              <input
                type="text"
                id="prenom"
                v-model="formData.prenom"
                required
                class="form-control"
                :class="{ 'error': formErrors.prenom }"
              />
              <span v-if="formErrors.prenom" class="error-text">{{ formErrors.prenom }}</span>
            </div>

            <div class="form-group">
              <label for="email">Email *</label>
              <input
                type="email"
                id="email"
                v-model="formData.email"
                required
                :disabled="!!selectedDoctor"
                class="form-control"
                :class="{ 'error': formErrors.email }"
              />
              <span v-if="formErrors.email" class="error-text">{{ formErrors.email }}</span>
            </div>

            <div class="form-group">
              <label for="specialite">Spécialité *</label>
              <input
                type="text"
                id="specialite"
                v-model="formData.specialite"
                required
                class="form-control"
                :class="{ 'error': formErrors.specialite }"
              />
              <span v-if="formErrors.specialite" class="error-text">{{ formErrors.specialite }}</span>
            </div>

            <div class="form-group">
              <label for="telephone">Téléphone</label>
              <input
                type="tel"
                id="telephone"
                v-model="formData.telephone"
                class="form-control"
                :class="{ 'error': formErrors.telephone }"
                placeholder="06 12 34 56 78"
              />
              <span v-if="formErrors.telephone" class="error-text">{{ formErrors.telephone }}</span>
            </div>

            <div class="form-group">
              <label for="password">Mot de passe *</label>
              <input
                type="password"
                id="password"
                v-model="formData.password"
                required
                class="form-control"
                :class="{ 'error': formErrors.password }"
                placeholder="Entrez le mot de passe"
              />
              <span v-if="formErrors.password" class="error-text">{{ formErrors.password }}</span>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn btn-primary" :disabled="saving">
                <i v-if="saving" class="fas fa-spinner fa-spin"></i>
                {{ saving ? 'Enregistrement...' : (selectedDoctor ? 'Mettre à jour' : 'Ajouter') }}
              </button>
              <button type="button" @click="closeModal" class="btn btn-secondary">
                Annuler
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Modal Planning -->
    <ScheduleModal
      v-if="showScheduleModal"
      :doctor="selectedDoctor"
      @close="closeScheduleModal"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useDoctorStore } from '@/stores/doctorStore'
import { storeToRefs } from 'pinia'
import ScheduleModal from '@/components/admin/doctors/ScheduleModal.vue'

const doctorStore = useDoctorStore()
const { doctors, loading, error } = storeToRefs(doctorStore)

// États réactifs
const showAddDoctorModal = ref(false)
const showScheduleModal = ref(false)
const selectedDoctor = ref(null)
const saving = ref(false)
const formError = ref('')
const formErrors = ref({})

const formData = ref({
  nom: '',
  prenom: '',
  email: '',
  specialite: '',
  telephone: '',
  password: ''
})

// Chargement initial
onMounted(async () => {
  await loadDoctors()
})

// Méthodes
const loadDoctors = async () => {
  try {
    await doctorStore.fetchDoctors()
  } catch (err) {
    console.error('Erreur chargement médecins:', err)
    formError.value = 'Erreur lors du chargement des médecins'
  }
}

const addDoctor = () => {
  resetForm()
  showAddDoctorModal.value = true
}

const editDoctor = (doctor) => {
  selectedDoctor.value = doctor
  formData.value = { ...doctor }
  showAddDoctorModal.value = true
}

const viewSchedule = (doctor) => {
  selectedDoctor.value = doctor
  showScheduleModal.value = true
}

const resetForm = () => {
  formData.value = {
    nom: '',
    prenom: '',
    email: '',
    specialite: '',
    telephone: '',
    password: ''
  }
  formError.value = ''
  formErrors.value = {}
  selectedDoctor.value = null
}

const closeModal = () => {
  showAddDoctorModal.value = false
  resetForm()
}

const closeScheduleModal = () => {
  showScheduleModal.value = false
  selectedDoctor.value = null
}

const validateForm = () => {
  formErrors.value = {};
  let isValid = true;

  // Validation de l'email
  if (!formData.value.email?.trim()) {
    formErrors.value.email = 'L\'email est requis';
    isValid = false;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email.trim())) {
    formErrors.value.email = 'Format d\'email invalide';
    isValid = false;
  }

  // Validation des autres champs
  if (!formData.value.nom?.trim()) {
    formErrors.value.nom = 'Le nom est requis';
    isValid = false;
  }

  if (!formData.value.prenom?.trim()) {
    formErrors.value.prenom = 'Le prénom est requis';
    isValid = false;
  }

  if (!formData.value.specialite?.trim()) {
    formErrors.value.specialite = 'La spécialité est requise';
    isValid = false;
  }

  // Validation du mot de passe
  if (!formData.value.password?.trim()) {
    formErrors.value.password = 'Le mot de passe est requis';
    isValid = false;
  } else if (formData.value.password.length < 8) {
    formErrors.value.password = 'Le mot de passe doit contenir au moins 8 caractères';
    isValid = false;
  }

  // Validation optionnelle du téléphone
  if (formData.value.telephone && !/^0[1-9]\d{8}$/.test(formData.value.telephone.replace(/\s/g, ''))) {
    formErrors.value.telephone = 'Le téléphone doit contenir 10 chiffres (ex: 0612345678)';
    isValid = false;
  }

  return isValid;
};

const confirmDelete = async (doctor) => {
  if (confirm(`Êtes-vous sûr de vouloir supprimer le Dr ${doctor.nom} ${doctor.prenom} ?`)) {
    try {
      await doctorStore.deleteDoctor(doctor.id)
      showNotification('success', 'Médecin supprimé avec succès')
      await loadDoctors()
    } catch (err) {
      console.error('Erreur suppression:', err)
      showNotification('error', err.message || 'Erreur lors de la suppression')
    }
  }
}

const saveDoctor = async () => {
  // Validation du formulaire
  if (!validateForm()) {
    formError.value = 'Veuillez corriger les erreurs dans le formulaire'
    return
  }

  try {
    saving.value = true
    formError.value = ''
    formErrors.value = {} // Réinitialiser les erreurs de formulaire

    // Déterminer l'action (création ou mise à jour)
    const isUpdate = !!selectedDoctor.value
    const action = isUpdate 
      ? doctorStore.updateDoctor(selectedDoctor.value.id, formData.value)
      : doctorStore.createDoctor(formData.value)

    // Exécuter l'action
    const result = await action

    // Vérification de la structure de la réponse
    if (!result || typeof result !== 'object') {
      throw new Error('Réponse invalide du serveur')
    }

    if (result.success) {
      // Succès
      showNotification('success', 
        isUpdate 
          ? 'Médecin mis à jour avec succès' 
          : 'Médecin créé avec succès'
      )
      closeModal()
      await loadDoctors()
    } else {
      // Erreur métier (validation, etc.)
      handleBusinessError(result)
    }
  } catch (err) {
    // Gestion des erreurs techniques
    handleTechnicalError(err)
  } finally {
    saving.value = false
  }
}

// Gestion des erreurs métier
const handleBusinessError = (result) => {
  formError.value = result.error || 'Erreur lors de la sauvegarde'
  
  if (result.errors) {
    formErrors.value = result.errors
  }
  
  // Log supplémentaire pour le débogage
  console.warn('Erreur métier:', {
    error: result.error,
    errors: result.errors
  })
}

// Gestion des erreurs techniques
const handleTechnicalError = (err) => {
  console.error('Erreur technique:', err)
  
  // Message d'erreur plus informatif
  let errorMessage = 'Une erreur est survenue'
  
  if (err.response) {
    // Erreur HTTP
    errorMessage = `Erreur serveur (${err.response.status})`
    if (err.response.status === 500) {
      errorMessage = 'Erreur interne du serveur'
    }
  } else if (err.request) {
    // Pas de réponse du serveur
    errorMessage = 'Pas de réponse du serveur'
  } else if (err.message) {
    // Erreur de configuration de la requête
    errorMessage = err.message
  }
  
  formError.value = errorMessage
}
// Utilitaires
const showNotification = (type, message) => {
  // À remplacer par votre système de notification (Toast, etc.)
  console.log(`${type}: ${message}`)
  alert(`${type}: ${message}`) // Temporaire
}
</script>

<style scoped>
.doctor-management {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.loading,
.error-message {
  padding: 1rem;
  text-align: center;
  margin: 1rem 0;
  border-radius: 0.375rem;
}

.loading {
  background-color: #f0f9ff;
  color: #0369a1;
}

.error-message {
  background-color: #fef2f2;
  color: #b91c1c;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table th,
.table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.table th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #334155;
}

.table tr:hover td {
  background-color: #f8fafc;
}

.actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.btn-icon {
  padding: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s;
  color: #64748b;
  font-size: 1rem;
}

.btn-icon:hover {
  color: #334155;
  background-color: #f1f5f9;
}

.btn-icon.delete:hover {
  color: #dc2626;
  background-color: #fee2e2;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #1e293b;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #64748b;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #334155;
}

.modal-body {
  padding: 1.25rem;
}

.doctor-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #334155;
  font-size: 0.875rem;
}

.form-control {
  padding: 0.625rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.form-control.error {
  border-color: #f87171;
}

.error-text {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.error-message {
  padding: 0.75rem;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  color: #b91c1c;
  font-size: 0.875rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.btn {
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border: none;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #e2e8f0;
  color: #334155;
  border: none;
}

.btn-secondary:hover {
  background-color: #cbd5e1;
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .table {
    display: block;
    overflow-x: auto;
  }
  
  .modal-content {
    max-width: 95%;
  }
}
</style>