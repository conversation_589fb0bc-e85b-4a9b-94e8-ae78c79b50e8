import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import suggestedSlotService from '@/services/suggestedSlotService.js'

export const useSuggestedSlotStore = defineStore('suggestedSlot', () => {
  // État
  const slots = ref([])
  const stats = ref({
    total: 0,
    acceptes: 0,
    refuses: 0,
    en_attente: 0,
    expires: 0,
    score_moyen: 0,
    score_acceptes: 0
  })
  const loading = ref(false)
  const error = ref(null)
  const lastFetch = ref(null)

  // Getters
  const slotCount = computed(() => slots.value.length)
  
  const pendingSlots = computed(() => 
    slots.value.filter(slot => slot.accepte === null && new Date(slot.expire_le) > new Date())
  )
  
  const acceptedSlots = computed(() => 
    slots.value.filter(slot => slot.accepte === 1)
  )
  
  const refusedSlots = computed(() => 
    slots.value.filter(slot => slot.accepte === 0)
  )
  
  const expiredSlots = computed(() => 
    slots.value.filter(slot => new Date(slot.expire_le) < new Date())
  )

  const slotsByStatus = computed(() => {
    return slots.value.reduce((groups, slot) => {
      const status = slot.statut || 'en_attente'
      if (!groups[status]) {
        groups[status] = []
      }
      groups[status].push(slot)
      return groups
    }, {})
  })

  const upcomingSlots = computed(() => {
    const now = new Date()
    return slots.value
      .filter(slot => new Date(slot.date_heure_suggeree) > now && slot.accepte !== 0)
      .sort((a, b) => new Date(a.date_heure_suggeree) - new Date(b.date_heure_suggeree))
  })

  // Actions
  const setLoading = (value) => {
    loading.value = value
  }

  const setError = (errorMessage) => {
    error.value = errorMessage
    console.error('Store: Erreur créneaux suggérés:', errorMessage)
  }

  const clearError = () => {
    error.value = null
  }

  // Charger les créneaux suggérés d'un médecin
  const loadSlotsByDoctor = async (medecinId, includeExpired = false) => {
    try {
      setLoading(true)
      clearError()
      
      console.log('Store: Chargement des créneaux suggérés...', { medecinId, includeExpired })
      const response = await suggestedSlotService.getByDoctor(medecinId, includeExpired)
      
      if (response.status === 'success') {
        slots.value = response.data || []
        lastFetch.value = new Date()
        console.log('Store: Créneaux suggérés chargés:', slots.value.length)
      } else {
        throw new Error(response.message || 'Erreur lors du chargement')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors du chargement des créneaux suggérés')
      slots.value = []
    } finally {
      setLoading(false)
    }
  }

  // Charger les créneaux suggérés d'un patient
  const loadSlotsByPatient = async (patientId, includeExpired = false) => {
    try {
      setLoading(true)
      clearError()
      
      console.log('Store: Chargement des créneaux suggérés du patient...', { patientId, includeExpired })
      const response = await suggestedSlotService.getByPatient(patientId, includeExpired)
      
      if (response.status === 'success') {
        slots.value = response.data || []
        lastFetch.value = new Date()
        console.log('Store: Créneaux suggérés du patient chargés:', slots.value.length)
      } else {
        throw new Error(response.message || 'Erreur lors du chargement')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors du chargement des créneaux suggérés')
      slots.value = []
    } finally {
      setLoading(false)
    }
  }

  // Charger les statistiques
  const loadStats = async (medecinId = null) => {
    try {
      console.log('Store: Chargement des statistiques...', { medecinId })
      const response = await suggestedSlotService.getStats(medecinId)
      
      if (response.status === 'success') {
        stats.value = response.data || stats.value
        console.log('Store: Statistiques chargées:', stats.value)
      } else {
        throw new Error(response.message || 'Erreur lors du chargement des stats')
      }
    } catch (err) {
      console.error('Store: Erreur stats:', err)
    }
  }

  // Créer un nouveau créneau suggéré
  const createSlot = async (slotData) => {
    try {
      setLoading(true)
      clearError()
      
      console.log('Store: Création du créneau suggéré...', slotData)
      const response = await suggestedSlotService.create(slotData)
      
      if (response.status === 'success') {
        const newSlot = response.data
        slots.value.push(newSlot)
        
        // Recharger les stats
        await loadStats(slotData.id_medecin)
        
        console.log('Store: Créneau suggéré créé:', newSlot)
        return { success: true, data: newSlot }
      } else {
        throw new Error(response.message || 'Erreur lors de la création')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de la création du créneau suggéré')
      return { success: false, error: err.message }
    } finally {
      setLoading(false)
    }
  }

  // Mettre à jour l'acceptation d'un créneau
  const updateAcceptance = async (slotId, accepted, userId = null) => {
    try {
      setLoading(true)
      clearError()
      
      console.log('Store: Mise à jour de l\'acceptation...', { slotId, accepted, userId })
      const response = await suggestedSlotService.updateAcceptance(slotId, accepted, userId)
      
      if (response.status === 'success') {
        // Mettre à jour le créneau dans la liste locale
        const index = slots.value.findIndex(slot => slot.id == slotId)
        if (index !== -1) {
          slots.value[index].accepte = accepted ? 1 : 0
          slots.value[index].statut = accepted ? 'accepte' : 'refuse'
        }
        
        // Recharger les stats
        await loadStats()
        
        console.log('Store: Acceptation mise à jour')
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || 'Erreur lors de la mise à jour')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de la mise à jour de l\'acceptation')
      return { success: false, error: err.message }
    } finally {
      setLoading(false)
    }
  }

  // Supprimer un créneau suggéré
  const deleteSlot = async (slotId) => {
    try {
      setLoading(true)
      clearError()
      
      console.log('Store: Suppression du créneau suggéré...', slotId)
      const response = await suggestedSlotService.delete(slotId)
      
      if (response.status === 'success') {
        // Supprimer de la liste locale
        slots.value = slots.value.filter(slot => slot.id != slotId)
        
        // Recharger les stats
        await loadStats()
        
        console.log('Store: Créneau suggéré supprimé')
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || 'Erreur lors de la suppression')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de la suppression du créneau suggéré')
      return { success: false, error: err.message }
    } finally {
      setLoading(false)
    }
  }

  // Nettoyer les créneaux expirés
  const cleanExpired = async () => {
    try {
      setLoading(true)
      clearError()
      
      console.log('Store: Nettoyage des créneaux expirés...')
      const response = await suggestedSlotService.cleanExpired()
      
      if (response.status === 'success') {
        // Recharger les créneaux et stats
        const currentMedecinId = slots.value.length > 0 ? slots.value[0].id_medecin : null
        if (currentMedecinId) {
          await loadSlotsByDoctor(currentMedecinId)
          await loadStats(currentMedecinId)
        }
        
        console.log('Store: Nettoyage terminé:', response.data.deleted_count)
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || 'Erreur lors du nettoyage')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors du nettoyage des créneaux expirés')
      return { success: false, error: err.message }
    } finally {
      setLoading(false)
    }
  }

  // Créer plusieurs créneaux suggérés
  const createMultipleSlots = async (slotsData) => {
    try {
      setLoading(true)
      clearError()
      
      const results = []
      let successCount = 0
      let errorCount = 0
      
      for (const slotData of slotsData) {
        try {
          const response = await suggestedSlotService.create(slotData)
          if (response.status === 'success') {
            results.push({ success: true, data: response.data })
            slots.value.push(response.data)
            successCount++
          } else {
            results.push({ success: false, error: response.message })
            errorCount++
          }
        } catch (err) {
          results.push({ success: false, error: err.message })
          errorCount++
        }
      }
      
      // Recharger les stats
      if (slotsData.length > 0) {
        await loadStats(slotsData[0].id_medecin)
      }
      
      console.log('Store: Création multiple terminée:', { successCount, errorCount })
      return {
        success: errorCount === 0,
        results,
        successCount,
        errorCount
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de la création multiple')
      return { success: false, error: err.message }
    } finally {
      setLoading(false)
    }
  }

  // Réinitialiser le store
  const reset = () => {
    slots.value = []
    stats.value = {
      total: 0,
      acceptes: 0,
      refuses: 0,
      en_attente: 0,
      expires: 0,
      score_moyen: 0,
      score_acceptes: 0
    }
    loading.value = false
    error.value = null
    lastFetch.value = null
  }

  return {
    // État
    slots,
    stats,
    loading,
    error,
    lastFetch,
    
    // Getters
    slotCount,
    pendingSlots,
    acceptedSlots,
    refusedSlots,
    expiredSlots,
    slotsByStatus,
    upcomingSlots,
    
    // Actions
    loadSlotsByDoctor,
    loadSlotsByPatient,
    loadStats,
    createSlot,
    updateAcceptance,
    deleteSlot,
    cleanExpired,
    createMultipleSlots,
    reset,
    clearError
  }
})
