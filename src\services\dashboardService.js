import api from './api'

class DashboardService {
  /**
   * R<PERSON>cupérer les statistiques du tableau de bord pour un médecin
   */
  async getDoctorStatistics(doctorId) {
    try {
      console.log('📊 Récupération des statistiques pour le médecin:', doctorId)
      const response = await api.get(`/doctor/statistics/${doctorId}`)
      console.log('✅ Statistiques reçues:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback avec des données par défaut
      return {
        status: 'success',
        data: {
          totalPatients: 0,
          appointmentsToday: 0,
          upcomingAppointments: 0,
          totalAppointments: 0,
          suggestedSlots: 0
        },
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer les rendez-vous récents pour un médecin (aujourd'hui + derniers jours)
   */
  async getTodayAppointments(doctorId) {
    try {
      console.log('📅 Récupération des RDV récents pour le médecin:', doctorId)

      // Essayer plusieurs routes possibles
      const possibleRoutes = [
        `/appointments/doctor/${doctorId}`,
        `/rendez-vous/medecin/${doctorId}`,
        `/appointments?doctor_id=${doctorId}`,
        `/rendez-vous?id_medecin=${doctorId}`,
        `/appointments`,
        `/rendez-vous`
      ]

      let response = null
      for (const route of possibleRoutes) {
        try {
          console.log(`🔍 Tentative avec la route: ${route}`)
          response = await api.get(route)
          if (response.data && (response.data.status === 'success' || response.data.data || Array.isArray(response.data))) {
            console.log('✅ Route fonctionnelle trouvée:', route)
            break
          }
        } catch (routeError) {
          console.log(`❌ Route ${route} non disponible:`, routeError.response?.status)
          continue
        }
      }

      if (response && response.data && (response.data.status === 'success' || response.data.data || Array.isArray(response.data))) {
        const today = new Date()
        const threeDaysAgo = new Date()
        threeDaysAgo.setDate(today.getDate() - 3)

        // Extraire les données selon le format de réponse
        let appointments = []
        if (response.data.status === 'success' && response.data.data) {
          appointments = response.data.data
        } else if (Array.isArray(response.data)) {
          appointments = response.data
        } else if (response.data.data && Array.isArray(response.data.data)) {
          appointments = response.data.data
        }

        console.log('📊 Rendez-vous récupérés:', appointments.length)
        console.log('📋 Détails des RDV:', appointments)

        // Filtrer les rendez-vous récents (aujourd'hui + 3 derniers jours)
        const recentAppointments = appointments.filter(apt => {
          const aptDate = new Date(apt.date_rendez_vous || apt.date)
          return aptDate >= threeDaysAgo && aptDate <= today
        })

        // Séparer les RDV d'aujourd'hui des RDV récents
        const todayStr = today.toISOString().split('T')[0]
        const todayAppointments = recentAppointments.filter(apt => {
          const aptDate = apt.date_rendez_vous ? apt.date_rendez_vous.split(' ')[0] : apt.date?.split('T')[0]
          return aptDate === todayStr
        })

        console.log('✅ RDV d\'aujourd\'hui trouvés:', todayAppointments.length)
        console.log('✅ RDV récents (3 derniers jours):', recentAppointments.length)

        // Retourner les RDV d'aujourd'hui, ou les récents si aucun aujourd'hui
        const finalAppointments = todayAppointments.length > 0 ? todayAppointments : recentAppointments

        return {
          status: 'success',
          data: finalAppointments,
          count: finalAppointments.length,
          todayCount: todayAppointments.length,
          recentCount: recentAppointments.length,
          isToday: todayAppointments.length > 0
        }
      }

      throw new Error('Réponse API invalide')
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des RDV récents:', error)
      return {
        status: 'error',
        data: [],
        count: 0,
        todayCount: 0,
        recentCount: 0,
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer le prochain rendez-vous pour un médecin (ou le plus récent si aucun à venir)
   */
  async getNextAppointment(doctorId) {
    try {
      console.log('⏰ Récupération du prochain RDV pour le médecin:', doctorId)

      // Essayer plusieurs routes possibles
      const possibleRoutes = [
        `/appointments/doctor/${doctorId}`,
        `/rendez-vous/medecin/${doctorId}`,
        `/appointments?doctor_id=${doctorId}`,
        `/rendez-vous?id_medecin=${doctorId}`,
        `/appointments`,
        `/rendez-vous`
      ]

      let response = null
      for (const route of possibleRoutes) {
        try {
          console.log(`🔍 Tentative avec la route: ${route}`)
          response = await api.get(route)
          if (response.data && (response.data.status === 'success' || response.data.data || Array.isArray(response.data))) {
            console.log('✅ Route fonctionnelle trouvée:', route)
            break
          }
        } catch (routeError) {
          console.log(`❌ Route ${route} non disponible:`, routeError.response?.status)
          continue
        }
      }

      if (response && response.data && (response.data.status === 'success' || response.data.data || Array.isArray(response.data))) {
        const now = new Date()
        console.log('🕐 Heure actuelle:', now.toISOString())

        // Extraire les données selon le format de réponse
        let appointments = []
        if (response.data.status === 'success' && response.data.data) {
          appointments = response.data.data
        } else if (Array.isArray(response.data)) {
          appointments = response.data
        } else if (response.data.data && Array.isArray(response.data.data)) {
          appointments = response.data.data
        }

        console.log('📊 Rendez-vous récupérés pour prochain RDV:', appointments.length)

        // D'abord chercher les RDV à venir (avec comparaison précise date + heure)
        const upcomingAppointments = appointments
          .filter(apt => {
            const aptDateString = apt.date_rendez_vous || apt.date
            const aptDate = new Date(aptDateString)

            // Debug: afficher les dates pour comprendre le problème
            console.log('🔍 Comparaison RDV:', {
              aptDateString,
              aptDate: aptDate.toISOString(),
              now: now.toISOString(),
              isAfterNow: aptDate > now,
              timeDiff: aptDate.getTime() - now.getTime()
            })

            return aptDate > now
          })
          .sort((a, b) => {
            const dateA = new Date(a.date_rendez_vous || a.date)
            const dateB = new Date(b.date_rendez_vous || b.date)
            return dateA - dateB
          })

        console.log(`📊 RDV à venir trouvés: ${upcomingAppointments.length}`)

        let nextAppointment = null
        let isUpcoming = true

        if (upcomingAppointments.length > 0) {
          nextAppointment = upcomingAppointments[0]
          console.log('✅ Prochain RDV à venir trouvé:', {
            id: nextAppointment.id,
            date: nextAppointment.date_rendez_vous || nextAppointment.date,
            patient: nextAppointment.patient_nom || nextAppointment.patient
          })
        } else {
          // Si aucun RDV à venir, prendre le plus récent
          console.log('⚠️ Aucun RDV à venir, recherche du plus récent...')
          const recentAppointments = appointments
            .sort((a, b) => {
              const dateA = new Date(a.date_rendez_vous || a.date)
              const dateB = new Date(b.date_rendez_vous || b.date)
              return dateB - dateA // Tri décroissant pour avoir le plus récent
            })

          if (recentAppointments.length > 0) {
            nextAppointment = recentAppointments[0]
            isUpcoming = false
            console.log('✅ RDV le plus récent trouvé (aucun à venir):', {
              id: nextAppointment.id,
              date: nextAppointment.date_rendez_vous || nextAppointment.date,
              patient: nextAppointment.patient_nom || nextAppointment.patient,
              isPast: true
            })
          }
        }

        return {
          status: 'success',
          data: nextAppointment,
          isUpcoming: isUpcoming
        }
      }

      throw new Error('Réponse API invalide')
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du prochain RDV:', error)
      return {
        status: 'error',
        data: null,
        isUpcoming: false,
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer les patients uniques d'aujourd'hui pour un médecin
   */
  async getUniquePatientsToday(doctorId) {
    try {
      const todayResult = await this.getTodayAppointments(doctorId)
      
      if (todayResult.status === 'success') {
        const uniquePatients = new Set()
        todayResult.data.forEach(apt => {
          if (apt.id_patient) {
            uniquePatients.add(apt.id_patient)
          }
        })
        
        console.log('✅ Patients uniques aujourd\'hui:', uniquePatients.size)
        return {
          status: 'success',
          count: uniquePatients.size,
          patients: Array.from(uniquePatients)
        }
      }
      
      return {
        status: 'error',
        count: 0,
        patients: []
      }
    } catch (error) {
      console.error('❌ Erreur lors du calcul des patients uniques:', error)
      return {
        status: 'error',
        count: 0,
        patients: []
      }
    }
  }

  /**
   * Récupérer l'ID médecin à partir de l'ID utilisateur
   */
  async getDoctorIdFromUserId(userId) {
    try {
      console.log('🔍 Recherche de l\'ID médecin pour l\'utilisateur:', userId)

      // Essayer plusieurs routes possibles
      const possibleRoutes = [
        `/doctors/user/${userId}`,
        `/medecins/user/${userId}`,
        `/doctors/${userId}`,
        `/medecins/${userId}`
      ]

      for (const route of possibleRoutes) {
        try {
          console.log(`🔍 Tentative avec la route: ${route}`)
          const response = await api.get(route)

          if (response.data.status === 'success' && response.data.data) {
            const doctorId = response.data.data.id
            console.log('✅ ID médecin trouvé:', doctorId, 'via route:', route)
            return doctorId
          }
        } catch (routeError) {
          console.log(`❌ Route ${route} non disponible:`, routeError.response?.status)
          continue
        }
      }

      // Fallback: utiliser l'ID utilisateur directement
      console.log('⚠️ Aucune route API disponible, utilisation de l\'ID utilisateur comme ID médecin')
      return userId
    } catch (error) {
      console.error('❌ Erreur lors de la recherche du médecin:', error)
      // Retourner l'ID utilisateur comme fallback
      console.log('📝 Utilisation de l\'ID utilisateur comme fallback final')
      return userId
    }
  }

  /**
   * Récupérer toutes les données du tableau de bord en une fois
   */
  async getDashboardData(userIdOrDoctorId) {
    try {
      console.log('🔄 Chargement des vraies données du tableau de bord pour l\'utilisateur/médecin:', userIdOrDoctorId)

      // Utiliser l'ID utilisateur directement comme ID médecin
      const doctorId = userIdOrDoctorId
      console.log('🎯 ID médecin utilisé:', doctorId)

      // Essayer de récupérer les vraies données
      try {
        const [
          statistics,
          todayAppointments,
          nextAppointment,
          uniquePatients
        ] = await Promise.all([
          this.getDoctorStatistics(doctorId),
          this.getTodayAppointments(doctorId),
          this.getNextAppointment(doctorId),
          this.getUniquePatientsToday(doctorId)
        ])

        const dashboardData = {
          doctorId: doctorId,
          statistics: statistics.data,
          todayAppointments: {
            count: todayAppointments.count,
            appointments: todayAppointments.data,
            todayCount: todayAppointments.todayCount,
            recentCount: todayAppointments.recentCount,
            isToday: todayAppointments.isToday
          },
          nextAppointment: nextAppointment.data,
          nextAppointmentIsUpcoming: nextAppointment.isUpcoming,
          uniquePatientsToday: uniquePatients.count,
          lastUpdated: new Date().toISOString()
        }

        console.log('✅ Vraies données du tableau de bord chargées:', dashboardData)
        return {
          status: 'success',
          data: dashboardData
        }
      } catch (apiError) {
        console.warn('⚠️ Erreur API, utilisation de données de fallback:', apiError.message)

        // Fallback avec données de démonstration
        const dashboardData = {
          doctorId: doctorId,
          statistics: {
            totalPatients: 0,
            appointmentsToday: 0,
            upcomingAppointments: 0,
            totalAppointments: 0,
            suggestedSlots: 0
          },
          todayAppointments: {
            count: 0,
            appointments: [],
            todayCount: 0,
            recentCount: 0,
            isToday: true
          },
          nextAppointment: null,
          nextAppointmentIsUpcoming: false,
          uniquePatientsToday: 0,
          lastUpdated: new Date().toISOString()
        }

        console.log('📝 Données de fallback utilisées:', dashboardData)
        return {
          status: 'success',
          data: dashboardData
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement du tableau de bord:', error)
      return {
        status: 'error',
        message: 'Erreur lors du chargement des données du tableau de bord',
        data: {
          doctorId: userIdOrDoctorId,
          statistics: {
            totalPatients: 0,
            appointmentsToday: 0,
            upcomingAppointments: 0,
            totalAppointments: 0,
            suggestedSlots: 0
          },
          todayAppointments: { count: 0, appointments: [], todayCount: 0, recentCount: 0, isToday: false },
          nextAppointment: null,
          nextAppointmentIsUpcoming: false,
          uniquePatientsToday: 0,
          lastUpdated: new Date().toISOString()
        }
      }
    }
  }

  /**
   * Formater l'heure d'un rendez-vous
   */
  formatAppointmentTime(dateString) {
    if (!dateString) return 'Heure non définie'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } catch (error) {
      console.warn('Erreur lors du formatage de l\'heure:', dateString)
      return 'Heure invalide'
    }
  }

  /**
   * Formater les détails d'un rendez-vous
   */
  formatAppointmentDetails(appointment) {
    if (!appointment) return null
    
    const patientName = appointment.patient_nom || appointment.patient || 'Patient'
    const type = appointment.type || 'Consultation'
    
    return `${patientName} - ${type}`
  }
}

export default new DashboardService()
