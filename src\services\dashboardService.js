import api from './api'

class DashboardService {
  /**
   * Récupérer les statistiques du tableau de bord pour un médecin
   */
  async getDoctorStatistics(doctorId) {
    try {
      console.log('📊 Récupération des statistiques pour le médecin:', doctorId)
      const response = await api.get(`/doctor/statistics/${doctorId}`)
      console.log('✅ Statistiques reçues:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback avec des données par défaut
      return {
        status: 'success',
        data: {
          totalPatients: 0,
          appointmentsToday: 0,
          upcomingAppointments: 0,
          totalAppointments: 0,
          suggestedSlots: 0
        },
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer les rendez-vous d'aujourd'hui pour un médecin
   */
  async getTodayAppointments(doctorId) {
    try {
      console.log('📅 Récupération des RDV d\'aujourd\'hui pour le médecin:', doctorId)
      const today = new Date().toISOString().split('T')[0]
      const response = await api.get(`/appointments/doctor/${doctorId}`)
      
      if (response.data.status === 'success') {
        // Filtrer les rendez-vous d'aujourd'hui
        const todayAppointments = response.data.data.filter(apt => {
          const aptDate = apt.date_rendez_vous ? apt.date_rendez_vous.split(' ')[0] : apt.date?.split('T')[0]
          return aptDate === today
        })
        
        console.log('✅ RDV d\'aujourd\'hui trouvés:', todayAppointments.length)
        return {
          status: 'success',
          data: todayAppointments,
          count: todayAppointments.length
        }
      }
      
      throw new Error('Réponse API invalide')
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des RDV d\'aujourd\'hui:', error)
      return {
        status: 'error',
        data: [],
        count: 0,
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer le prochain rendez-vous pour un médecin
   */
  async getNextAppointment(doctorId) {
    try {
      console.log('⏰ Récupération du prochain RDV pour le médecin:', doctorId)
      const response = await api.get(`/appointments/doctor/${doctorId}`)
      
      if (response.data.status === 'success') {
        const now = new Date()
        const upcomingAppointments = response.data.data
          .filter(apt => {
            const aptDate = new Date(apt.date_rendez_vous || apt.date)
            return aptDate > now
          })
          .sort((a, b) => {
            const dateA = new Date(a.date_rendez_vous || a.date)
            const dateB = new Date(b.date_rendez_vous || b.date)
            return dateA - dateB
          })
        
        const nextAppointment = upcomingAppointments.length > 0 ? upcomingAppointments[0] : null
        console.log('✅ Prochain RDV trouvé:', nextAppointment)
        
        return {
          status: 'success',
          data: nextAppointment
        }
      }
      
      throw new Error('Réponse API invalide')
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du prochain RDV:', error)
      return {
        status: 'error',
        data: null,
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer les patients uniques d'aujourd'hui pour un médecin
   */
  async getUniquePatientsToday(doctorId) {
    try {
      const todayResult = await this.getTodayAppointments(doctorId)
      
      if (todayResult.status === 'success') {
        const uniquePatients = new Set()
        todayResult.data.forEach(apt => {
          if (apt.id_patient) {
            uniquePatients.add(apt.id_patient)
          }
        })
        
        console.log('✅ Patients uniques aujourd\'hui:', uniquePatients.size)
        return {
          status: 'success',
          count: uniquePatients.size,
          patients: Array.from(uniquePatients)
        }
      }
      
      return {
        status: 'error',
        count: 0,
        patients: []
      }
    } catch (error) {
      console.error('❌ Erreur lors du calcul des patients uniques:', error)
      return {
        status: 'error',
        count: 0,
        patients: []
      }
    }
  }

  /**
   * Récupérer toutes les données du tableau de bord en une fois
   */
  async getDashboardData(doctorId) {
    try {
      console.log('🔄 Chargement complet du tableau de bord pour le médecin:', doctorId)
      
      const [
        statistics,
        todayAppointments,
        nextAppointment,
        uniquePatients
      ] = await Promise.all([
        this.getDoctorStatistics(doctorId),
        this.getTodayAppointments(doctorId),
        this.getNextAppointment(doctorId),
        this.getUniquePatientsToday(doctorId)
      ])

      const dashboardData = {
        statistics: statistics.data,
        todayAppointments: {
          count: todayAppointments.count,
          appointments: todayAppointments.data
        },
        nextAppointment: nextAppointment.data,
        uniquePatientsToday: uniquePatients.count,
        lastUpdated: new Date().toISOString()
      }

      console.log('✅ Données complètes du tableau de bord chargées:', dashboardData)
      return {
        status: 'success',
        data: dashboardData
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement du tableau de bord:', error)
      return {
        status: 'error',
        message: 'Erreur lors du chargement des données du tableau de bord',
        data: {
          statistics: {
            totalPatients: 0,
            appointmentsToday: 0,
            upcomingAppointments: 0,
            totalAppointments: 0,
            suggestedSlots: 0
          },
          todayAppointments: { count: 0, appointments: [] },
          nextAppointment: null,
          uniquePatientsToday: 0,
          lastUpdated: new Date().toISOString()
        }
      }
    }
  }

  /**
   * Formater l'heure d'un rendez-vous
   */
  formatAppointmentTime(dateString) {
    if (!dateString) return 'Heure non définie'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } catch (error) {
      console.warn('Erreur lors du formatage de l\'heure:', dateString)
      return 'Heure invalide'
    }
  }

  /**
   * Formater les détails d'un rendez-vous
   */
  formatAppointmentDetails(appointment) {
    if (!appointment) return null
    
    const patientName = appointment.patient_nom || appointment.patient || 'Patient'
    const type = appointment.type || 'Consultation'
    
    return `${patientName} - ${type}`
  }
}

export default new DashboardService()
