import api from './api'

class DashboardService {
  /**
   * Récupérer les statistiques du tableau de bord pour un médecin
   */
  async getDoctorStatistics(doctorId) {
    try {
      console.log('📊 Récupération des statistiques pour le médecin:', doctorId)
      const response = await api.get(`/doctor/statistics/${doctorId}`)
      console.log('✅ Statistiques reçues:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback avec des données par défaut
      return {
        status: 'success',
        data: {
          totalPatients: 0,
          appointmentsToday: 0,
          upcomingAppointments: 0,
          totalAppointments: 0,
          suggestedSlots: 0
        },
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer les rendez-vous récents pour un médecin (aujourd'hui + derniers jours)
   */
  async getTodayAppointments(doctorId) {
    try {
      console.log('📅 Récupération des RDV récents pour le médecin:', doctorId)
      const response = await api.get(`/appointments/doctor/${doctorId}`)

      if (response.data.status === 'success') {
        const today = new Date()
        const threeDaysAgo = new Date()
        threeDaysAgo.setDate(today.getDate() - 3)

        // Filtrer les rendez-vous récents (aujourd'hui + 3 derniers jours)
        const recentAppointments = response.data.data.filter(apt => {
          const aptDate = new Date(apt.date_rendez_vous || apt.date)
          return aptDate >= threeDaysAgo && aptDate <= today
        })

        // Séparer les RDV d'aujourd'hui des RDV récents
        const todayStr = today.toISOString().split('T')[0]
        const todayAppointments = recentAppointments.filter(apt => {
          const aptDate = apt.date_rendez_vous ? apt.date_rendez_vous.split(' ')[0] : apt.date?.split('T')[0]
          return aptDate === todayStr
        })

        console.log('✅ RDV d\'aujourd\'hui trouvés:', todayAppointments.length)
        console.log('✅ RDV récents (3 derniers jours):', recentAppointments.length)

        // Retourner les RDV d'aujourd'hui, ou les récents si aucun aujourd'hui
        const finalAppointments = todayAppointments.length > 0 ? todayAppointments : recentAppointments

        return {
          status: 'success',
          data: finalAppointments,
          count: finalAppointments.length,
          todayCount: todayAppointments.length,
          recentCount: recentAppointments.length,
          isToday: todayAppointments.length > 0
        }
      }

      throw new Error('Réponse API invalide')
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des RDV récents:', error)
      return {
        status: 'error',
        data: [],
        count: 0,
        todayCount: 0,
        recentCount: 0,
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer le prochain rendez-vous pour un médecin (ou le plus récent si aucun à venir)
   */
  async getNextAppointment(doctorId) {
    try {
      console.log('⏰ Récupération du prochain RDV pour le médecin:', doctorId)
      const response = await api.get(`/appointments/doctor/${doctorId}`)

      if (response.data.status === 'success') {
        const now = new Date()

        // D'abord chercher les RDV à venir
        const upcomingAppointments = response.data.data
          .filter(apt => {
            const aptDate = new Date(apt.date_rendez_vous || apt.date)
            return aptDate > now
          })
          .sort((a, b) => {
            const dateA = new Date(a.date_rendez_vous || a.date)
            const dateB = new Date(b.date_rendez_vous || b.date)
            return dateA - dateB
          })

        let nextAppointment = null
        let isUpcoming = true

        if (upcomingAppointments.length > 0) {
          nextAppointment = upcomingAppointments[0]
          console.log('✅ Prochain RDV à venir trouvé:', nextAppointment)
        } else {
          // Si aucun RDV à venir, prendre le plus récent
          const recentAppointments = response.data.data
            .sort((a, b) => {
              const dateA = new Date(a.date_rendez_vous || a.date)
              const dateB = new Date(b.date_rendez_vous || b.date)
              return dateB - dateA // Tri décroissant pour avoir le plus récent
            })

          if (recentAppointments.length > 0) {
            nextAppointment = recentAppointments[0]
            isUpcoming = false
            console.log('✅ RDV le plus récent trouvé (aucun à venir):', nextAppointment)
          }
        }

        return {
          status: 'success',
          data: nextAppointment,
          isUpcoming: isUpcoming
        }
      }

      throw new Error('Réponse API invalide')
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du prochain RDV:', error)
      return {
        status: 'error',
        data: null,
        isUpcoming: false,
        source: 'fallback'
      }
    }
  }

  /**
   * Récupérer les patients uniques d'aujourd'hui pour un médecin
   */
  async getUniquePatientsToday(doctorId) {
    try {
      const todayResult = await this.getTodayAppointments(doctorId)
      
      if (todayResult.status === 'success') {
        const uniquePatients = new Set()
        todayResult.data.forEach(apt => {
          if (apt.id_patient) {
            uniquePatients.add(apt.id_patient)
          }
        })
        
        console.log('✅ Patients uniques aujourd\'hui:', uniquePatients.size)
        return {
          status: 'success',
          count: uniquePatients.size,
          patients: Array.from(uniquePatients)
        }
      }
      
      return {
        status: 'error',
        count: 0,
        patients: []
      }
    } catch (error) {
      console.error('❌ Erreur lors du calcul des patients uniques:', error)
      return {
        status: 'error',
        count: 0,
        patients: []
      }
    }
  }

  /**
   * Récupérer l'ID médecin à partir de l'ID utilisateur
   */
  async getDoctorIdFromUserId(userId) {
    try {
      console.log('🔍 Recherche de l\'ID médecin pour l\'utilisateur:', userId)
      const response = await api.get(`/doctors/user/${userId}`)

      if (response.data.status === 'success' && response.data.data) {
        const doctorId = response.data.data.id
        console.log('✅ ID médecin trouvé:', doctorId)
        return doctorId
      }

      // Fallback: essayer de trouver dans la liste des médecins
      console.log('⚠️ Tentative de fallback pour trouver le médecin')
      const allDoctorsResponse = await api.get('/doctors')
      if (allDoctorsResponse.data.status === 'success') {
        const doctor = allDoctorsResponse.data.data.find(d => d.user_id == userId)
        if (doctor) {
          console.log('✅ Médecin trouvé via fallback:', doctor.id)
          return doctor.id
        }
      }

      throw new Error('Médecin non trouvé pour cet utilisateur')
    } catch (error) {
      console.error('❌ Erreur lors de la recherche du médecin:', error)
      // Retourner l'ID utilisateur comme fallback
      console.log('📝 Utilisation de l\'ID utilisateur comme fallback')
      return userId
    }
  }

  /**
   * Récupérer toutes les données du tableau de bord en une fois
   */
  async getDashboardData(userIdOrDoctorId) {
    try {
      console.log('🔄 Chargement complet du tableau de bord pour l\'utilisateur/médecin:', userIdOrDoctorId)

      // D'abord, s'assurer qu'on a l'ID médecin correct
      const doctorId = await this.getDoctorIdFromUserId(userIdOrDoctorId)
      console.log('🎯 ID médecin final utilisé:', doctorId)

      const [
        statistics,
        todayAppointments,
        nextAppointment,
        uniquePatients
      ] = await Promise.all([
        this.getDoctorStatistics(doctorId),
        this.getTodayAppointments(doctorId),
        this.getNextAppointment(doctorId),
        this.getUniquePatientsToday(doctorId)
      ])

      const dashboardData = {
        doctorId: doctorId,
        statistics: statistics.data,
        todayAppointments: {
          count: todayAppointments.count,
          appointments: todayAppointments.data,
          todayCount: todayAppointments.todayCount,
          recentCount: todayAppointments.recentCount,
          isToday: todayAppointments.isToday
        },
        nextAppointment: nextAppointment.data,
        nextAppointmentIsUpcoming: nextAppointment.isUpcoming,
        uniquePatientsToday: uniquePatients.count,
        lastUpdated: new Date().toISOString()
      }

      console.log('✅ Données complètes du tableau de bord chargées:', dashboardData)
      return {
        status: 'success',
        data: dashboardData
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement du tableau de bord:', error)
      return {
        status: 'error',
        message: 'Erreur lors du chargement des données du tableau de bord',
        data: {
          doctorId: userIdOrDoctorId,
          statistics: {
            totalPatients: 0,
            appointmentsToday: 0,
            upcomingAppointments: 0,
            totalAppointments: 0,
            suggestedSlots: 0
          },
          todayAppointments: { count: 0, appointments: [], todayCount: 0, recentCount: 0, isToday: false },
          nextAppointment: null,
          nextAppointmentIsUpcoming: false,
          uniquePatientsToday: 0,
          lastUpdated: new Date().toISOString()
        }
      }
    }
  }

  /**
   * Formater l'heure d'un rendez-vous
   */
  formatAppointmentTime(dateString) {
    if (!dateString) return 'Heure non définie'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } catch (error) {
      console.warn('Erreur lors du formatage de l\'heure:', dateString)
      return 'Heure invalide'
    }
  }

  /**
   * Formater les détails d'un rendez-vous
   */
  formatAppointmentDetails(appointment) {
    if (!appointment) return null
    
    const patientName = appointment.patient_nom || appointment.patient || 'Patient'
    const type = appointment.type || 'Consultation'
    
    return `${patientName} - ${type}`
  }
}

export default new DashboardService()
