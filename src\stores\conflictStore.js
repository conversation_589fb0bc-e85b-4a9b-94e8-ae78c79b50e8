// stores/conflictStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import conflictService from '@/services/conflictService.js'

export const useConflictStore = defineStore('conflict', () => {
  const conflicts = ref([])
  const stats = ref({
    total: 0,
    actifs: 0,
    resolus: 0,
    expires: 0,
    critiques: 0,
    eleves: 0,
    temps_resolution_moyen: 0
  })
  const loading = ref(false)
  const error = ref(null)

  const activeConflicts = computed(() =>
    conflicts.value.filter(conflict =>
      conflict.statut === 'actif' || conflict.status === 'active'
    )
  )

  const criticalConflicts = computed(() =>
    activeConflicts.value.filter(conflict =>
      conflict.severite === 'critique' || conflict.severity === 'critical'
    )
  )

  const conflictCount = computed(() => activeConflicts.value.length)
  const criticalCount = computed(() => criticalConflicts.value.length)

  // Actions
  const setLoading = (value) => {
    loading.value = value
  }

  const setError = (errorMessage) => {
    error.value = errorMessage
    console.error('Store: Erreur conflits:', errorMessage)
  }

  const clearError = () => {
    error.value = null
  }

  // Charger tous les conflits actifs
  async function loadConflicts(medecinId = null) {
    try {
      setLoading(true)
      clearError()

      console.log('Store: Chargement des conflits...', { medecinId })
      const response = await conflictService.getActiveConflicts(medecinId)

      if (response.status === 'success') {
        conflicts.value = response.data || []
        console.log('Store: Conflits chargés:', conflicts.value.length)
      } else {
        throw new Error(response.message || 'Erreur lors du chargement')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors du chargement des conflits')
      conflicts.value = []
    } finally {
      setLoading(false)
    }
  }

  // Charger les statistiques
  async function loadStats(medecinId = null) {
    try {
      console.log('Store: Chargement des statistiques...', { medecinId })
      const response = await conflictService.getConflictStats(medecinId)

      if (response.status === 'success') {
        stats.value = response.data || stats.value
        console.log('Store: Statistiques chargées:', stats.value)
      } else {
        throw new Error(response.message || 'Erreur lors du chargement des stats')
      }
    } catch (err) {
      console.error('Store: Erreur stats:', err)
    }
  }

  // Détecter les conflits pour un médecin
  async function detectConflicts(medecinId, date = null) {
    try {
      setLoading(true)
      clearError()

      console.log('Store: Détection des conflits...', { medecinId, date })
      const response = await conflictService.detectConflicts(medecinId, date)

      if (response.status === 'success') {
        // Recharger les conflits après détection
        await loadConflicts(medecinId)
        await loadStats(medecinId)

        return response.data
      } else {
        throw new Error(response.message || 'Erreur lors de la détection')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de la détection des conflits')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Résoudre un conflit
  async function resolveConflict(conflictId, resolutionData) {
    try {
      setLoading(true)
      clearError()

      console.log('Store: Résolution du conflit...', { conflictId, resolutionData })
      const response = await conflictService.resolveConflict(conflictId, resolutionData)

      if (response.status === 'success') {
        // Mettre à jour le conflit dans la liste locale
        const index = conflicts.value.findIndex(c => c.id == conflictId)
        if (index !== -1) {
          conflicts.value[index].statut = 'resolu'
          conflicts.value[index].methode_resolution = resolutionData.methode
          conflicts.value[index].resolu_le = new Date().toISOString()
        }

        // Recharger les stats
        await loadStats()

        return response
      } else {
        throw new Error(response.message || 'Erreur lors de la résolution')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de la résolution du conflit')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Ignorer un conflit
  async function ignoreConflict(conflictId, userId = null) {
    try {
      setLoading(true)
      clearError()

      console.log('Store: Ignorance du conflit...', { conflictId, userId })
      const response = await conflictService.ignoreConflict(conflictId, userId)

      if (response.status === 'success') {
        // Mettre à jour le conflit dans la liste locale
        const index = conflicts.value.findIndex(c => c.id == conflictId)
        if (index !== -1) {
          conflicts.value[index].statut = 'ignore'
          conflicts.value[index].methode_resolution = 'ignore'
          conflicts.value[index].resolu_le = new Date().toISOString()
        }

        // Recharger les stats
        await loadStats()

        return response
      } else {
        throw new Error(response.message || 'Erreur lors de l\'ignorance')
      }
    } catch (err) {
      setError(err.message || 'Erreur lors de l\'ignorance du conflit')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Réinitialiser le store
  const reset = () => {
    conflicts.value = []
    stats.value = {
      total: 0,
      actifs: 0,
      resolus: 0,
      expires: 0,
      critiques: 0,
      eleves: 0,
      temps_resolution_moyen: 0
    }
    loading.value = false
    error.value = null
  }

  return {
    // État
    conflicts,
    stats,
    loading,
    error,

    // Getters
    activeConflicts,
    criticalConflicts,
    conflictCount,
    criticalCount,

    // Actions
    loadConflicts,
    loadStats,
    detectConflicts,
    resolveConflict,
    ignoreConflict,
    reset,
    clearError
  }
})