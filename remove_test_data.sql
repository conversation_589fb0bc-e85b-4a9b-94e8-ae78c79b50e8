-- Script SQL pour supprimer les données de test de l'agenda médical
-- Exécutez ce script pour nettoyer votre base de données

-- Afficher un message de début
SELECT '🧹 SUPPRESSION DES DONNÉES DE TEST' as message;
SELECT '=====================================' as separator;

-- Compter les données avant suppression
SELECT 'AVANT SUPPRESSION:' as status;
SELECT 'Médecins de test:' as type, COUNT(*) as count 
FROM utilisateur 
WHERE role = 'medecin' AND email IN (
    '<EMAIL>', 
    '<EMAIL>', 
    '<EMAIL>'
);

SELECT 'Patients de test:' as type, COUNT(*) as count 
FROM utilisateur 
WHERE role = 'patient' AND email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
);

SELECT 'Rendez-vous de test:' as type, COUNT(*) as count 
FROM rendez_vous 
WHERE notes = 'Rendez-vous de test';

-- Vérifier si la table creneaux_suggeres existe et compter
SELECT 'Créneaux suggérés de test:' as type, 
       CASE 
           WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres')
           THEN (SELECT COUNT(*) FROM creneaux_suggeres WHERE raison IN ('Créneau disponible', 'Consultation de suivi', 'Nouveau patient'))
           ELSE 0
       END as count;

-- Commencer la suppression
SELECT '' as separator;
SELECT '🗑️ DÉBUT DE LA SUPPRESSION...' as message;

-- 1. Supprimer les rendez-vous de test
DELETE FROM rendez_vous WHERE notes = 'Rendez-vous de test';
SELECT 'Rendez-vous de test supprimés' as action, ROW_COUNT() as affected_rows;

-- 2. Supprimer les créneaux suggérés de test (si la table existe)
DELETE FROM creneaux_suggeres 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres')
  AND raison IN ('Créneau disponible', 'Consultation de suivi', 'Nouveau patient');
SELECT 'Créneaux suggérés de test supprimés' as action, ROW_COUNT() as affected_rows;

-- 3. Supprimer les conflits de test (si la table existe)
DELETE FROM conflits 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conflits')
  AND (description LIKE '%test%' OR notes LIKE '%test%');
SELECT 'Conflits de test supprimés' as action, ROW_COUNT() as affected_rows;

-- 4. Supprimer les utilisateurs de test (médecins et patients)
DELETE FROM utilisateur 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
);
SELECT 'Utilisateurs de test supprimés' as action, ROW_COUNT() as affected_rows;

-- Compter les données après suppression
SELECT '' as separator;
SELECT 'APRÈS SUPPRESSION:' as status;

SELECT 'Total médecins restants:' as type, COUNT(*) as count 
FROM utilisateur WHERE role = 'medecin';

SELECT 'Total patients restants:' as type, COUNT(*) as count 
FROM utilisateur WHERE role = 'patient';

SELECT 'Total rendez-vous restants:' as type, COUNT(*) as count 
FROM rendez_vous;

SELECT 'Total créneaux suggérés restants:' as type, 
       CASE 
           WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'creneaux_suggeres')
           THEN (SELECT COUNT(*) FROM creneaux_suggeres)
           ELSE 0
       END as count;

-- Message de fin
SELECT '' as separator;
SELECT '✅ SUPPRESSION DES DONNÉES DE TEST TERMINÉE' as message;
SELECT 'Toutes les données de test ont été supprimées avec succès!' as result;

-- Afficher les utilisateurs restants (pour vérification)
SELECT '' as separator;
SELECT 'UTILISATEURS RESTANTS:' as status;
SELECT 
    id,
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    role,
    specialite
FROM utilisateur 
ORDER BY role, nom;
