<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json");

// Gère la preflight request (OPTIONS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/config/database.php';

// Activer la journalisation des erreurs dans un fichier
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_errors.log');

// Log des données reçues
$rawData = file_get_contents("php://input");
error_log("Données brutes reçues : " . $rawData);

$data = json_decode($rawData, true);
error_log("Données décodées : " . print_r($data, true));

if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("Erreur de décodage JSON : " . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['message' => 'Données JSON invalides']);
    exit;
}

// Validation des données
$required = ['email', 'password', 'nom', 'prenom', 'role'];
foreach ($required as $field) {
    if (empty($data[$field])) {
        error_log("Champ manquant : " . $field);
        http_response_code(400);
        echo json_encode(['message' => "Le champ $field est requis."]);
        exit;
    }
}

try {
    $pdo = Database::getInstance()->getConnection();
    error_log("Connexion à la base de données établie");

    // Vérification si l'email existe déjà
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM utilisateur WHERE email = ?");
    error_log("Vérification de l'email : " . $data['email']);
    
    $stmt->execute([$data['email']]);
    if ($stmt->fetchColumn() > 0) {
        error_log("Email déjà utilisé : " . $data['email']);
        http_response_code(400);
        echo json_encode(['message' => 'Cet email est déjà utilisé.']);
        exit;
    }

    // Hachage du mot de passe
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT, ['cost' => 12]);
    error_log("Mot de passe haché généré");

    // Insertion de l'utilisateur
    $stmt = $pdo->prepare("
        INSERT INTO utilisateur (email, password, nom, prenom, role) 
        VALUES (?, ?, ?, ?, ?)
    ");
    error_log("Tentative d'insertion - Email: " . $data['email'] . ", Nom: " . $data['nom'] . ", Prénom: " . $data['prenom'] . ", Rôle: " . $data['role']);

    $success = $stmt->execute([
        $data['email'],
        $hashedPassword,
        $data['nom'],
        $data['prenom'],
        'patient' // Forcer le rôle à 'patient' pour la sécurité
    ]);

    if ($success) {
        $userId = $pdo->lastInsertId();
        error_log("Utilisateur créé avec succès. ID: " . $userId);
        $response = [
            'message' => 'Inscription réussie.',
            'user' => [
                'id' => $userId,
                'email' => $data['email'],
                'nom' => $data['nom'],
                'prenom' => $data['prenom'],
                'role' => $data['role']
            ]
        ];
        echo json_encode($response);
    } else {
        throw new Exception("Erreur lors de l'insertion");
    }
} catch (PDOException $e) {
    error_log("Erreur PDO lors de l'inscription : " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['message' => "Une erreur s'est produite lors de l'inscription.", 'error' => $e->getMessage()]);
} catch (Exception $e) {
    error_log("Erreur lors de l'inscription : " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['message' => "Une erreur s'est produite lors de l'inscription."]);
}
?>
