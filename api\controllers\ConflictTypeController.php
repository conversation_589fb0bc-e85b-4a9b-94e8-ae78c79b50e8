<?php
require_once __DIR__ . '/../config/database.php';

class ConflictTypeController {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    /**
     * Récupérer tous les types de conflits
     */
    public function index() {
        try {
            // Vérifier si la table types_conflits existe
            $stmt = $this->db->query("SHOW TABLES LIKE 'types_conflits'");
            if (!$stmt->fetch()) {
                // Table n'existe pas, retourner les types hardcodés
                $hardcodedTypes = $this->getHardcodedTypes();
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'data' => $hardcodedTypes,
                    'source' => 'hardcoded'
                ]);
                return;
            }

            $query = "
                SELECT id, code, nom, description, gravite_defaut, icone, couleur, actif, ordre_affichage
                FROM types_conflits
                WHERE actif = 1
                ORDER BY ordre_affichage ASC, nom ASC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $types = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Formater les données pour correspondre au format attendu par le frontend
            $formattedTypes = [];
            foreach ($types as $type) {
                $formattedTypes[$type['code']] = [
                    'id' => $type['id'],
                    'label' => $type['nom'],
                    'description' => $type['description'],
                    'severity_default' => $type['gravite_defaut'],
                    'icon' => $type['icone'],
                    'color' => $type['couleur'],
                    'active' => (bool)$type['actif'],
                    'order' => $type['ordre_affichage']
                ];
            }

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $formattedTypes,
                'source' => 'database'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictTypeController->index: " . $e->getMessage());
            // En cas d'erreur, retourner les types hardcodés
            $hardcodedTypes = $this->getHardcodedTypes();
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $hardcodedTypes,
                'source' => 'fallback'
            ]);
        }
    }

    /**
     * Créer un nouveau type de conflit
     */
    public function store() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            $query = "
                INSERT INTO types_conflits (code, nom, description, gravite_defaut, icone, couleur, ordre_affichage)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $data['code'],
                $data['nom'],
                $data['description'],
                $data['gravite_defaut'],
                $data['icone'] ?? 'fas fa-exclamation-circle',
                $data['couleur'] ?? '#ffc107',
                $data['ordre_affichage'] ?? 0
            ]);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Type de conflit créé avec succès',
                'data' => ['id' => $this->db->lastInsertId()]
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictTypeController->store: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création du type de conflit'
            ]);
        }
    }

    /**
     * Mettre à jour un type de conflit
     */
    public function update($id) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            $query = "
                UPDATE types_conflits 
                SET nom = ?, description = ?, gravite_defaut = ?, icone = ?, couleur = ?, ordre_affichage = ?
                WHERE id = ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $data['nom'],
                $data['description'],
                $data['gravite_defaut'],
                $data['icone'],
                $data['couleur'],
                $data['ordre_affichage'] ?? 0,
                $id
            ]);

            if ($stmt->rowCount() > 0) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Type de conflit mis à jour avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Type de conflit non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ConflictTypeController->update: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la mise à jour du type de conflit'
            ]);
        }
    }

    /**
     * Supprimer un type de conflit (désactivation)
     */
    public function delete($id) {
        try {
            // Vérifier si le type est utilisé dans des conflits existants
            $checkQuery = "SELECT COUNT(*) as count FROM conflits WHERE type_conflit = (SELECT code FROM types_conflits WHERE id = ?)";
            $checkStmt = $this->db->prepare($checkQuery);
            $checkStmt->execute([$id]);
            $usage = $checkStmt->fetch();

            if ($usage['count'] > 0) {
                // Désactiver au lieu de supprimer si utilisé
                $query = "UPDATE types_conflits SET actif = 0 WHERE id = ?";
                $message = 'Type de conflit désactivé (utilisé dans des conflits existants)';
            } else {
                // Supprimer complètement si non utilisé
                $query = "DELETE FROM types_conflits WHERE id = ?";
                $message = 'Type de conflit supprimé avec succès';
            }
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);

            if ($stmt->rowCount() > 0) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => $message
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Type de conflit non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ConflictTypeController->delete: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression du type de conflit'
            ]);
        }
    }

    /**
     * Récupérer les statistiques d'utilisation des types
     */
    public function getStats() {
        try {
            $query = "
                SELECT 
                    tc.id,
                    tc.code,
                    tc.nom,
                    tc.gravite_defaut,
                    tc.couleur,
                    COUNT(c.id) as nb_conflits,
                    COUNT(CASE WHEN c.resolu = 0 THEN 1 END) as nb_actifs,
                    COUNT(CASE WHEN c.resolu = 1 THEN 1 END) as nb_resolus
                FROM types_conflits tc
                LEFT JOIN conflits c ON tc.code = c.type_conflit
                WHERE tc.actif = 1
                GROUP BY tc.id, tc.code, tc.nom, tc.gravite_defaut, tc.couleur
                ORDER BY nb_conflits DESC, tc.nom ASC
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictTypeController->getStats: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }

    /**
     * Types de conflits hardcodés (fallback)
     */
    private function getHardcodedTypes() {
        return [
            'chevauchement' => [
                'id' => 1,
                'label' => 'Chevauchement de créneaux',
                'description' => 'Deux rendez-vous se chevauchent dans le temps',
                'severity_default' => 'elevee',
                'icon' => 'fas fa-exclamation-triangle',
                'color' => '#fd7e14',
                'active' => true,
                'order' => 1
            ],
            'duree_insuffisante' => [
                'id' => 2,
                'label' => 'Durée insuffisante',
                'description' => 'Le temps alloué est insuffisant pour le type de consultation',
                'severity_default' => 'moyenne',
                'icon' => 'fas fa-clock',
                'color' => '#ffc107',
                'active' => true,
                'order' => 2
            ],
            'hors_horaires' => [
                'id' => 3,
                'label' => 'Hors horaires de travail',
                'description' => 'Rendez-vous programmé en dehors des horaires du médecin',
                'severity_default' => 'elevee',
                'icon' => 'fas fa-calendar-times',
                'color' => '#fd7e14',
                'active' => true,
                'order' => 3
            ],
            'double_reservation' => [
                'id' => 4,
                'label' => 'Double réservation',
                'description' => 'Même patient avec plusieurs rendez-vous simultanés',
                'severity_default' => 'critique',
                'icon' => 'fas fa-copy',
                'color' => '#dc3545',
                'active' => true,
                'order' => 4
            ],
            'patient_absent' => [
                'id' => 5,
                'label' => 'Patient absent',
                'description' => 'Patient marqué comme absent mais créneau toujours réservé',
                'severity_default' => 'faible',
                'icon' => 'fas fa-user-times',
                'color' => '#28a745',
                'active' => true,
                'order' => 5
            ],
            'medecin_indisponible' => [
                'id' => 6,
                'label' => 'Médecin indisponible',
                'description' => 'Médecin en congé ou indisponible pendant le créneau',
                'severity_default' => 'critique',
                'icon' => 'fas fa-user-md-times',
                'color' => '#dc3545',
                'active' => true,
                'order' => 6
            ]
        ];
    }
}
?>
