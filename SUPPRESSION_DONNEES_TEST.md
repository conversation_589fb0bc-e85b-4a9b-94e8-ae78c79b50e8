# 🧹 Guide de Suppression des Données de Test

## 🎯 **Objectif**
Ce guide vous explique comment supprimer les données de test de votre agenda médical pour nettoyer votre base de données.

## ⚠️ **ATTENTION**
- **Ces actions sont irréversibles**
- **Sauvegardez votre base de données** avant de procéder si vous avez des données importantes
- **Vérifiez bien** ce que vous supprimez

## 🚀 **Méthodes de Suppression**

### **Méthode 1 : Via l'Interface Web (RECOMMANDÉE)**

1. **Accéder au diagnostic :**
   - Ouvrir http://localhost:3000
   - Se connecter à l'application
   - Aller dans la vue "Médecin"
   - Cliquer sur l'onglet "Diagnostic" (🐛)

2. **Supprimer les données de test :**
   - Cliquer sur "Supprimer données de test" (bouton rouge)
   - Confirmer la suppression dans la popup
   - Attendre la confirmation de succès

3. **Actions dangereuses (si nécessaire) :**
   - Cliquer sur "Actions dangereuses" pour révéler les options avancées
   - **"Supprimer TOUTES les données"** : Supprime tout sauf les admins
   - **"Réinitialiser les tables"** : Vide complètement les tables de données

### **Méthode 2 : Via Script PHP**

```bash
# Depuis le dossier racine du projet
php api/scripts/remove_test_data.php
```

### **Méthode 3 : Via SQL Direct**

```bash
# Se connecter à MySQL et exécuter
mysql -u root -p agenda_medical < remove_test_data.sql
```

### **Méthode 4 : Via API Direct**

```bash
# Supprimer seulement les données de test
curl -X DELETE http://localhost:8000/api/cleanup/test-data

# Supprimer TOUTES les données (DANGEREUX)
curl -X DELETE http://localhost:8000/api/cleanup/all-data

# Réinitialiser les tables
curl -X DELETE http://localhost:8000/api/cleanup/reset-tables
```

## 📊 **Ce qui sera supprimé**

### **Suppression des données de test uniquement :**
- **👨‍⚕️ Médecins de test :**
  - Dr. Jean Dupont (<EMAIL>)
  - Dr. Marie Martin (<EMAIL>)
  - Dr. Pierre Bernard (<EMAIL>)

- **👤 Patients de test :**
  - Sophie Durand (<EMAIL>)
  - Paul Moreau (<EMAIL>)
  - Claire Petit (<EMAIL>)
  - Michel Roux (<EMAIL>)

- **📅 Rendez-vous de test :** Tous les RDV avec la note "Rendez-vous de test"
- **🕒 Créneaux suggérés de test :** Créneaux avec raisons spécifiques de test
- **⚠️ Conflits de test :** Conflits contenant "test" dans la description

### **Suppression de TOUTES les données (DANGEREUX) :**
- **TOUS** les médecins et patients
- **TOUS** les rendez-vous
- **TOUS** les créneaux suggérés
- **TOUS** les conflits
- ✅ **Préservé :** Comptes administrateurs

### **Réinitialisation des tables :**
- Vide complètement les tables : `rendez_vous`, `creneaux_suggeres`, `conflits`
- ✅ **Préservé :** Table `utilisateur` (tous les comptes)

## 🔍 **Vérification après Suppression**

Après la suppression, vérifiez que :

1. **Les données de test ont disparu :**
   - Plus de médecins/patients avec les emails de test
   - Plus de rendez-vous de test dans le tableau de bord
   - Plus de créneaux suggérés de test

2. **Les données importantes sont préservées :**
   - Vos vrais comptes utilisateurs existent toujours
   - Vos vrais rendez-vous sont toujours là (si vous en aviez)

3. **L'application fonctionne normalement :**
   - Connexion possible
   - Interface responsive
   - Pas d'erreurs dans la console

## 🛠️ **Dépannage**

### **Erreurs communes :**

- **"Table doesn't exist"** → Normal si certaines tables optionnelles n'existent pas
- **"Foreign key constraint"** → L'ordre de suppression est géré automatiquement
- **"Access denied"** → Vérifiez les permissions de votre utilisateur MySQL

### **Si quelque chose se passe mal :**

1. **Restaurer depuis une sauvegarde** (si vous en avez une)
2. **Recréer les données nécessaires** avec les scripts d'ajout
3. **Vérifier les logs** dans la console du navigateur et du serveur

## 📋 **Commandes de Vérification**

### **Vérifier l'état de la base de données :**

```sql
-- Compter les utilisateurs
SELECT role, COUNT(*) as count FROM utilisateur GROUP BY role;

-- Compter les rendez-vous
SELECT COUNT(*) as total_appointments FROM rendez_vous;

-- Compter les créneaux suggérés (si la table existe)
SELECT COUNT(*) as total_slots FROM creneaux_suggeres;

-- Lister les utilisateurs restants
SELECT nom, prenom, email, role FROM utilisateur ORDER BY role, nom;
```

### **Via l'interface de diagnostic :**
- Utiliser le bouton "Vérifier les données"
- Utiliser les boutons "Test API" pour vérifier les endpoints

## 🎯 **Recommandations**

1. **Toujours sauvegarder** avant de supprimer des données importantes
2. **Commencer par la suppression des données de test** uniquement
3. **Utiliser l'interface web** qui est plus sûre et interactive
4. **Vérifier les résultats** après chaque suppression
5. **Garder les scripts** pour usage futur

## 🆘 **En cas d'urgence**

Si vous avez supprimé des données importantes par erreur :

1. **Arrêter immédiatement** toute autre opération
2. **Restaurer depuis une sauvegarde** si disponible
3. **Recréer les données manuellement** si nécessaire
4. **Utiliser les scripts d'ajout** pour recréer des données de test

---

**⚠️ Rappel : Ces opérations sont irréversibles. Procédez avec prudence !**
