<!-- src/components/patient/PatientBooking.vue -->
<template>
  <div class="patient-booking">
    <div class="booking-container">
      <!-- En-tête avec icône -->
      <div class="header">
        <div class="header-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H12V15H7V10Z" fill="currentColor"/>
          </svg>
        </div>
        <h2 class="header-title">Prendre un rendez-vous</h2>
        <p class="header-subtitle">Choisis<PERSON>z votre médecin et votre créneau préféré</p>
      </div>
    
      <form @submit.prevent="handleSubmit" class="booking-form">
        <!-- Sélection du médecin -->
        <div class="form-group">
          <label for="doctor" class="form-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none">
              <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
            </svg>
            Médecin
          </label>
          <div class="select-wrapper">
            <select v-model="form.doctor_id" @change="loadAvailability" required class="form-select">
              <option value="">Choisir un médecin</option>
              <option v-for="doctor in doctors" :key="doctor.id" :value="doctor.id">
                {{ doctor.fullName }} - {{ doctor.specialite || 'Autre' }}
              </option>
            </select>
            <svg class="select-arrow" viewBox="0 0 24 24">
              <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
            </svg>
          </div>
        </div>

        <!-- Sélection de la date -->
        <div class="form-group">
          <label for="date" class="form-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none">
              <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19Z" fill="currentColor"/>
            </svg>
            Date
          </label>
          <input 
            v-model="form.date" 
            type="date" 
            :min="minDate"
            @change="loadAvailability"
            required
            class="form-input"
          >
        </div>

        <!-- Créneaux disponibles -->
        <div v-if="availableSlots.length > 0" class="form-group">
          <label class="form-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none">
              <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2ZM17 13H11V7H12.5V11.5H17V13Z" fill="currentColor"/>
            </svg>
            Créneaux disponibles
          </label>
          <div class="time-slots">
            <button
              v-for="slot in availableSlots"
              :key="slot.id"
              type="button"
              class="time-slot"
              :class="{ active: form.start_time === slot.time }"
              @click="selectTimeSlot(slot.time)"
            >
              <span class="time-text">{{ slot.time }}</span>
              <span class="duration-text">{{ slot.duration }} min</span>
              <div class="slot-indicator"></div>
              <div v-if="slot.slotData?.raison" class="slot-reason">
                {{ slot.slotData.raison }}
              </div>
            </button>
          </div>
        </div>

        <!-- Message si pas de créneaux -->
        <div v-else-if="form.doctor_id && form.date" class="no-slots">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
          </svg>
          <p>Aucun créneau disponible pour cette date</p>
        </div>

        <!-- Notes -->
        <div class="form-group">
          <label for="notes" class="form-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none">
              <path d="M14 2H6C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z" fill="currentColor"/>
            </svg>
            Notes (optionnel)
          </label>
          <textarea 
            v-model="form.notes" 
            rows="3" 
            class="form-textarea"
            placeholder="Ajoutez des informations complémentaires..."
          ></textarea>
        </div>

        <!-- Alertes de conflit -->
        <div v-if="appointmentStore.hasConflicts" class="conflict-alert">
          <div class="conflict-header">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M1 21H23L12 2L1 21ZM13 18H11V16H13V18ZM13 14H11V10H13V14Z" fill="currentColor"/>
            </svg>
            <h4>Conflits détectés</h4>
          </div>
          <ul class="conflict-list">
            <li v-for="conflict in appointmentStore.conflicts" :key="conflict.id">
              {{ conflict.message }}
            </li>
          </ul>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button 
            type="submit" 
            :disabled="!canSubmit || appointmentStore.loading"
            class="btn-primary"
          >
            <svg v-if="appointmentStore.loading" class="loading-spinner" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"/>
              <path d="M4 12C4 7.58 7.58 4 12 4" stroke="currentColor" stroke-width="4" stroke-linecap="round" fill="none"/>
            </svg>
            <svg v-else class="btn-icon" viewBox="0 0 24 24" fill="none">
              <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
            </svg>
            {{ appointmentStore.loading ? 'Création en cours...' : 'Confirmer le rendez-vous' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useDoctorStore } from '@/stores/doctorStore'
import { useAuthStore } from '@/stores/authStore'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import suggestedSlotService from '@/services/suggestedSlotService'


const appointmentStore = useAppointmentStore()
const doctorStore = useDoctorStore()
const authStore = useAuthStore()
const suggestedSlotStore = useSuggestedSlotStore()

const form = ref({
  doctor_id: '',
  patient_id: authStore.user?.id || 1, // ID du patient connecté
  date: '',
  start_time: '',
  end_time: '',
  type: 'consultation',
  notes: ''
})

const availableSlots = ref([])

const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const doctors = computed(() => doctorStore.doctors)

const canSubmit = computed(() => {
  return form.value.doctor_id && 
         form.value.date && 
         form.value.start_time
})

onMounted(async () => {
  try {
    console.log('Chargement des médecins...')
    await doctorStore.fetchDoctors()
    console.log('Médecins chargés:', doctors.value)
  } catch (error) {
    console.error('Erreur lors du chargement des médecins:', error)
  }
})
const loading = ref(false)

const loadAvailability = async () => {
  if (form.value.doctor_id && form.value.date) {
    try {
      loading.value = true
      console.log('Chargement des créneaux pour médecin:', form.value.doctor_id, 'date:', form.value.date)

      // Charger tous les créneaux du médecin
      await suggestedSlotStore.loadSlotsByDoctor(form.value.doctor_id, false)

      // Filtrer les créneaux disponibles pour la date sélectionnée
      const allSlots = suggestedSlotStore.slots
      const dateSlots = allSlots.filter(slot => {
        const slotDate = slot.date_heure_suggeree.split(' ')[0]
        return slotDate === form.value.date &&
               slot.statut === 'disponible' &&
               new Date(slot.date_heure_suggeree) > new Date()
      })

      // Convertir en format attendu par l'interface
      availableSlots.value = dateSlots.map(slot => {
        const time = slot.date_heure_suggeree.split(' ')[1].slice(0, 5)
        return {
          id: slot.id,
          time: time,
          duration: slot.duree,
          available: true,
          slotData: slot
        }
      }).sort((a, b) => a.time.localeCompare(b.time))

      console.log('Créneaux disponibles trouvés:', availableSlots.value)
    } catch (error) {
      console.error('Erreur lors du chargement des créneaux:', error)
      availableSlots.value = []
    } finally {
      loading.value = false
    }
  } else {
    availableSlots.value = []
  }
}

watch([() => form.value.doctor_id, () => form.value.date], () => {
  loadAvailability()
})

const selectedSlot = ref(null)

const selectTimeSlot = (time) => {
  form.value.start_time = time

  // Trouver le créneau correspondant
  const slot = availableSlots.value.find(s => s.time === time)
  if (slot) {
    selectedSlot.value = slot.slotData
    // Calculer l'heure de fin basée sur la durée du créneau
    const startDate = new Date(`${form.value.date}T${time}`)
    const endDate = new Date(startDate.getTime() + slot.duration * 60000)
    form.value.end_time = endDate.toTimeString().slice(0, 5)
  }
}

const handleSubmit = async () => {
  if (!form.value.doctor_id || !form.value.patient_id || !form.value.date || !form.value.start_time) {
    alert('Veuillez remplir tous les champs obligatoires')
    return
  }

  if (!selectedSlot.value) {
    alert('Veuillez sélectionner un créneau valide')
    return
  }

  try {
    // 1. Créer le rendez-vous
    const appointmentData = {
      id_medecin: form.value.doctor_id,
      id_patient: form.value.patient_id,
      date_rendez_vous: `${form.value.date} ${form.value.start_time}:00`,
      duree: selectedSlot.value.duree,
      type: form.value.type,
      notes: form.value.notes,
      statut: 'planifie',
      priorite: 'moyenne'
    }

    console.log('Création du rendez-vous:', appointmentData)
    const result = await appointmentStore.createAppointment(appointmentData)

    if (result.success) {
      // 2. Marquer le créneau comme accepté et assigner le patient
      console.log('Marquage du créneau comme accepté:', selectedSlot.value.id)
      await suggestedSlotStore.updateAcceptance(
        selectedSlot.value.id,
        true,
        form.value.patient_id
      )

      alert('Rendez-vous confirmé avec succès !')
      resetForm()

      // Recharger les créneaux pour mettre à jour l'affichage
      await loadAvailability()
    } else {
      alert('Erreur lors de la création du rendez-vous: ' + (result.error || 'Erreur inconnue'))
    }
  } catch (error) {
    console.error('Erreur lors de la création:', error)
    alert('Erreur lors de la création du rendez-vous')
  }
}

const resetForm = () => {
  form.value = {
    doctor_id: '',
    patient_id: authStore.user?.id || 1,
    date: '',
    start_time: '',
    end_time: '',
    type: 'consultation',
    notes: ''
  }
  availableSlots.value = []
  selectedSlot.value = null
}
</script>

<style scoped>
.patient-booking {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 600px;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.header-icon svg {
  width: 30px;
  height: 30px;
}

.header-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  color: #718096;
  font-size: 16px;
  margin: 0;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.label-icon {
  width: 18px;
  height: 18px;
  color: #667eea;
}

.form-input, .form-textarea {
  padding: 15px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
  color: #2d3748;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.select-wrapper {
  position: relative;
}

.form-select {
  width: 100%;
  padding: 15px 50px 15px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  color: #2d3748;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.select-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #a0aec0;
  pointer-events: none;
  transition: transform 0.3s ease;
}

.form-select:focus + .select-arrow {
  transform: translateY(-50%) rotate(180deg);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-textarea::placeholder {
  color: #a0aec0;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.time-slot {
  position: relative;
  padding: 15px 20px;
  border: 2px solid #e2e8f0;
  background: white;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #4a5568;
  overflow: hidden;
}

.time-slot::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: left 0.3s ease;
  z-index: 1;
}

.time-slot:hover::before,
.time-slot.active::before {
  left: 0;
}

.time-text {
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
  font-weight: 600;
  font-size: 16px;
}

.duration-text {
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
  font-size: 12px;
  opacity: 0.8;
}

.slot-reason {
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
  font-size: 11px;
  opacity: 0.7;
  text-align: center;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-slot:hover .time-text,
.time-slot.active .time-text,
.time-slot:hover .duration-text,
.time-slot.active .duration-text,
.time-slot:hover .slot-reason,
.time-slot.active .slot-reason {
  color: white;
}

.slot-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #48bb78;
  position: relative;
  z-index: 2;
  transition: background 0.3s ease;
}

.time-slot:hover .slot-indicator,
.time-slot.active .slot-indicator {
  background: rgba(255, 255, 255, 0.8);
}

.time-slot:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.time-slot.active {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.no-slots {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  border-radius: 12px;
  color: #718096;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.no-slots svg {
  width: 40px;
  height: 40px;
  color: #a0aec0;
}

.conflict-alert {
  background: linear-gradient(135deg, #fed7d7, #feb2b2);
  border: 1px solid #fc8181;
  padding: 20px;
  border-radius: 12px;
  color: #742a2a;
}

.conflict-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.conflict-header svg {
  width: 24px;
  height: 24px;
  color: #e53e3e;
}

.conflict-header h4 {
  margin: 0;
  font-weight: 600;
}

.conflict-list {
  margin: 0;
  padding-left: 20px;
}

.conflict-list li {
  margin-bottom: 8px;
}

.form-actions {
  margin-top: 10px;
}

.btn-primary {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 18px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #764ba2, #667eea);
  transition: left 0.3s ease;
}

.btn-primary:hover::before {
  left: 0;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary:disabled::before {
  display: none;
}

.btn-icon, .loading-spinner {
  width: 20px;
  height: 20px;
  position: relative;
  z-index: 2;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Animations d'entrée */
.booking-container {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 640px) {
  .booking-container {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .header-title {
    font-size: 24px;
  }
  
  .time-slots {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }
  
  .time-slot {
    padding: 12px 16px;
  }
}
</style>