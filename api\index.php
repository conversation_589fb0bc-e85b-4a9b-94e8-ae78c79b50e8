<?php
/**
 * API réelle avec AuthController
 */

// Headers CORS pour permettre les requêtes depuis l'interface Vue
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Gérer les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Charger les dépendances
require_once 'config/database.php';
require_once 'controllers/AuthController.php';
require_once 'controllers/PatientController.php';
require_once 'controllers/PatientHistoryController.php';
require_once 'controllers/DocumentController.php';
require_once 'controllers/AppointmentController.php';
require_once 'middleware/AuthMiddleware.php';
require_once 'controllers/AdminController.php';
require_once 'controllers/DashboardController.php';

// Initialiser la base de données
$database = Database::getInstance();
$db = $database->getConnection();

// Initialiser les contrôleurs
$authController = new AuthController($db);
$patientController = new PatientController($db);
$historyController = new PatientHistoryController($db);
$documentController = new DocumentController($db);
$appointmentController = new AppointmentController($db);
$adminController = new AdminController($db);
$dashboardController = new DashboardController($db);

// URI - Nettoyer correctement l'URI
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = trim($uri, '/');

// Supprimer index.php de l'URI si présent
$uri = str_replace('index.php/', '', $uri);
$uri = str_replace('index.php', '', $uri);
$uri = trim($uri, '/');

// Log pour debug
error_log("URI reçue: " . $_SERVER['REQUEST_URI']);
error_log("URI nettoyée: " . $uri);
error_log("Méthode: " . $_SERVER['REQUEST_METHOD']);

// Router avec AuthController réel
if ($uri === 'auth/login' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $authController->login();
} elseif ($uri === 'auth/verify' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $authController->verifyToken();
} elseif ($uri === 'auth/register' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $authController->register();
} elseif ($uri === 'patients' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $patientController->index();
} elseif ($uri === 'patients' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $patientController->store();
} elseif ($uri === 'slot-types' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/SlotTypeController.php';
    $slotTypeController = new SlotTypeController($db);
    $slotTypeController->index();
} elseif ($uri === 'slot-types/active' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/SlotTypeController.php';
    $slotTypeController = new SlotTypeController($db);
    $slotTypeController->getActive();
} elseif ($uri === 'slot-types' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'controllers/SlotTypeController.php';
    $slotTypeController = new SlotTypeController($db);
    $slotTypeController->store();
} elseif (preg_match('#^slot-types/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    require_once 'controllers/SlotTypeController.php';
    $slotTypeController = new SlotTypeController($db);
    $slotTypeController->update($matches[1]);
} elseif ($uri === 'slot-types/stats' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Route pour les statistiques des types de créneaux
    try {
        $stmt = $db->prepare("
            SELECT
                tc.id,
                tc.nom,
                tc.duree,
                tc.prix_base,
                COUNT(cs.id) as nb_creneaux_suggeres,
                COUNT(CASE WHEN cs.accepte = 1 THEN 1 END) as nb_acceptes,
                COUNT(CASE WHEN cs.accepte = 0 THEN 1 END) as nb_refuses,
                COUNT(CASE WHEN cs.accepte IS NULL THEN 1 END) as nb_en_attente
            FROM types_creneaux tc
            LEFT JOIN creneaux_suggeres cs ON tc.id = cs.type_creneau_id
            WHERE tc.actif = 1
            GROUP BY tc.id, tc.nom, tc.duree, tc.prix_base
            ORDER BY tc.duree
        ");
        $stmt->execute();
        $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'status' => 'success',
            'data' => $stats
        ]);
    } catch (Exception $e) {
        error_log("Erreur slot-types/stats: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['message' => 'Erreur lors de la récupération des statistiques']);
    }
} elseif (preg_match('#^slot-types/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    require_once 'controllers/SlotTypeController.php';
    $slotTypeController = new SlotTypeController($db);
    $slotTypeController->delete($matches[1]);
} elseif ($uri === 'suggested-slots' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/SuggestedSlotController.php';
    $suggestedSlotController = new SuggestedSlotController($db);
    $suggestedSlotController->index();
} elseif ($uri === 'suggested-slots' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'controllers/SuggestedSlotController.php';
    $suggestedSlotController = new SuggestedSlotController($db);
    $suggestedSlotController->store();
} elseif (preg_match('#^suggested-slots/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    require_once 'controllers/SuggestedSlotController.php';
    $suggestedSlotController = new SuggestedSlotController($db);
    $suggestedSlotController->delete($matches[1]);
} elseif ($uri === 'appointments' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $appointmentController->index();
} elseif ($uri === 'appointments' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $appointmentController->store();
} elseif (preg_match('#^appointments/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    $appointmentController->update($matches[1]);
} elseif (preg_match('#^appointments/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $appointmentController->delete($matches[1]);
} elseif (preg_match('#^appointments/doctor/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Route pour récupérer les rendez-vous d'un médecin spécifique
    try {
        $doctorId = $matches[1];
        $stmt = $db->prepare("
            SELECT r.*,
                   p.nom as patient_nom, p.prenom as patient_prenom,
                   u.nom as medecin_nom, u.prenom as medecin_prenom
            FROM rendez_vous r
            LEFT JOIN patients p ON r.id_patient = p.id
            LEFT JOIN utilisateur u ON r.id_medecin = u.id
            WHERE r.id_medecin = ?
            ORDER BY r.date_rendez_vous DESC
        ");
        $stmt->execute([$doctorId]);
        $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Formater les données
        $formattedAppointments = array_map(function($appointment) {
            return [
                'id' => $appointment['id'],
                'date_rendez_vous' => $appointment['date_rendez_vous'],
                'patient_nom' => $appointment['patient_nom'] . ' ' . $appointment['patient_prenom'],
                'medecin_nom' => $appointment['medecin_nom'] . ' ' . $appointment['medecin_prenom'],
                'type' => $appointment['type'] ?? 'Consultation',
                'statut' => $appointment['statut'] ?? 'programmé',
                'notes' => $appointment['notes'] ?? '',
                'duree' => $appointment['duree'] ?? 30,
                'id_patient' => $appointment['id_patient'],
                'id_medecin' => $appointment['id_medecin']
            ];
        }, $appointments);

        echo json_encode([
            'status' => 'success',
            'data' => $formattedAppointments
        ]);
    } catch (Exception $e) {
        error_log("Erreur appointments/doctor/{id}: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['message' => 'Erreur lors de la récupération des rendez-vous du médecin']);
    }
} elseif (preg_match('#^doctor/statistics/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    $dashboardController->getDoctorStatistics($matches[1]);
} elseif (preg_match('#^suggested-slots/doctor/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/SuggestedSlotController.php';
    $suggestedSlotController = new SuggestedSlotController($db);
    $_GET['medecin_id'] = $matches[1]; // Passer l'ID du médecin
    $suggestedSlotController->index();
} elseif ($uri === 'suggested-slots/stats' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/SuggestedSlotController.php';
    $suggestedSlotController = new SuggestedSlotController($db);
    $suggestedSlotController->getStats();
} elseif ($uri === 'slot-types/stats' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/SlotTypeController.php';
    $slotTypeController = new SlotTypeController($db);
    $slotTypeController->getStats();
} elseif ($uri === 'doctors' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Route pour récupérer tous les médecins
    try {
        $stmt = $db->prepare("
            SELECT m.id, m.nom, m.prenom, m.email, m.specialite, m.telephone,
                   u.id as user_id, u.role
            FROM medecins m
            LEFT JOIN utilisateur u ON m.user_id = u.id
            WHERE u.role = 'doctor'
            ORDER BY m.nom, m.prenom
        ");
        $stmt->execute();
        $medecins = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Formater les données
        $formattedMedecins = array_map(function($medecin) {
            return [
                'id' => $medecin['id'],
                'user_id' => $medecin['user_id'],
                'nom' => $medecin['nom'],
                'prenom' => $medecin['prenom'],
                'email' => $medecin['email'],
                'specialite' => $medecin['specialite'] ?? 'Médecine générale',
                'telephone' => $medecin['telephone'] ?? '',
                'role' => $medecin['role']
            ];
        }, $medecins);

        echo json_encode([
            'status' => 'success',
            'data' => $formattedMedecins
        ]);
    } catch (Exception $e) {
        error_log("Erreur doctors: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['message' => 'Erreur lors de la récupération des médecins']);
    }
} elseif (preg_match('#^doctor/statistics/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Route pour les statistiques d'un médecin
    $userId = $matches[1];

    try {
        // D'abord récupérer l'ID médecin depuis l'user_id
        $stmt = $db->prepare("SELECT id FROM medecins WHERE user_id = ?");
        $stmt->execute([$userId]);
        $medecin = $stmt->fetch();

        if (!$medecin) {
            http_response_code(404);
            echo json_encode(['message' => 'Médecin non trouvé']);
            exit;
        }

        $medecinId = $medecin['id'];

        // Statistiques des rendez-vous
        $stmt = $db->prepare("
            SELECT
                COUNT(*) as total_rdv,
                COUNT(CASE WHEN DATE(date_rendez_vous) = CURDATE() THEN 1 END) as rdv_aujourd_hui,
                COUNT(CASE WHEN date_rendez_vous > NOW() THEN 1 END) as rdv_futurs,
                COUNT(CASE WHEN statut = 'confirme' THEN 1 END) as rdv_confirmes,
                COUNT(CASE WHEN statut = 'planifie' THEN 1 END) as rdv_planifies,
                COUNT(CASE WHEN statut = 'annule' THEN 1 END) as rdv_annules
            FROM rendez_vous
            WHERE id_medecin = ?
        ");
        $stmt->execute([$medecinId]);
        $rdvStats = $stmt->fetch();

        // Statistiques des créneaux suggérés
        $stmt = $db->prepare("
            SELECT
                COUNT(*) as total_creneaux,
                COUNT(CASE WHEN accepte = 1 THEN 1 END) as creneaux_acceptes,
                COUNT(CASE WHEN accepte = 0 THEN 1 END) as creneaux_refuses,
                COUNT(CASE WHEN accepte IS NULL THEN 1 END) as creneaux_en_attente
            FROM creneaux_suggeres
            WHERE id_medecin = ?
        ");
        $stmt->execute([$medecinId]);
        $creneauxStats = $stmt->fetch();

        // Patients uniques
        $stmt = $db->prepare("
            SELECT COUNT(DISTINCT id_patient) as patients_uniques
            FROM rendez_vous
            WHERE id_medecin = ?
        ");
        $stmt->execute([$medecinId]);
        $patientsStats = $stmt->fetch();

        $statistics = [
            'medecin_id' => $medecinId,
            'user_id' => $userId,
            'rendez_vous' => $rdvStats,
            'creneaux_suggeres' => $creneauxStats,
            'patients' => $patientsStats
        ];

        echo json_encode([
            'status' => 'success',
            'data' => $statistics
        ]);
    } catch (Exception $e) {
        error_log("Erreur doctor/statistics: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['message' => 'Erreur lors de la récupération des statistiques']);
    }
} elseif (preg_match('#^doctors/user/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Route pour récupérer un médecin par son user_id
    try {
        $stmt = $db->prepare("SELECT id, nom, prenom, email
                             FROM utilisateur
                             WHERE id = ? AND role = 'doctor'");
        $stmt->execute([$matches[1]]);
        $medecin = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($medecin) {
            echo json_encode([
                'status' => 'success',
                'data' => $medecin
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['message' => 'Médecin non trouvé']);
        }
    } catch (Exception $e) {
        error_log("Erreur doctors/user/{id}: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['message' => 'Erreur lors de la récupération du médecin: ' . $e->getMessage()]);
    }
} elseif (preg_match('#^medecins/(\d+)$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // Route pour récupérer les données d'un médecin spécifique
    try {
        $stmt = $db->prepare("SELECT id, nom, prenom, email
                             FROM utilisateur
                             WHERE id = ? AND role = 'doctor'");
        $stmt->execute([$matches[1]]);
        $medecin = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($medecin) {
            echo json_encode([
                'status' => 'success',
                'data' => $medecin
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['message' => 'Médecin non trouvé']);
        }
    } catch (Exception $e) {
        error_log("Erreur medecins/{id}: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['message' => 'Erreur lors de la récupération du médecin: ' . $e->getMessage()]);
    }
} elseif ($uri === 'conflicts' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->index();
} elseif ($uri === 'conflicts/stats' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->getStats();
} elseif ($uri === 'conflicts/trends' && $_SERVER['REQUEST_METHOD'] === 'GET') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->analyzeTrends();
} elseif ($uri === 'conflicts/auto-cleanup' && $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->autoCleanup();
} elseif ($uri === 'conflicts/schedule-notifications' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->scheduleNotifications();
} elseif ($uri === 'conflicts/notify' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->notifyConflicts();
} elseif ($uri === 'conflicts/detect' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->detectConflicts();
} elseif ($uri === 'conflicts/expired' && $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->autoCleanup(); // Utiliser la même méthode
} elseif ($uri === 'conflicts/validate-slot' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->validateTimeSlot();
} elseif (preg_match('#^conflicts/(\d+)/resolve$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->resolve($matches[1]);
} elseif (preg_match('#^conflicts/(\d+)/ignore$#', $uri, $matches) && $_SERVER['REQUEST_METHOD'] === 'PUT') {
    require_once 'controllers/ConflictController.php';
    $conflictController = new ConflictController($db);
    $conflictController->ignore($matches[1]);
} elseif ($uri === 'test') {
    echo json_encode([
        'status' => 'success',
        'message' => 'API réelle fonctionnelle',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} else {
    http_response_code(404);
    echo json_encode(['message' => 'Route non trouvée: ' . $uri]);
}
?>
