<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../controllers/AuthController.php';

// Log des données reçues
$rawData = file_get_contents("php://input");
error_log("Données brutes reçues : " . $rawData);

$data = json_decode($rawData, true);
error_log("Données décodées : " . print_r($data, true));

$email = $data['email'] ?? '';
$password = $data['password'] ?? '';

if (!$email || !$password) {
    http_response_code(400);
    echo json_encode(['message' => 'Email et mot de passe requis.']);
    error_log("Données manquantes - email: " . ($email ? 'présent' : 'manquant') . ", password: " . ($password ? 'présent' : 'manquant'));
    exit;
}

try {
    $pdo = Database::getInstance()->getConnection();
    
    // Recherche l'utilisateur par email
    $stmt = $pdo->prepare("SELECT id, nom, prenom, email, password, role FROM utilisateur WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();

    error_log("Recherche utilisateur - email: " . $email);
    error_log("Utilisateur trouvé : " . ($user ? "oui" : "non"));
    
    if ($user) {
        error_log("Mot de passe fourni (non haché) : " . substr($password, 0, 3) . "***");
        error_log("Mot de passe stocké (haché) : " . $user['password']);
        
        // Vérification du mot de passe
        if (password_verify($password, $user['password'])) {
            // Authentification réussie
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['role'] = $user['role'];

            // Créer une instance de AuthController avec la connexion à la base de données
            $authController = new AuthController($pdo);
            $token = $authController->generateJWT($user);

            $response = [
                'message' => 'Connexion réussie.',
                'user' => [
                    'id' => $user['id'],
                    'nom' => $user['nom'],
                    'prenom' => $user['prenom'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ],
                'token' => $token
            ];

            error_log("Connexion réussie pour l'utilisateur : " . $user['email']);
            error_log("Rôle de l'utilisateur : " . $user['role']);
            error_log("Réponse envoyée : " . json_encode($response));
            
            echo json_encode($response);
            exit;
        }
    }
    
    // Si on arrive ici, soit l'utilisateur n'existe pas, soit le mot de passe est incorrect
    http_response_code(401);
    echo json_encode(['message' => 'Email ou mot de passe incorrect.']);
    error_log("Échec de l'authentification - Utilisateur non trouvé ou mot de passe incorrect pour l'email : " . $email);
    
} catch (Exception $e) {
    error_log("Erreur lors de la connexion : " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['message' => 'Erreur lors de la connexion']);
}
?>
