<?php

class AuthController {
    private $db;
    private $secret_key;

    public function __construct($db) {
        $this->db = $db;
        $config = require __DIR__ . '/../config/jwt.php';
        $this->secret_key = $config['secret_key'];
    }

    public function login() {
        error_log("=== DÉBUT LOGIN ===");
        error_log("Method: " . $_SERVER['REQUEST_METHOD']);
        error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'non défini'));

        // Récupérer les données POST
        $rawData = file_get_contents("php://input");
        error_log("Raw data reçu: " . $rawData);

        $data = json_decode($rawData, true); // Utiliser true pour avoir un array
        error_log("Data décodé: " . print_r($data, true));

        // Vérifier si les données sont dans $_POST (form data)
        if (empty($data) && !empty($_POST)) {
            error_log("Utilisation de \$_POST: " . print_r($_POST, true));
            $data = $_POST;
        }

        // Vérifier les champs requis
        $email = $data['email'] ?? null;
        $password = $data['password'] ?? null;

        error_log("Email extrait: " . ($email ?? 'null'));
        error_log("Password extrait: " . ($password ? '[PRÉSENT]' : 'null'));

        if (!$email || !$password) {
            error_log("Email ou mot de passe manquant");
            http_response_code(400);
            echo json_encode(["message" => "Email et mot de passe requis"]);
            return;
        }

        try {
            error_log("Tentative de connexion pour l'email: " . $email);

            // Préparer la requête pour la table utilisateur
            $query = "SELECT id, nom, prenom, email, password, role FROM utilisateur WHERE email = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$email]);

            if ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
                error_log("Utilisateur trouvé: " . $user['nom'] . " " . $user['prenom'] . " (" . $user['role'] . ")");
                error_log("Hash en base: " . substr($user['password'], 0, 20) . "...");

                // Vérifier le mot de passe
                $passwordCheck = password_verify($password, $user['password']);
                error_log("Vérification mot de passe: " . ($passwordCheck ? 'SUCCÈS' : 'ÉCHEC'));

                if ($passwordCheck) {
                    error_log("Mot de passe correct, génération du token");
                    
                    // Générer le token JWT
                    $token = $this->generateJWT($user);
                    
                    http_response_code(200);
                    echo json_encode([
                        "message" => "Connexion réussie",
                        "token" => $token,
                        "user" => [
                            "id" => $user['id'],
                            "nom" => $user['nom'],
                            "prenom" => $user['prenom'],
                            "email" => $user['email'],
                            "role" => $user['role']
                        ]
                    ]);
                    error_log("Connexion réussie pour l'utilisateur: " . $user['email']);
                } else {
                    error_log("Mot de passe incorrect pour l'email: " . $email);
                    error_log("Password fourni: " . $password);
                    error_log("Hash attendu: " . $user['password']);
                    http_response_code(401);
                    echo json_encode(["message" => "Email ou mot de passe incorrect"]);
                }
            } else {
                error_log("Aucun utilisateur trouvé pour l'email: " . $email);

                // Lister les emails disponibles pour debug
                $debugQuery = "SELECT email FROM utilisateur LIMIT 5";
                $debugStmt = $this->db->prepare($debugQuery);
                $debugStmt->execute();
                $availableEmails = $debugStmt->fetchAll(PDO::FETCH_COLUMN);
                error_log("Emails disponibles: " . implode(', ', $availableEmails));

                http_response_code(401);
                echo json_encode(["message" => "Email ou mot de passe incorrect"]);
            }
        } catch (Exception $e) {
            error_log("Erreur lors de la connexion: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(["message" => "Erreur lors de la connexion"]);
        }
    }

    public function generateJWT($user) {
        $issued_at = time();
        $expiration = $issued_at + (60 * 60); // Token valide pour 1 heure

        $payload = [
            "iat" => $issued_at,
            "exp" => $expiration,
            "user" => [
                "id" => $user['id'],
                "nom" => $user['nom'],
                "prenom" => $user['prenom'],
                "email" => $user['email'],
                "role" => $user['role']
            ]
        ];

        // Encoder en base64
        $header = base64_encode(json_encode(['typ' => 'JWT', 'alg' => 'HS256']));
        $payload = base64_encode(json_encode($payload));
        
        // Créer la signature
        $signature = hash_hmac('sha256', "$header.$payload", $this->secret_key, true);
        $signature = base64_encode($signature);

        return "$header.$payload.$signature";
    }

    public function validateToken($token) {
        try {
            // Séparer le token en ses composants
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                error_log("Format de token invalide");
                return false;
            }

            list($header, $payload, $signature) = $parts;

            // Vérifier la signature
            $valid_signature = base64_encode(
                hash_hmac('sha256', "$header.$payload", $this->secret_key, true)
            );

            if ($signature !== $valid_signature) {
                error_log("Signature du token invalide");
                return false;
            }

            // Décoder le payload
            $payload_data = json_decode(base64_decode($payload), true);

            // Vérifier l'expiration
            if (isset($payload_data['exp']) && $payload_data['exp'] < time()) {
                error_log("Token expiré");
                return false;
            }

            return $payload_data;
        } catch (Exception $e) {
            error_log("Erreur lors de la validation du token: " . $e->getMessage());
            return false;
        }
    }

    public function verifyToken() {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        $token = str_replace('Bearer ', '', $auth_header);

        if (!$token) {
            http_response_code(401);
            echo json_encode(['message' => 'Token non fourni.']);
            return;
        }

        $decoded = $this->validateToken($token);

        if ($decoded && isset($decoded['user'])) {
            http_response_code(200);
            echo json_encode(['user' => $decoded['user']]);
        } else {
            http_response_code(401);
            echo json_encode(['message' => 'Token invalide ou expiré.']);
        }
    }
}