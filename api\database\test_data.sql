-- Script pour insérer des données de test dans l'agenda médical

-- Insérer des patients de test
INSERT INTO patients (user_id, date_naissance, telephone, adresse) VALUES
(1, '1990-05-15', '**********', '123 Rue de la Santé, Paris'),
(2, '1985-08-22', '**********', '456 Avenue des Médecins, Lyon');

-- Insérer des rendez-vous de test pour aujourd'hui et demain
INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, duree, type, statut, notes, priorite) VALUES
-- Rendez-vous d'aujourd'hui
(1, 1, CONCAT(CURDATE(), ' 09:00:00'), 30, 'Consultation générale', 'planifie', 'Contrôle de routine', 'moyenne'),
(1, 2, CONCAT(CURDATE(), ' 10:30:00'), 45, 'Consultation spécialisée', 'confirme', 'Suivi post-opératoire', 'elevee'),
(1, 1, CONCAT(CURDATE(), ' 14:00:00'), 30, 'Consultation de suivi', 'planifie', 'Résultats d\'analyses', 'faible'),

-- Rendez-vous de demain
(1, 2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:30:00'), 30, 'Consultation générale', 'planifie', 'Consultation préventive', 'moyenne'),
(1, 1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:00:00'), 60, 'Consultation spécialisée', 'planifie', 'Bilan complet', 'elevee'),

-- Rendez-vous de la semaine prochaine
(1, 2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 7 DAY), ' 09:30:00'), 30, 'Consultation de suivi', 'planifie', 'Contrôle mensuel', 'moyenne'),

-- Rendez-vous passés (pour les statistiques)
(1, 1, CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' 15:00:00'), 30, 'Consultation générale', 'termine', 'Consultation terminée', 'moyenne'),
(1, 2, CONCAT(DATE_SUB(CURDATE(), INTERVAL 2 DAY), ' 10:00:00'), 45, 'Consultation spécialisée', 'termine', 'Consultation terminée', 'elevee'),
(1, 1, CONCAT(DATE_SUB(CURDATE(), INTERVAL 3 DAY), ' 14:30:00'), 30, 'Consultation de suivi', 'termine', 'Consultation terminée', 'faible'),

-- Rendez-vous ce mois-ci (pour les statistiques mensuelles)
(1, 2, CONCAT(DATE_SUB(CURDATE(), INTERVAL 5 DAY), ' 09:00:00'), 30, 'Consultation générale', 'termine', 'Consultation terminée', 'moyenne'),
(1, 1, CONCAT(DATE_SUB(CURDATE(), INTERVAL 7 DAY), ' 11:30:00'), 45, 'Consultation spécialisée', 'termine', 'Consultation terminée', 'elevee'),
(1, 2, CONCAT(DATE_SUB(CURDATE(), INTERVAL 10 DAY), ' 16:00:00'), 30, 'Consultation de suivi', 'termine', 'Consultation terminée', 'moyenne');

-- Créer un conflit potentiel (deux rendez-vous proches)
INSERT INTO rendez_vous (id_medecin, id_patient, date_rendez_vous, duree, type, statut, notes, priorite) VALUES
(1, 1, CONCAT(CURDATE(), ' 09:15:00'), 30, 'Consultation urgente', 'planifie', 'Rendez-vous en conflit avec 09:00', 'urgente');

-- Vérifier les données insérées
SELECT 'Patients créés:' as info;
SELECT p.id, u.nom, u.prenom, p.telephone 
FROM patients p 
JOIN utilisateur u ON p.user_id = u.id;

SELECT 'Rendez-vous créés:' as info;
SELECT 
    rdv.id,
    rdv.date_rendez_vous,
    rdv.type,
    rdv.statut,
    CONCAT(u.nom, ' ', u.prenom) as patient_nom
FROM rendez_vous rdv
JOIN patients p ON rdv.id_patient = p.id
JOIN utilisateur u ON p.user_id = u.id
ORDER BY rdv.date_rendez_vous;
