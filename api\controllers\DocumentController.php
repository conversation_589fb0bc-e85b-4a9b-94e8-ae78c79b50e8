<?php

class DocumentController {
    private $db;
    private $uploadDir;

    public function __construct($db = null) {
        $this->db = $db ?? Database::getInstance()->getConnection();
        $this->uploadDir = __DIR__ . '/../uploads/documents/';
        
        // Créer le répertoire d'upload s'il n'existe pas
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0777, true);
        }
    }

    public function getDocuments($patientId) {
        try {
            $query = "
                SELECT 
                    id,
                    nom_fichier,
                    type_fichier,
                    description,
                    date_creation,
                    taille_fichier
                FROM documents
                WHERE patient_id = ?
                ORDER BY date_creation DESC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$patientId]);
            $documents = $stmt->fetchAll(PDO::FETCH_ASSOC);

            header('Content-Type: application/json');
            echo json_encode($documents);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la récupération des documents: ' . $e->getMessage()]);
        }
    }

    public function uploadDocument($patientId) {
        try {
            if (!isset($_FILES['file'])) {
                throw new Exception('Aucun fichier n\'a été envoyé');
            }

            $file = $_FILES['file'];
            $fileName = basename($file['name']);
            $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            
            // Vérifier le type de fichier
            $allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
            if (!in_array($fileType, $allowedTypes)) {
                throw new Exception('Type de fichier non autorisé');
            }

            // Générer un nom de fichier unique
            $newFileName = uniqid() . '.' . $fileType;
            $targetPath = $this->uploadDir . $newFileName;

            // Déplacer le fichier
            if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
                throw new Exception('Erreur lors du déplacement du fichier');
            }

            // Insérer les informations dans la base de données
            $stmt = $this->db->prepare("
                INSERT INTO documents (
                    patient_id,
                    nom_fichier,
                    type_fichier,
                    description,
                    chemin_fichier,
                    taille_fichier,
                    date_creation
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $patientId,
                $_POST['name'] ?? $fileName,
                $_POST['type'] ?? $fileType,
                $_POST['description'] ?? '',
                $newFileName,
                $file['size']
            ]);

            $documentId = $this->db->lastInsertId();

            // Retourner les informations du document
            header('Content-Type: application/json');
            echo json_encode([
                'id' => $documentId,
                'nom_fichier' => $_POST['name'] ?? $fileName,
                'type_fichier' => $_POST['type'] ?? $fileType,
                'description' => $_POST['description'] ?? '',
                'taille_fichier' => $file['size'],
                'date_creation' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    public function downloadDocument($documentId) {
        try {
            // Récupérer les informations du document
            $stmt = $this->db->prepare("
                SELECT nom_fichier, chemin_fichier, type_fichier
                FROM documents
                WHERE id = ?
            ");
            $stmt->execute([$documentId]);
            $document = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$document) {
                throw new Exception('Document non trouvé');
            }

            $filePath = $this->uploadDir . $document['chemin_fichier'];
            if (!file_exists($filePath)) {
                throw new Exception('Fichier non trouvé');
            }

            // Définir les en-têtes pour le téléchargement
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $document['nom_fichier'] . '"');
            header('Content-Length: ' . filesize($filePath));

            // Envoyer le fichier
            readfile($filePath);
            exit();

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    public function deleteDocument($documentId) {
        try {
            // Récupérer le chemin du fichier
            $stmt = $this->db->prepare("
                SELECT chemin_fichier
                FROM documents
                WHERE id = ?
            ");
            $stmt->execute([$documentId]);
            $document = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$document) {
                throw new Exception('Document non trouvé');
            }

            // Supprimer le fichier physique
            $filePath = $this->uploadDir . $document['chemin_fichier'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Supprimer l'entrée dans la base de données
            $stmt = $this->db->prepare("DELETE FROM documents WHERE id = ?");
            $stmt->execute([$documentId]);

            header('Content-Type: application/json');
            echo json_encode(['message' => 'Document supprimé avec succès']);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
} 