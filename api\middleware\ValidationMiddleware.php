<?php
class ValidationMiddleware {
    public static function validate(array $data, array $rules): array {
        $errors = [];
        
        foreach ($rules as $field => $validations) {
            $value = $data[$field] ?? null;
            
            foreach (explode('|', $validations) as $validation) {
                $params = [];
                
                // Extraction des paramètres (ex: max:255)
                if (str_contains($validation, ':')) {
                    [$validation, $paramStr] = explode(':', $validation, 2);
                    $params = explode(',', $paramStr);
                }
                
                $method = 'validate' . ucfirst($validation);
                
                if (!method_exists(self::class, $method)) {
                    throw new \Exception("Validation rule '$validation' does not exist");
                }
                
                if (!self::$method($value, ...$params)) {
                    $errors[$field][] = self::getErrorMessage($validation, $field, $params);
                }
            }
        }
        
        return $errors;
    }

    // Messages d'erreur par défaut
    private static function getErrorMessage(string $rule, string $field, array $params): string {
        $messages = [
            'required' => "Le champ $field est obligatoire",
            'email' => "Le champ $field doit être une adresse email valide",
            'min' => "Le champ $field doit contenir au moins {$params[0]} caractères",
            'max' => "Le champ $field ne doit pas dépasser {$params[0]} caractères",
            'numeric' => "Le champ $field doit être un nombre",
            'date' => "Le champ $field doit être une date valide",
            'confirmed' => "Les champs $field ne correspondent pas",
            'regex' => "Le format du champ $field est invalide"
        ];
        
        return $messages[$rule] ?? "Erreur de validation pour le champ $field";
    }

    // ============ RÈGLES DE VALIDATION ============
    
    private static function validateRequired($value): bool {
        return !empty($value) || $value === '0';
    }
    
    private static function validateEmail($value): bool {
        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    private static function validateMin($value, $min): bool {
        return strlen(trim($value)) >= $min;
    }
    
    private static function validateMax($value, $max): bool {
        return strlen(trim($value)) <= $max;
    }
    
    private static function validateNumeric($value): bool {
        return is_numeric($value);
    }
    
    private static function validateDate($value): bool {
        return strtotime($value) !== false;
    }
    
    private static function validateConfirmed($value, $field, $data): bool {
        return $value === ($data["{$field}_confirmation"] ?? null);
    }
    
    private static function validateRegex($value, $pattern): bool {
        return preg_match($pattern, $value) === 1;
    }

}