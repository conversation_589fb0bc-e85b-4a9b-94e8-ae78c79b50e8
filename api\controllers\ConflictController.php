<?php
require_once __DIR__ . '/../models/Conflict.php';
require_once __DIR__ . '/../config/database.php';

class ConflictController {
    private $db;
    private $conflictModel;

    public function __construct($database) {
        $this->db = $database;
        $this->conflictModel = new Conflict($database);
    }

    /**
     * Récupérer tous les conflits actifs
     */
    public function index() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $conflicts = $this->conflictModel->getActiveConflicts($medecinId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $conflicts
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->index: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des conflits'
            ]);
        }
    }

    /**
     * Récupérer les statistiques des conflits
     */
    public function getStats() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $stats = $this->conflictModel->getConflictStats($medecinId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->getStats: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }

    /**
     * Détecter et enregistrer les conflits pour un médecin
     */
    public function detectConflicts() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            $medecinId = $data['medecin_id'] ?? $_GET['medecin_id'] ?? null;
            $date = $data['date'] ?? $_GET['date'] ?? date('Y-m-d');

            if (!$medecinId) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'ID du médecin requis'
                ]);
                return;
            }

            $conflictsCreated = $this->conflictModel->detectAndSaveConflicts($medecinId, $date);
            $activeConflicts = $this->conflictModel->getActiveConflicts($medecinId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'conflicts_created' => $conflictsCreated,
                    'active_conflicts' => $activeConflicts,
                    'total_active' => count($activeConflicts)
                ]
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->detectConflicts: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la détection des conflits'
            ]);
        }
    }

    /**
     * Résoudre un conflit
     */
    public function resolve($conflictId) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            $resolutionData = [
                'methode' => $data['methode'] ?? 'reprogrammation',
                'resolu_par' => $data['resolu_par'] ?? null,
                'notes' => $data['notes'] ?? null
            ];

            $success = $this->conflictModel->resolve($conflictId, $resolutionData);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Conflit résolu avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Conflit non trouvé ou déjà résolu'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->resolve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la résolution du conflit'
            ]);
        }
    }

    /**
     * Ignorer un conflit
     */
    public function ignore($conflictId) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            $userId = $data['user_id'] ?? null;

            $success = $this->conflictModel->ignore($conflictId, $userId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Conflit ignoré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Conflit non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->ignore: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ignorance du conflit'
            ]);
        }
    }
}
