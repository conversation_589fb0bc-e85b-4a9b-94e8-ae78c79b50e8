<?php
require_once __DIR__ . '/../models/Conflict.php';
require_once __DIR__ . '/../config/database.php';

class ConflictController {
    private $db;
    private $conflictModel;

    public function __construct($database) {
        $this->db = $database;
        $this->conflictModel = new Conflict($database);
    }

    /**
     * Récupérer tous les conflits actifs
     */
    public function index() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $conflicts = $this->conflictModel->getActiveConflicts($medecinId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $conflicts
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->index: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des conflits'
            ]);
        }
    }

    /**
     * Récupérer les statistiques des conflits
     */
    public function getStats() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $stats = $this->conflictModel->getConflictStats($medecinId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->getStats: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }

    /**
     * Détecter et enregistrer les conflits pour un médecin
     */
    public function detectConflicts() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            $medecinId = $data['medecin_id'] ?? $_GET['medecin_id'] ?? null;
            $date = $data['date'] ?? $_GET['date'] ?? date('Y-m-d');

            if (!$medecinId) {
                http_response_code(400);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'ID du médecin requis'
                ]);
                return;
            }

            $conflictsCreated = $this->conflictModel->detectAndSaveConflicts($medecinId, $date);
            $activeConflicts = $this->conflictModel->getActiveConflicts($medecinId);

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'conflicts_created' => $conflictsCreated,
                    'active_conflicts' => $activeConflicts,
                    'total_active' => count($activeConflicts)
                ]
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->detectConflicts: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la détection des conflits'
            ]);
        }
    }

    /**
     * Résoudre un conflit
     */
    public function resolve($conflictId) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            $resolutionData = [
                'methode' => $data['methode'] ?? 'reprogrammation',
                'resolu_par' => $data['resolu_par'] ?? null,
                'notes' => $data['notes'] ?? null
            ];

            $success = $this->conflictModel->resolve($conflictId, $resolutionData);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Conflit résolu avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Conflit non trouvé ou déjà résolu'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->resolve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la résolution du conflit'
            ]);
        }
    }

    /**
     * Ignorer un conflit
     */
    public function ignore($conflictId) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);
            $userId = $data['user_id'] ?? null;

            $success = $this->conflictModel->ignore($conflictId, $userId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Conflit ignoré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Conflit non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->ignore: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ignorance du conflit'
            ]);
        }
    }

    /**
     * Nettoyage automatique des conflits expirés
     */
    public function autoCleanup() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;

            $result = [
                'cleaned_conflicts' => 0,
                'message' => 'Aucun conflit expiré à nettoyer'
            ];

            // Vérifier si la table conflits existe
            $stmt = $this->db->query("SHOW TABLES LIKE 'conflits'");
            if ($stmt->fetch()) {
                // Nettoyer les conflits expirés (exemple: plus de 24h)
                $sql = "DELETE FROM conflits WHERE statut = 'actif' AND cree_le < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
                $params = [];

                if ($medecinId) {
                    $sql .= " AND medecin_id = ?";
                    $params[] = $medecinId;
                }

                $stmt = $this->db->prepare($sql);
                $stmt->execute($params);
                $result['cleaned_conflicts'] = $stmt->rowCount();

                if ($result['cleaned_conflicts'] > 0) {
                    $result['message'] = "{$result['cleaned_conflicts']} conflit(s) expiré(s) nettoyé(s)";
                }
            }

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $result
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->autoCleanup: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors du nettoyage automatique: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Analyser les tendances des conflits
     */
    public function analyzeTrends() {
        try {
            $medecinId = $_GET['medecin_id'] ?? null;
            $days = $_GET['days'] ?? 30;

            // Simulation des tendances pour l'instant
            $trends = [
                'peak_conflict_hours' => ['09:00', '14:00', '16:00'],
                'most_common_types' => ['chevauchement', 'duree_insuffisante'],
                'conflict_frequency' => [
                    'monday' => 5,
                    'tuesday' => 3,
                    'wednesday' => 8,
                    'thursday' => 4,
                    'friday' => 2
                ],
                'recommendations' => [
                    'Éviter les créneaux de 9h et 14h pour les consultations longues',
                    'Prévoir des tampons de 15 minutes entre les rendez-vous'
                ]
            ];

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $trends
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->analyzeTrends: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'analyse des tendances: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Programmer des notifications
     */
    public function scheduleNotifications() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            $result = [
                'scheduled' => true,
                'interval_minutes' => $input['interval_minutes'] ?? 30,
                'message' => 'Notifications programmées avec succès'
            ];

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $result
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->scheduleNotifications: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la programmation des notifications: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Envoyer des notifications
     */
    public function notifyConflicts() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $conflicts = $input['conflicts'] ?? [];

            $result = [
                'notifications_sent' => count($conflicts),
                'message' => count($conflicts) . ' notification(s) envoyée(s)'
            ];

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $result
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->notifyConflicts: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'envoi des notifications: ' . $e->getMessage()
            ]);
        }
    }



    /**
     * Valider un créneau
     */
    public function validateTimeSlot() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Simulation de validation
            $result = [
                'valid' => true,
                'conflicts' => [],
                'warnings' => [],
                'message' => 'Créneau valide'
            ];

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $result
            ]);

        } catch (Exception $e) {
            error_log("Erreur dans ConflictController->validateTimeSlot: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la validation du créneau: ' . $e->getMessage()
            ]);
        }
    }
}
