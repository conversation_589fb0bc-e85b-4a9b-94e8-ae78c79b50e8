<template>
  <div class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>
          <i class="fas fa-clock"></i>
          {{ isEditing ? 'Modifier le créneau' : 'Nouveau créneau disponible' }}
        </h3>
        <button @click="closeModal" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-body">
        <!-- Note: Les patients choisiront eux-mêmes ces créneaux -->
        <div class="info-banner">
          <i class="fas fa-info-circle"></i>
          <span>Les patients pourront choisir parmi ces créneaux disponibles</span>
        </div>

        <!-- Date et heure -->
        <div class="form-row">
          <div class="form-group">
            <label for="date">Date *</label>
            <input 
              id="date"
              v-model="form.date" 
              type="date" 
              required
              :min="minDate"
              class="form-control"
            >
          </div>
          
          <div class="form-group">
            <label for="time">Heure *</label>
            <input 
              id="time"
              v-model="form.time" 
              type="time" 
              required
              class="form-control"
            >
          </div>
        </div>

        <!-- Durée -->
        <div class="form-group">
          <label for="duration">Durée (minutes) *</label>
          <select id="duration" v-model="form.duree" required class="form-control">
            <option value="15">15 minutes</option>
            <option value="30">30 minutes</option>
            <option value="45">45 minutes</option>
            <option value="60">1 heure</option>
            <option value="90">1h30</option>
            <option value="120">2 heures</option>
          </select>
        </div>

        <!-- Type de créneau -->
        <div class="form-group">
          <label for="slot-type">Type de créneau</label>
          <select id="slot-type" v-model="form.type_creneau_id" class="form-control">
            <option value="">-- Sélectionner un type --</option>
            <option
              v-for="type in slotTypes"
              :key="type.id"
              :value="type.id"
            >
              {{ type.nom }} ({{ type.duree }} min - {{ formatPriceXOF(type.prix_base) }})
            </option>
          </select>
        </div>

        <!-- Raison -->
        <div class="form-group">
          <label for="reason">Raison / Notes</label>
          <textarea 
            id="reason"
            v-model="form.raison" 
            rows="3"
            placeholder="Motif de la proposition, notes particulières..."
            class="form-control"
          ></textarea>
        </div>

        <!-- Expiration -->
        <div class="form-group">
          <label for="expiry">Expiration</label>
          <select id="expiry" v-model="form.expire_days" class="form-control">
            <option value="1">1 jour</option>
            <option value="3">3 jours</option>
            <option value="7">7 jours</option>
            <option value="14">14 jours</option>
            <option value="30">30 jours</option>
          </select>
        </div>

        <!-- Génération automatique -->
        <div v-if="!isEditing" class="auto-generation">
          <div class="form-group">
            <label class="checkbox-label">
              <input v-model="autoGenerate" type="checkbox">
              Générer plusieurs créneaux automatiquement
            </label>
          </div>

          <div v-if="autoGenerate" class="auto-options">
            <div class="form-row">
              <div class="form-group">
                <label for="days-ahead">Jours à l'avance</label>
                <select id="days-ahead" v-model="autoOptions.daysAhead" class="form-control">
                  <option value="3">3 jours</option>
                  <option value="7">7 jours</option>
                  <option value="14">14 jours</option>
                  <option value="30">30 jours</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="checkbox-label">
                  <input v-model="autoOptions.excludeWeekends" type="checkbox">
                  Exclure les weekends
                </label>
              </div>
            </div>

            <div class="form-group">
              <label>Heures préférées</label>
              <div class="hours-grid">
                <label 
                  v-for="hour in availableHours" 
                  :key="hour"
                  class="hour-checkbox"
                >
                  <input 
                    v-model="autoOptions.preferredHours" 
                    :value="hour" 
                    type="checkbox"
                  >
                  {{ hour }}h
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Vérification des conflits -->
        <div v-if="conflicts.length > 0" class="conflicts-warning">
          <div class="warning-header">
            <i class="fas fa-exclamation-triangle"></i>
            Conflits détectés
          </div>
          <div class="conflicts-list">
            <div v-for="conflict in conflicts" :key="conflict.id" class="conflict-item">
              {{ conflict.patient_nom }} - {{ formatDateTime(conflict.date_rendez_vous) }}
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="modal-actions">
          <button type="button" @click="closeModal" class="btn-secondary">
            Annuler
          </button>
          <button 
            type="submit" 
            :disabled="loading || !isFormValid"
            class="btn-primary"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-save"></i>
            {{ isEditing ? 'Modifier' : (autoGenerate ? 'Créer les créneaux' : 'Créer le créneau') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import { usePatientStore } from '@/stores/patientStore'
import { useAuthStore } from '@/stores/authStore'
import suggestedSlotService from '@/services/suggestedSlotService'
import { slotTypeService } from '@/services/slotTypeService'

const props = defineProps({
  slotData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'created'])

const suggestedSlotStore = useSuggestedSlotStore()
const patientStore = usePatientStore()
const authStore = useAuthStore()

// État local
const loading = ref(false)
const conflicts = ref([])
const autoGenerate = ref(false)
const slotTypes = ref([])

const form = ref({
  id_patient: '',
  date: '',
  time: '',
  duree: 30,
  type_creneau_id: '',
  raison: '',
  expire_days: 7
})

const autoOptions = ref({
  daysAhead: 7,
  excludeWeekends: true,
  preferredHours: [9, 10, 11, 14, 15, 16, 17]
})

const availableHours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]

// Computed properties
const isEditing = computed(() => !!props.slotData)
const patients = computed(() => patientStore.patients)

const minDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

const isFormValid = computed(() => {
  return form.value.date &&
         form.value.time &&
         form.value.duree
})

// Méthodes
const closeModal = () => {
  emit('close')
}

const loadPatients = async () => {
  try {
    await patientStore.fetchAll()
  } catch (error) {
    console.error('Erreur lors du chargement des patients:', error)
  }
}

const loadSlotTypes = async () => {
  try {
    const response = await slotTypeService.getActive()
    slotTypes.value = response.data
  } catch (error) {
    console.error('Erreur lors du chargement des types de créneaux:', error)
  }
}

const formatPriceXOF = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price) + ' XOF'
}

const checkConflicts = async () => {
  if (!form.value.date || !form.value.time || !form.value.duree) {
    conflicts.value = []
    return
  }

  try {
    const dateTime = `${form.value.date} ${form.value.time}:00`
    const doctorId = authStore.user?.id || 1
    
    // Simuler la vérification des conflits
    // En production, cela ferait un appel API
    conflicts.value = []
  } catch (error) {
    console.error('Erreur lors de la vérification des conflits:', error)
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  try {
    loading.value = true
    const doctorId = authStore.user?.id || 1
    
    if (autoGenerate.value && !isEditing.value) {
      // Génération automatique de plusieurs créneaux
      const slots = suggestedSlotService.generateSuggestedSlots(
        doctorId,
        form.value.id_patient,
        {
          daysAhead: autoOptions.value.daysAhead,
          preferredHours: autoOptions.value.preferredHours,
          duration: form.value.duree,
          excludeWeekends: autoOptions.value.excludeWeekends
        }
      )

      // Ajouter les données communes
      const slotsWithData = slots.map(slot => ({
        ...slot,
        raison: form.value.raison,
        expire_days: form.value.expire_days
      }))

      const result = await suggestedSlotStore.createMultipleSlots(slotsWithData)
      
      if (result.success) {
        emit('created')
      } else {
        console.error('Erreur lors de la création multiple:', result.error)
      }
    } else {
      // Création ou modification d'un seul créneau
      const slotData = {
        id_medecin: doctorId,
        id_patient: null, // Pas de patient spécifique - créneau disponible
        date_heure_suggeree: `${form.value.date} ${form.value.time}:00`,
        duree: form.value.duree,
        type_creneau_id: form.value.type_creneau_id || null,
        raison: form.value.raison,
        expire_days: form.value.expire_days
      }

      const result = await suggestedSlotStore.createSlot(slotData)
      
      if (result.success) {
        emit('created')
      } else {
        console.error('Erreur lors de la création:', result.error)
      }
    }
  } catch (error) {
    console.error('Erreur lors de la soumission:', error)
  } finally {
    loading.value = false
  }
}

const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('fr-FR')
}

// Watchers
watch([() => form.value.date, () => form.value.time, () => form.value.duree], () => {
  checkConflicts()
}, { deep: true })

// Synchroniser la durée avec le type sélectionné
watch(() => form.value.type_creneau_id, (newTypeId) => {
  if (newTypeId && slotTypes.value.length > 0) {
    const selectedType = slotTypes.value.find(type => type.id == newTypeId)
    if (selectedType) {
      form.value.duree = selectedType.duree
    }
  }
})

// Lifecycle
onMounted(async () => {
  await loadPatients()
  await loadSlotTypes()

  // Pré-remplir le formulaire si on édite
  if (isEditing.value) {
    const slot = props.slotData
    const dateTime = new Date(slot.date_heure_suggeree)

    form.value = {
      id_patient: slot.id_patient,
      date: dateTime.toISOString().split('T')[0],
      time: dateTime.toTimeString().slice(0, 5),
      duree: slot.duree,
      type_creneau_id: slot.type_creneau_id || '',
      raison: slot.raison || '',
      expire_days: 7 // Valeur par défaut pour l'édition
    }
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-header h3 i {
  color: #3b82f6;
  margin-right: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.info-banner {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 8px;
  color: #1e40af;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.info-banner i {
  font-size: 1rem;
  color: #3b82f6;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: normal;
}

.auto-generation {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.auto-options {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.hour-checkbox {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.conflicts-warning {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #dc2626;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.conflicts-list {
  font-size: 0.875rem;
}

.conflict-item {
  color: #7f1d1d;
  margin-bottom: 0.25rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.btn-primary, .btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .hours-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}
</style>
