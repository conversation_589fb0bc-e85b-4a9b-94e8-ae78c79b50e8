import api from './api'

const conflictTypeService = {
  /**
   * <PERSON><PERSON><PERSON><PERSON>rer tous les types de conflits
   */
  async getAll() {
    try {
      console.log('🔍 Récupération des types de conflits...')
      const response = await api.get('/conflict-types')
      console.log('✅ Types de conflits récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des types de conflits:', error)
      // Fallback avec les types hardcodés si l'API n'est pas disponible
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: utilisation des types hardcodés')
        return this.getFallbackTypes()
      }
      throw error
    }
  },

  /**
   * Créer un nouveau type de conflit
   */
  async create(typeData) {
    try {
      console.log('➕ Création d\'un nouveau type de conflit...', typeData)
      const response = await api.post('/conflict-types', typeData)
      console.log('✅ Type de conflit créé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la création du type de conflit:', error)
      throw error
    }
  },

  /**
   * Mettre à jour un type de conflit
   */
  async update(id, typeData) {
    try {
      console.log('📝 Mise à jour du type de conflit...', id, typeData)
      const response = await api.put(`/conflict-types/${id}`, typeData)
      console.log('✅ Type de conflit mis à jour:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour du type de conflit:', error)
      throw error
    }
  },

  /**
   * Supprimer un type de conflit
   */
  async delete(id) {
    try {
      console.log('🗑️ Suppression du type de conflit...', id)
      const response = await api.delete(`/conflict-types/${id}`)
      console.log('✅ Type de conflit supprimé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la suppression du type de conflit:', error)
      throw error
    }
  },

  /**
   * Récupérer les statistiques d'utilisation des types
   */
  async getStats() {
    try {
      console.log('📊 Récupération des statistiques des types de conflits...')
      const response = await api.get('/conflict-types/stats')
      console.log('✅ Statistiques récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      // Fallback avec des stats vides
      if (error.response?.status === 404) {
        console.log('📝 Mode fallback: statistiques vides')
        return {
          status: 'success',
          data: []
        }
      }
      throw error
    }
  },

  /**
   * Types de conflits en fallback (hardcodés)
   */
  getFallbackTypes() {
    return {
      status: 'success',
      data: {
        'chevauchement': {
          id: 1,
          label: 'Chevauchement de créneaux',
          description: 'Deux rendez-vous se chevauchent dans le temps',
          severity_default: 'elevee',
          icon: 'fas fa-exclamation-triangle',
          color: '#fd7e14',
          active: true,
          order: 1
        },
        'duree_insuffisante': {
          id: 2,
          label: 'Durée insuffisante',
          description: 'Le temps alloué est insuffisant pour le type de consultation',
          severity_default: 'moyenne',
          icon: 'fas fa-clock',
          color: '#ffc107',
          active: true,
          order: 2
        },
        'hors_horaires': {
          id: 3,
          label: 'Hors horaires de travail',
          description: 'Rendez-vous programmé en dehors des horaires du médecin',
          severity_default: 'elevee',
          icon: 'fas fa-calendar-times',
          color: '#fd7e14',
          active: true,
          order: 3
        },
        'double_reservation': {
          id: 4,
          label: 'Double réservation',
          description: 'Même patient avec plusieurs rendez-vous simultanés',
          severity_default: 'critique',
          icon: 'fas fa-copy',
          color: '#dc3545',
          active: true,
          order: 4
        },
        'patient_absent': {
          id: 5,
          label: 'Patient absent',
          description: 'Patient marqué comme absent mais créneau toujours réservé',
          severity_default: 'faible',
          icon: 'fas fa-user-times',
          color: '#28a745',
          active: true,
          order: 5
        },
        'medecin_indisponible': {
          id: 6,
          label: 'Médecin indisponible',
          description: 'Médecin en congé ou indisponible pendant le créneau',
          severity_default: 'critique',
          icon: 'fas fa-user-md-times',
          color: '#dc3545',
          active: true,
          order: 6
        },
        'salle_occupee': {
          id: 7,
          label: 'Salle occupée',
          description: 'Salle de consultation déjà occupée',
          severity_default: 'elevee',
          icon: 'fas fa-door-closed',
          color: '#fd7e14',
          active: true,
          order: 7
        },
        'equipement_indisponible': {
          id: 8,
          label: 'Équipement indisponible',
          description: 'Équipement médical nécessaire non disponible',
          severity_default: 'moyenne',
          icon: 'fas fa-tools',
          color: '#ffc107',
          active: true,
          order: 8
        },
        'urgence_medicale': {
          id: 9,
          label: 'Urgence médicale',
          description: 'Urgence médicale perturbant le planning',
          severity_default: 'critique',
          icon: 'fas fa-ambulance',
          color: '#dc3545',
          active: true,
          order: 9
        },
        'retard_precedent': {
          id: 10,
          label: 'Retard du rendez-vous précédent',
          description: 'Retard causé par le rendez-vous précédent',
          severity_default: 'moyenne',
          icon: 'fas fa-hourglass-half',
          color: '#ffc107',
          active: true,
          order: 10
        }
      }
    }
  },

  /**
   * Valider les données d'un type de conflit
   */
  validateTypeData(typeData) {
    const errors = []

    // Vérifier le code/key
    const code = typeData.code || typeData.key
    if (!code || code.trim() === '') {
      errors.push('Le code est requis')
    }

    // Vérifier le nom/label
    const nom = typeData.nom || typeData.label
    if (!nom || nom.trim() === '') {
      errors.push('Le nom est requis')
    }

    // Vérifier la description
    if (!typeData.description || typeData.description.trim() === '') {
      errors.push('La description est requise')
    }

    // Vérifier la gravité
    const gravite = typeData.gravite_defaut || typeData.severity_default
    if (!['faible', 'moyenne', 'elevee', 'critique'].includes(gravite)) {
      errors.push('La gravité par défaut doit être: faible, moyenne, elevee ou critique')
    }

    // Vérifier la couleur
    const couleur = typeData.couleur || typeData.color
    if (couleur && !/^#[0-9A-F]{6}$/i.test(couleur)) {
      errors.push('La couleur doit être au format hexadécimal (#RRGGBB)')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * Formater les données pour l'API
   */
  formatForApi(formData) {
    return {
      code: formData.key || formData.code,
      nom: formData.label || formData.nom,
      description: formData.description,
      gravite_defaut: formData.severity_default || formData.gravite_defaut,
      icone: formData.icon || formData.icone,
      couleur: formData.color || formData.couleur,
      ordre_affichage: formData.order || formData.ordre_affichage || 0
    }
  }
}

export default conflictTypeService
