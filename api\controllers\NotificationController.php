<?php
require_once __DIR__ . '/../models/Notification.php';
require_once __DIR__ . '/../services/EmailService.php';

class NotificationController {
    private $db;
    private $notificationModel;
    private $emailService;
    
    public function __construct($database) {
        $this->db = $database;
        $this->notificationModel = new Notification($database);
        $this->emailService = new EmailService();
    }
    
    /**
     * Traiter les notifications en attente
     */
    public function processNotifications() {
        try {
            $pendingNotifications = $this->notificationModel->getPendingNotifications();
            
            $processed = 0;
            $errors = 0;
            
            foreach ($pendingNotifications as $notification) {
                try {
                    $success = $this->sendNotification($notification);
                    
                    if ($success) {
                        $this->notificationModel->markAsSent($notification['id']);
                        $processed++;
                        error_log("Notification envoyée: {$notification['id']} - {$notification['sujet']}");
                    } else {
                        $errors++;
                        error_log("Échec envoi notification: {$notification['id']}");
                    }
                } catch (Exception $e) {
                    $errors++;
                    error_log("Erreur notification {$notification['id']}: " . $e->getMessage());
                }
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Traitement des notifications terminé',
                'processed' => $processed,
                'errors' => $errors,
                'total' => count($pendingNotifications)
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans NotificationController->processNotifications: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors du traitement des notifications'
            ]);
        }
    }
    
    /**
     * Créer les rappels automatiques
     */
    public function createReminders() {
        try {
            $appointmentsNeedingReminders = $this->notificationModel->getAppointmentsNeedingReminders();
            
            $created = 0;
            
            foreach ($appointmentsNeedingReminders as $appointment) {
                try {
                    $reminderIds = $this->notificationModel->createReminderNotifications($appointment['id']);
                    $created += count($reminderIds);
                    error_log("Rappels créés pour RDV {$appointment['id']}: " . count($reminderIds));
                } catch (Exception $e) {
                    error_log("Erreur création rappels RDV {$appointment['id']}: " . $e->getMessage());
                }
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Rappels créés',
                'created' => $created,
                'appointments_processed' => count($appointmentsNeedingReminders)
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans NotificationController->createReminders: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création des rappels'
            ]);
        }
    }
    
    /**
     * Envoyer une notification spécifique
     */
    private function sendNotification($notification) {
        try {
            // Préparer les données pour l'email
            $appointmentData = [
                'patient_nom' => $notification['destinataire_type'] === 'patient' ? $notification['destinataire_nom'] : $this->getPatientName($notification['rdv_id']),
                'patient_email' => $notification['destinataire_type'] === 'patient' ? $notification['destinataire_email'] : $this->getPatientEmail($notification['rdv_id']),
                'patient_telephone' => $notification['destinataire_type'] === 'patient' ? $notification['destinataire_telephone'] : $this->getPatientPhone($notification['rdv_id']),
                'medecin_nom' => $notification['destinataire_type'] === 'medecin' ? $notification['destinataire_nom'] : $this->getDoctorName($notification['rdv_id']),
                'medecin_email' => $notification['destinataire_type'] === 'medecin' ? $notification['destinataire_email'] : $this->getDoctorEmail($notification['rdv_id']),
                'date_rendez_vous' => $notification['date_rendez_vous'],
                'duree' => $notification['duree'],
                'type' => $notification['rdv_type'],
                'notes' => $notification['notes'],
                'specialite' => $this->getDoctorSpecialty($notification['rdv_id'])
            ];
            
            switch ($notification['type']) {
                case 'confirmation':
                    if ($notification['destinataire_type'] === 'patient') {
                        return $this->emailService->sendAppointmentConfirmationToPatient($appointmentData);
                    } else {
                        return $this->emailService->sendAppointmentNotificationToDoctor($appointmentData);
                    }
                    break;
                    
                case 'notification':
                    return $this->emailService->sendAppointmentNotificationToDoctor($appointmentData);
                    break;
                    
                case 'rappel':
                    if ($notification['destinataire_type'] === 'patient') {
                        return $this->emailService->sendAppointmentReminderToPatient($appointmentData);
                    } else {
                        return $this->emailService->sendAppointmentReminderToDoctor($appointmentData);
                    }
                    break;
                    
                case 'annulation':
                    return $this->emailService->sendAppointmentCancellation($appointmentData, $notification['destinataire_type'] === 'medecin');
                    break;
                    
                default:
                    error_log("Type de notification non supporté: {$notification['type']}");
                    return false;
            }
        } catch (Exception $e) {
            error_log("Erreur lors de l'envoi de la notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Créer les notifications pour un nouveau RDV
     */
    public function createAppointmentNotifications($rdvId) {
        try {
            $confirmationIds = $this->notificationModel->createConfirmationNotifications($rdvId);
            $reminderIds = $this->notificationModel->createReminderNotifications($rdvId);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Notifications créées pour le RDV',
                'confirmation_notifications' => $confirmationIds,
                'reminder_notifications' => $reminderIds
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans NotificationController->createAppointmentNotifications: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la création des notifications'
            ]);
        }
    }
    
    /**
     * Nettoyer les anciennes notifications
     */
    public function cleanOldNotifications() {
        try {
            $deleted = $this->notificationModel->cleanOldNotifications(30);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Nettoyage terminé',
                'deleted_count' => $deleted
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans NotificationController->cleanOldNotifications: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors du nettoyage'
            ]);
        }
    }
    
    /**
     * Obtenir les statistiques des notifications
     */
    public function getStats() {
        try {
            $query = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN statut = 'en_attente' THEN 1 ELSE 0 END) as en_attente,
                    SUM(CASE WHEN statut = 'envoye' THEN 1 ELSE 0 END) as envoyes,
                    SUM(CASE WHEN statut = 'erreur' THEN 1 ELSE 0 END) as erreurs,
                    SUM(CASE WHEN type = 'confirmation' THEN 1 ELSE 0 END) as confirmations,
                    SUM(CASE WHEN type = 'rappel' THEN 1 ELSE 0 END) as rappels,
                    SUM(CASE WHEN type = 'annulation' THEN 1 ELSE 0 END) as annulations
                FROM notifications
                WHERE cree_le >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'data' => $stats
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans NotificationController->getStats: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques'
            ]);
        }
    }
    
    // Méthodes utilitaires pour récupérer les données manquantes
    private function getPatientName($rdvId) {
        $query = "SELECT CONCAT(p.nom, ' ', p.prenom) as nom FROM rendez_vous rdv INNER JOIN patients p ON rdv.id_patient = p.id WHERE rdv.id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$rdvId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['nom'] : '';
    }
    
    private function getPatientEmail($rdvId) {
        $query = "SELECT p.email FROM rendez_vous rdv INNER JOIN patients p ON rdv.id_patient = p.id WHERE rdv.id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$rdvId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['email'] : '';
    }
    
    private function getPatientPhone($rdvId) {
        $query = "SELECT p.telephone FROM rendez_vous rdv INNER JOIN patients p ON rdv.id_patient = p.id WHERE rdv.id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$rdvId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['telephone'] : '';
    }
    
    private function getDoctorName($rdvId) {
        $query = "SELECT CONCAT('Dr. ', u.nom, ' ', u.prenom) as nom FROM rendez_vous rdv INNER JOIN medecins m ON rdv.id_medecin = m.id INNER JOIN utilisateur u ON m.user_id = u.id WHERE rdv.id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$rdvId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['nom'] : '';
    }
    
    private function getDoctorEmail($rdvId) {
        $query = "SELECT u.email FROM rendez_vous rdv INNER JOIN medecins m ON rdv.id_medecin = m.id INNER JOIN utilisateur u ON m.user_id = u.id WHERE rdv.id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$rdvId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['email'] : '';
    }
    
    private function getDoctorSpecialty($rdvId) {
        $query = "SELECT m.specialite FROM rendez_vous rdv INNER JOIN medecins m ON rdv.id_medecin = m.id WHERE rdv.id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$rdvId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['specialite'] : '';
    }
}
?>
