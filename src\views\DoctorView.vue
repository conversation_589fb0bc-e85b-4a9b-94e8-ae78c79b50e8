<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import DoctorDashboard from '@/components/doctor/DoctorDashboard.vue'
import CalendarView from '@/components/doctor/CalendarView.vue'
import ConflictAlerts from '@/components/doctor/ConflictAlerts.vue'
import { CalendarSyncService } from '@/services/calendarsync'
import { dateHelpers } from '@/utils/dateHelpers'
import AppointmentManager from '@/components/doctor/AppointmentManager.vue'
import LogoutButton from '@/components/shared/LogoutButton.vue'
import { useAuthStore } from '@/stores/authStore'
import { storeToRefs } from 'pinia'
import { usePatientStore } from '@/stores/patientStore'
import api from '@/services/api'
import AddPatientForm from '@/components/admin/patients/PatientFormModal.vue'
import PatientDetails from '@/components/admin/patients/PatientDetails.vue'
import AppointmentEditor from '@/components/admin/appointments/AppointmentEditor.vue'
import SuggestedSlots from '@/components/doctor/SuggestedSlots.vue'
import SlotTypeManager from '@/components/doctor/SlotTypeManager.vue'
import ConflictTypeManager from '@/components/doctor/ConflictTypeManager.vue'

const store = useAppointmentStore()
const authStore = useAuthStore()
const patientStore = usePatientStore()
const { fullName } = storeToRefs(authStore)
const activeTab = ref('dashboard')
const selectedDate = ref(null)
const searchQuery = ref('')
const selectedPeriod = ref('mois')
const showAddPatient = ref(false)
const patients = ref([])
const filteredPatients = computed(() => {
  if (!searchQuery.value) return patients.value
  const query = searchQuery.value.toLowerCase()
  return patients.value.filter(patient => 
    patient.nom.toLowerCase().includes(query) ||
    patient.prenom.toLowerCase().includes(query) ||
    `${patient.nom} ${patient.prenom}`.toLowerCase().includes(query)
  )
})

const statistics = ref({
  totalPatients: 0,
  monthlyAppointments: 0,
  completionRate: 0,
  averageConsultationTime: 0
})

const showPatientDetails = ref(false)
const selectedPatient = ref(null)
const showAppointmentEditor = ref(false)
const appointmentPatient = ref(null)

// Fonctions pour les onglets
const getTabIcon = (tab) => {
  const icons = {
    dashboard: 'fas fa-chart-line',
    calendar: 'fas fa-calendar-alt',
    patients: 'fas fa-users',
    creneaux: 'fas fa-clock',
    'types-creneaux': 'fas fa-cogs',
    'types-conflits': 'fas fa-exclamation-triangle',
    analytics: 'fas fa-chart-bar'
  }
  return icons[tab] || 'fas fa-circle'
}

const getTabLabel = (tab) => {
  const labels = {
    dashboard: 'Tableau de bord',
    calendar: 'Calendrier',
    patients: 'Patients',
    creneaux: 'Créneaux',
    'types-creneaux': 'Types de Créneaux',
    'types-conflits': 'Types de Conflits',
    analytics: 'Analyses'
  }
  return labels[tab] || tab
}

// Statistiques
const fetchStatistics = async () => {
  try {
    // Utiliser l'instance API configurée avec l'authentification
    const response = await api.get(`/doctor/statistics/${authStore.user.id}`)
    if (response.data.status === 'success') {
      statistics.value = response.data.data
    } else {
      console.error('Erreur API:', response.data.message)
    }
  } catch (error) {
    console.error('Erreur lors du chargement des statistiques:', error)
    // Valeurs par défaut en cas d'erreur
    statistics.value = {
      totalPatients: 0,
      monthlyAppointments: 0,
      completionRate: 0,
      averageConsultationTime: 0
    }
  }
}

// Gestion des patients
const fetchPatients = async () => {
  try {
    const response = await api.get('/patients')
    if (response.data.status === 'success') {
      patients.value = response.data.data || response.data
    } else {
      console.error('Erreur API patients:', response.data.message)
      patients.value = []
    }
  } catch (error) {
    console.error('Erreur lors du chargement des patients:', error)
    patients.value = []
  }
}

const handleAddPatient = async (patientData) => {
  try {
    await patientStore.create(patientData)
    showAddPatient.value = false
    await fetchPatients()
  } catch (error) {
    console.error('Erreur lors de l\'ajout du patient:', error)
  }
}

const viewPatientFile = (patient) => {
  selectedPatient.value = patient
  showPatientDetails.value = true
}

const editPatient = (patient) => {
  selectedPatient.value = patient
  showPatientDetails.value = true
}

const scheduleAppointment = (patient) => {
  appointmentPatient.value = patient
  showAppointmentEditor.value = true
  activeTab.value = 'calendar'
}

const handleAppointmentSubmit = async (appointmentData) => {
  try {
    await store.createAppointment(appointmentData)
    showAppointmentEditor.value = false
    // Rafraîchir le calendrier
    await store.fetchAppointments()
  } catch (error) {
    console.error('Erreur lors de la création du rendez-vous:', error)
  }
}

onMounted(() => {
  fetchStatistics()
  fetchPatients()
})

function handleDateSelection(date) {
  selectedDate.value = date
}

// Méthodes pour les actions rapides du dashboard
const viewTodayAppointments = () => {
  console.log('Affichage des rendez-vous d\'aujourd\'hui')
  activeTab.value = 'calendar'
  selectedDate.value = new Date()

  // Feedback visuel
  showNotification('Navigation vers les rendez-vous d\'aujourd\'hui', 'info')
}

const viewNextAppointment = () => {
  console.log('Affichage du prochain rendez-vous')
  activeTab.value = 'calendar'

  // Feedback visuel
  showNotification('Navigation vers le prochain rendez-vous', 'info')
}

// Fonction pour afficher des notifications
const showNotification = (message, type = 'info') => {
  // Créer une notification temporaire
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === 'info' ? 'info-circle' : 'check-circle'}"></i>
    ${message}
  `

  // Styles inline pour la notification
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'info' ? '#3498db' : '#27ae60',
    color: 'white',
    padding: '1rem 1.5rem',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    zIndex: '9999',
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    fontSize: '0.9rem',
    fontWeight: '500',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease'
  })

  document.body.appendChild(notification)

  // Animation d'entrée
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // Suppression automatique
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 300)
  }, 3000)
}

// Méthodes pour les actions du dashboard
const handleViewPatients = () => {
  console.log('Navigation vers les patients')
  activeTab.value = 'patients'
  showNotification('Affichage de la liste des patients', 'info')
}

const handleNewAppointment = () => {
  console.log('Création d\'un nouveau rendez-vous')
  showAppointmentEditor.value = true
  showNotification('Ouverture du formulaire de rendez-vous', 'info')
}

const handleViewCalendar = () => {
  console.log('Navigation vers le calendrier')
  activeTab.value = 'calendar'
  showNotification('Affichage du calendrier', 'info')
}

const handleViewAnalytics = () => {
  console.log('Navigation vers les analyses')
  activeTab.value = 'analytics'
  showNotification('Affichage des analyses et rapports', 'info')
}
</script>

<template>
  <div class="doctor-view-container">
    <!-- En-tête -->
    <header class="doctor-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="title-gradient">
            <i class="fas fa-user-md header-icon"></i>
            Tableau de bord Médecin
          </h1>
          <p class="subtitle">Gestion des rendez-vous et planning</p>
        </div>
        <div class="user-info">
          <span class="user-name">
            <i class="fas fa-user"></i>
            Dr. {{ fullName }}
          </span>
          <LogoutButton class="logout-btn" />
        </div>
      </div>
    </header>

    <!-- Navigation des onglets -->
    <nav class="tab-navigation">
      <button
        v-for="tab in ['dashboard', 'calendar', 'patients', 'creneaux', 'types-creneaux', 'types-conflits', 'analytics']"
        :key="tab"
        :class="['tab-button', { active: activeTab === tab }]"
        @click="activeTab = tab"
      >
        <i :class="getTabIcon(tab)"></i>
        {{ getTabLabel(tab) }}
      </button>
    </nav>

    <!-- Contenu principal -->
    <main class="doctor-content">
      <!-- Dashboard -->
      <section v-show="activeTab === 'dashboard'" class="dashboard-section">
        <DoctorDashboard
          class="dashboard-card"
          @view-today-appointments="viewTodayAppointments"
          @view-next-appointment="viewNextAppointment"
          @view-patients="handleViewPatients"
          @new-appointment="handleNewAppointment"
          @view-calendar="handleViewCalendar"
          @view-analytics="handleViewAnalytics"
        />
        <div class="statistics-grid">
          <div class="stat-card">
            <h3>Patients Totaux</h3>
            <div class="stat-value">{{ statistics.totalPatients }}</div>
            <div class="stat-trend">
              <i class="fas fa-arrow-up"></i>
              +5% ce mois
            </div>
          </div>
          <div class="stat-card">
            <h3>RDV Mensuels</h3>
            <div class="stat-value">{{ statistics.monthlyAppointments }}</div>
            <div class="stat-trend">
              <i class="fas fa-chart-line"></i>
              Moyenne: 45
            </div>
          </div>
          <div class="stat-card">
            <h3>Taux de Présence</h3>
            <div class="stat-value">{{ statistics.completionRate }}%</div>
            <div class="stat-trend positive">
              <i class="fas fa-check-circle"></i>
              Excellent
            </div>
          </div>
          <div class="stat-card">
            <h3>Durée Moyenne</h3>
            <div class="stat-value">{{ statistics.averageConsultationTime }} min</div>
          </div>
        </div>
        <AppointmentManager class="dashboard-card" />
      </section>

      <!-- Calendrier -->
      <section v-show="activeTab === 'calendar'" class="calendar-section">
        <CalendarView 
          @date-selected="handleDateSelection"
          :selected-date="selectedDate"
        />
        <ConflictAlerts />
      </section>

      <!-- Créneaux -->
      <section v-show="activeTab === 'creneaux'" class="creneaux-section">
        <SuggestedSlots />
      </section>

      <!-- Types de Créneaux -->
      <section v-show="activeTab === 'types-creneaux'" class="types-creneaux-section">
        <SlotTypeManager />
      </section>

      <!-- Types de Conflits -->
      <section v-show="activeTab === 'types-conflits'" class="types-conflits-section">
        <ConflictTypeManager />
      </section>

      <!-- Gestion des Patients -->
      <section v-show="activeTab === 'patients'" class="patients-section">
        <div class="patients-tools">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="Rechercher un patient..." v-model="searchQuery">
          </div>
          <button class="add-patient-btn" @click="showAddPatient = true">
            <i class="fas fa-user-plus"></i>
            Nouveau Patient
          </button>
        </div>
        <div class="patient-list">
          <div v-for="patient in filteredPatients" :key="patient.id" class="patient-item">
            <div class="patient-avatar">
              <i class="fas fa-user-circle"></i>
            </div>
            <div class="patient-info">
              <div class="patient-name">{{ patient.nom }} {{ patient.prenom }}</div>
              <div class="patient-details">
                <span v-if="patient.telephone">
                  <i class="fas fa-phone"></i>
                  {{ patient.telephone }}
                </span>
                <span v-if="patient.email">
                  <i class="fas fa-envelope"></i>
                  {{ patient.email }}
                </span>
              </div>
            </div>
            <div class="patient-actions">
              <button class="action-btn" title="Voir le dossier" @click="viewPatientFile(patient)">
                <i class="fas fa-folder-open"></i>
              </button>
              <button class="action-btn" title="Prendre un rendez-vous" @click="scheduleAppointment(patient)">
                <i class="fas fa-calendar-plus"></i>
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Analyses et Rapports -->
      <section v-show="activeTab === 'analytics'" class="analytics-section">
        <div class="analytics-header">
          <h2>Analyses et Rapports</h2>
          <div class="period-selector">
            <button 
              v-for="period in ['semaine', 'mois', 'trimestre']"
              :key="period"
              :class="['period-btn', { active: selectedPeriod === period }]"
              @click="selectedPeriod = period"
            >
              {{ period }}
            </button>
          </div>
        </div>
        <div class="analytics-grid">
          <!-- Graphiques et statistiques à implémenter -->
        </div>
      </section>
    </main>

    <!-- Modal d'ajout de patient -->
    <AddPatientForm
      v-if="showAddPatient"
      @submit="handleAddPatient"
      @close="showAddPatient = false"
    />

    <!-- Modal de détails du patient -->
    <PatientDetails 
      v-if="showPatientDetails"
      :patient="selectedPatient"
      @close="showPatientDetails = false"
      @edit="editPatient"
    />

    <!-- Modal d'ajout/modification de rendez-vous -->
    <AppointmentEditor
      v-if="showAppointmentEditor"
      :patient="appointmentPatient"
      @close="showAppointmentEditor = false"
      @save="handleAppointmentSubmit"
    />
  </div>
</template>

<style scoped>
/* Importations */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* Variables CSS */
:root {
  --primary: #3b82f6;
  --primary-light: #93c5fd;
  --secondary: #10b981;
  --dark: #1e293b;
  --light: #f8fafc;
  --gray: #64748b;
  --danger: #ef4444;
}

/* Styles de base */
.doctor-view-container {
  font-family: 'Poppins', sans-serif;
  min-height: 100vh;
  background: #f8fafc;
}

/* En-tête */
.doctor-header {
  background: white;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.title-gradient {
  font-size: 1.8rem;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-icon {
  font-size: 2rem;
  color: #3b82f6;
}

.subtitle {
  color: #64748b;
  font-size: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e293b;
  font-weight: 500;
}

.user-name i {
  color: var(--primary);
}

.tab-navigation {
  background: white;
  padding: 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 0.5rem;
}

.tab-button:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.tab-button.active {
  background: #3b82f6;
  color: white;
}

.doctor-content {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-card h3 {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: #1e293b;
}

.stat-trend {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-trend.positive {
  color: #10b981;
}

.patients-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.add-patient-btn {
  padding: 0.75rem 1.5rem;
  background: #347ef6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-patient-btn:hover {
  background: #2563eb;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.period-selector {
  display: flex;
  gap: 0.5rem;
  background: white;
  padding: 0.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.period-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.period-btn.active {
  background: #3b82f6;
  color: white;
}

/* Styles pour éviter le chevauchement */
.calendar-section > * {
  position: relative;
  z-index: 1;
}

.calendar-section > *:last-child {
  margin-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-wrap: wrap;
  }

  .patients-tools {
    flex-direction: column;
    gap: 1rem;
  }

  .search-box {
    max-width: none;
  }

  .calendar-section {
    padding: 1rem;
    gap: 1rem;
  }
}

/* Section Calendrier */
.calendar-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  min-height: 600px;
}

/* Section Créneaux */
.creneaux-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Section Types de Créneaux */
.types-creneaux-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Section Patients */
.patients-section {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.patients-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.search-box {
  position: relative;
  flex: 1;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.add-patient-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.add-patient-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, #059669, #047857);
}

.add-patient-btn i {
  font-size: 1.1rem;
}

.patient-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.patient-item {
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.patient-item:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-weight: 500;
  color: var(--dark);
  margin-bottom: 0.25rem;
}

.patient-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--gray);
}

.patient-details span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.patient-details i {
  font-size: 0.75rem;
}

/* Styles pour la liste des patients */
.patient-avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: 50%;
  color: var(--primary);
  font-size: 1.5rem;
}

.patient-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.5rem;
  border: none;
  background: none;
  color: var(--gray);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  color: var(--primary);
  background: var(--primary-light);
}

/* Responsive */
@media (max-width: 768px) {
  .patients-tools {
    flex-direction: column;
  }
  
  .add-patient-btn {
    width: 100%;
    justify-content: center;
  }
  
  .patient-list {
    grid-template-columns: 1fr;
  }
}
</style>