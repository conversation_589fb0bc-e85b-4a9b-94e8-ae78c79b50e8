<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppointmentStore } from '@/stores/appointmentStore'
import DoctorDashboard from '@/components/doctor/DoctorDashboard.vue'
import CalendarView from '@/components/doctor/CalendarView.vue'
import ConflictAlerts from '@/components/doctor/ConflictAlerts.vue'
import { CalendarSyncService } from '@/services/calendarsync'
import { dateHelpers } from '@/utils/dateHelpers'
import AppointmentManager from '@/components/doctor/AppointmentManager.vue'
import LogoutButton from '@/components/shared/LogoutButton.vue'
import { useAuthStore } from '@/stores/authStore'
import { storeToRefs } from 'pinia'
import { usePatientStore } from '@/stores/patientStore'
import api from '@/services/api'
import AddPatientForm from '@/components/admin/patients/PatientFormModal.vue'
import PatientDetails from '@/components/admin/patients/PatientDetails.vue'
import AppointmentEditor from '@/components/admin/appointments/AppointmentEditor.vue'
import SuggestedSlots from '@/components/doctor/SuggestedSlots.vue'
import SlotTypeManager from '@/components/doctor/SlotTypeManager.vue'
import ConflictTypeManager from '@/components/doctor/ConflictTypeManager.vue'
import DatabaseManager from '@/components/doctor/DatabaseManager.vue'

const store = useAppointmentStore()
const authStore = useAuthStore()
const patientStore = usePatientStore()
const { fullName } = storeToRefs(authStore)
const activeTab = ref('dashboard')
const selectedDate = ref(null)
const searchQuery = ref('')
const selectedPeriod = ref('mois')
const showAddPatient = ref(false)
const patients = ref([])
const loading = ref(false)
const filteredPatients = computed(() => {
  if (!searchQuery.value) return patients.value
  const query = searchQuery.value.toLowerCase()
  return patients.value.filter(patient =>
    patient.nom.toLowerCase().includes(query) ||
    patient.prenom.toLowerCase().includes(query) ||
    `${patient.nom} ${patient.prenom}`.toLowerCase().includes(query)
  )
})

const statistics = ref({
  totalPatients: 0,
  monthlyAppointments: 0,
  completionRate: 0,
  averageConsultationTime: 0
})

const statisticsLoading = ref(false)

const showPatientDetails = ref(false)
const selectedPatient = ref(null)
const showAppointmentEditor = ref(false)
const appointmentPatient = ref(null)

// Fonctions pour les onglets
const getTabIcon = (tab) => {
  const icons = {
    dashboard: 'fas fa-chart-line',
    calendar: 'fas fa-calendar-alt',
    patients: 'fas fa-users',
    creneaux: 'fas fa-clock',
    'types-creneaux': 'fas fa-cogs',
    'types-conflits': 'fas fa-exclamation-triangle',
    analytics: 'fas fa-chart-bar',
    database: 'fas fa-database'
  }
  return icons[tab] || 'fas fa-circle'
}

const getTabLabel = (tab) => {
  const labels = {
    dashboard: 'Tableau de bord',
    calendar: 'Calendrier',
    patients: 'Patients',
    creneaux: 'Créneaux',
    'types-creneaux': 'Types de Créneaux',
    'types-conflits': 'Types de Conflits',
    analytics: 'Analyses',
    database: 'Base de données'
  }
  return labels[tab] || tab
}

// Statistiques (remplacée par loadStatistics plus bas)

// Gestion des patients
const fetchPatients = async () => {
  loading.value = true
  try {
    console.log('🔄 Chargement des patients...')
    const response = await api.get('/patients')
    if (response.data.status === 'success') {
      patients.value = response.data.data || response.data
      console.log('✅ Patients chargés:', patients.value.length)
    } else {
      console.error('❌ Erreur API patients:', response.data.message)
      patients.value = []
    }
  } catch (error) {
    console.error('❌ Erreur lors du chargement des patients:', error)
    patients.value = []
  } finally {
    loading.value = false
  }
}

const handleAddPatient = async (patientData) => {
  try {
    await patientStore.create(patientData)
    showAddPatient.value = false
    await fetchPatients()
  } catch (error) {
    console.error('Erreur lors de l\'ajout du patient:', error)
  }
}

const viewPatientFile = (patient) => {
  selectedPatient.value = patient
  showPatientDetails.value = true
}

const viewPatientHistory = (patient) => {
  console.log('Affichage de l\'historique pour:', patient.nom, patient.prenom)
  // TODO: Implémenter l'affichage de l'historique
  showNotification(`Historique de ${patient.nom} ${patient.prenom}`, 'info')
}

const formatDate = (dateString) => {
  if (!dateString) return 'Non renseigné'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR')
  } catch (error) {
    return 'Date invalide'
  }
}

// Chargement des statistiques réelles
const loadStatistics = async () => {
  statisticsLoading.value = true
  try {
    console.log('📊 Chargement des statistiques...')

    // Récupérer l'ID médecin
    let doctorId = authStore.user?.id || 1

    if (authStore.user && authStore.user.role === 'doctor') {
      try {
        const doctorResponse = await api.get(`/doctors/user/${authStore.user.id}`)
        if (doctorResponse.data.status === 'success' && doctorResponse.data.data) {
          doctorId = doctorResponse.data.data.id
          console.log('✅ ID médecin trouvé pour les stats:', doctorId)
        }
      } catch (error) {
        console.warn('⚠️ Erreur lors de la récupération de l\'ID médecin pour les stats:', error)
      }
    }

    // Charger les statistiques depuis l'API
    const [patientsResponse, appointmentsResponse] = await Promise.all([
      api.get('/patients'),
      api.get(`/appointments/doctor/${doctorId}`)
    ])

    // Calculer les statistiques
    const totalPatients = patientsResponse.data.status === 'success' ?
      (patientsResponse.data.data?.length || 0) : 0

    const appointments = appointmentsResponse.data.status === 'success' ?
      (appointmentsResponse.data.data || []) : []

    // Calculer les RDV du mois en cours
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()
    const monthlyAppointments = appointments.filter(apt => {
      const aptDate = new Date(apt.date_rendez_vous || apt.date)
      return aptDate.getMonth() === currentMonth && aptDate.getFullYear() === currentYear
    }).length

    // Calculer le taux de présence (RDV terminés vs total)
    const completedAppointments = appointments.filter(apt =>
      apt.statut === 'termine' || apt.statut === 'terminé' || apt.status === 'completed'
    ).length
    const completionRate = appointments.length > 0 ?
      Math.round((completedAppointments / appointments.length) * 100) : 0

    // Durée moyenne (simulation - à adapter selon vos données)
    const averageConsultationTime = 30 // minutes

    // Mettre à jour les statistiques
    statistics.value = {
      totalPatients,
      monthlyAppointments,
      completionRate,
      averageConsultationTime
    }

    console.log('✅ Statistiques chargées:', statistics.value)

  } catch (error) {
    console.error('❌ Erreur lors du chargement des statistiques:', error)
    // Garder les valeurs par défaut en cas d'erreur
  } finally {
    statisticsLoading.value = false
  }
}

const editPatient = (patient) => {
  selectedPatient.value = patient
  showPatientDetails.value = true
}

const scheduleAppointment = (patient) => {
  appointmentPatient.value = patient
  showAppointmentEditor.value = true
  activeTab.value = 'calendar'
}

const handleAppointmentSubmit = async (appointmentData) => {
  try {
    await store.createAppointment(appointmentData)
    showAppointmentEditor.value = false
    // Rafraîchir le calendrier
    await store.fetchAppointments()
  } catch (error) {
    console.error('Erreur lors de la création du rendez-vous:', error)
  }
}

onMounted(async () => {
  console.log('🚀 Initialisation de la vue médecin...')
  await Promise.all([
    fetchPatients(),
    loadStatistics()
  ])
  console.log('✅ Vue médecin initialisée')
})

function handleDateSelection(date) {
  selectedDate.value = date
}

// Méthodes pour les actions rapides du dashboard
const viewTodayAppointments = () => {
  console.log('Affichage des rendez-vous d\'aujourd\'hui')
  activeTab.value = 'calendar'
  selectedDate.value = new Date()

  // Feedback visuel
  showNotification('Navigation vers les rendez-vous d\'aujourd\'hui', 'info')
}

const viewNextAppointment = () => {
  console.log('Affichage du prochain rendez-vous')
  activeTab.value = 'calendar'

  // Feedback visuel
  showNotification('Navigation vers le prochain rendez-vous', 'info')
}

// Fonction pour afficher des notifications
const showNotification = (message, type = 'info') => {
  // Créer une notification temporaire
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === 'info' ? 'info-circle' : 'check-circle'}"></i>
    ${message}
  `

  // Styles inline pour la notification
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: type === 'info' ? '#3498db' : '#27ae60',
    color: 'white',
    padding: '1rem 1.5rem',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    zIndex: '9999',
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    fontSize: '0.9rem',
    fontWeight: '500',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease'
  })

  document.body.appendChild(notification)

  // Animation d'entrée
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // Suppression automatique
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 300)
  }, 3000)
}

// Méthodes pour les actions du dashboard
const handleViewPatients = () => {
  console.log('Navigation vers les patients')
  activeTab.value = 'patients'
  showNotification('Affichage de la liste des patients', 'info')
}

const handleNewAppointment = () => {
  console.log('Création d\'un nouveau rendez-vous')
  showAppointmentEditor.value = true
  showNotification('Ouverture du formulaire de rendez-vous', 'info')
}

const handleViewCalendar = () => {
  console.log('Navigation vers le calendrier')
  activeTab.value = 'calendar'
  showNotification('Affichage du calendrier', 'info')
}

const handleViewAnalytics = () => {
  console.log('Navigation vers les analyses')
  activeTab.value = 'analytics'
  showNotification('Affichage des analyses et rapports', 'info')
}
</script>

<template>
  <div class="doctor-view-container">
    <!-- En-tête -->
    <header class="doctor-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="title-gradient">
            <i class="fas fa-user-md header-icon"></i>
            Tableau de bord Médecin
          </h1>
          <p class="subtitle">Gestion des rendez-vous et planning</p>
        </div>
        <div class="user-info">
          <span class="user-name">
            <i class="fas fa-user"></i>
            Dr. {{ fullName }}
          </span>
          <LogoutButton class="logout-btn" />
        </div>
      </div>
    </header>

    <!-- Navigation des onglets -->
    <nav class="tab-navigation">
      <button
        v-for="tab in ['dashboard', 'calendar', 'patients', 'creneaux', 'types-creneaux', 'types-conflits', 'analytics', 'database']"
        :key="tab"
        :class="['tab-button', { active: activeTab === tab }]"
        @click="activeTab = tab"
      >
        <i :class="getTabIcon(tab)"></i>
        {{ getTabLabel(tab) }}
      </button>
    </nav>

    <!-- Contenu principal -->
    <main class="doctor-content">
      <!-- Dashboard -->
      <section v-show="activeTab === 'dashboard'" class="dashboard-section">
        <DoctorDashboard
          class="dashboard-card"
          @view-today-appointments="viewTodayAppointments"
          @view-next-appointment="viewNextAppointment"
          @view-patients="handleViewPatients"
          @new-appointment="handleNewAppointment"
          @view-calendar="handleViewCalendar"
          @view-analytics="handleViewAnalytics"
        />
        <div class="statistics-grid">
          <!-- Indicateur de chargement -->
          <div v-if="statisticsLoading" class="statistics-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Chargement des statistiques...</p>
          </div>

          <!-- Statistiques -->
          <template v-else>
            <div class="stat-card">
              <div class="stat-header">
                <h3>Patients Totaux</h3>
                <div class="stat-badge">
                  <i class="fas fa-database"></i>
                  Données réelles
                </div>
              </div>
              <div class="stat-value">{{ statistics.totalPatients }}</div>
              <div class="stat-trend">
                <i class="fas fa-users"></i>
                Total dans la base
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-header">
                <h3>RDV ce Mois</h3>
                <div class="stat-badge">
                  <i class="fas fa-calendar"></i>
                  {{ new Date().toLocaleDateString('fr-FR', { month: 'long' }) }}
                </div>
              </div>
              <div class="stat-value">{{ statistics.monthlyAppointments }}</div>
              <div class="stat-trend">
                <i class="fas fa-chart-line"></i>
                Mois en cours
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-header">
                <h3>Taux de Présence</h3>
                <div class="stat-badge">
                  <i class="fas fa-percentage"></i>
                  Calculé
                </div>
              </div>
              <div class="stat-value">{{ statistics.completionRate }}%</div>
              <div class="stat-trend" :class="{ positive: statistics.completionRate >= 80 }">
                <i class="fas fa-check-circle"></i>
                {{ statistics.completionRate >= 80 ? 'Excellent' : 'À améliorer' }}
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-header">
                <h3>Durée Moyenne</h3>
                <div class="stat-badge">
                  <i class="fas fa-clock"></i>
                  Estimation
                </div>
              </div>
              <div class="stat-value">{{ statistics.averageConsultationTime }} min</div>
              <div class="stat-trend">
                <i class="fas fa-stopwatch"></i>
                Par consultation
              </div>
            </div>
          </template>
        </div>
        <AppointmentManager class="dashboard-card" />
      </section>

      <!-- Calendrier -->
      <section v-show="activeTab === 'calendar'" class="calendar-section">
        <CalendarView 
          @date-selected="handleDateSelection"
          :selected-date="selectedDate"
        />
        <ConflictAlerts />
      </section>

      <!-- Créneaux -->
      <section v-show="activeTab === 'creneaux'" class="creneaux-section">
        <SuggestedSlots />
      </section>

      <!-- Types de Créneaux -->
      <section v-show="activeTab === 'types-creneaux'" class="types-creneaux-section">
        <SlotTypeManager />
      </section>

      <!-- Types de Conflits -->
      <section v-show="activeTab === 'types-conflits'" class="types-conflits-section">
        <ConflictTypeManager />
      </section>

      <!-- Gestion des Patients -->
      <section v-show="activeTab === 'patients'" class="patients-section">
        <div class="patients-header">
          <div class="header-content">
            <h2>
              <i class="fas fa-users"></i>
              Gestion des Patients
            </h2>
            <div class="patients-stats">
              <div class="stat-badge">
                <i class="fas fa-user-check"></i>
                {{ filteredPatients.length }} patients
              </div>
              <div class="stat-badge">
                <i class="fas fa-database"></i>
                Données réelles
              </div>
            </div>
          </div>

          <div class="patients-tools">
            <div class="search-box">
              <i class="fas fa-search"></i>
              <input type="text" placeholder="Rechercher un patient..." v-model="searchQuery">
            </div>
            <button class="add-patient-btn" @click="showAddPatient = true">
              <i class="fas fa-user-plus"></i>
              Nouveau Patient
            </button>
          </div>
        </div>

        <!-- État de chargement -->
        <div v-if="loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Chargement des patients...</p>
        </div>

        <!-- État vide -->
        <div v-else-if="filteredPatients.length === 0" class="empty-state">
          <i class="fas fa-user-friends"></i>
          <h3>Aucun patient trouvé</h3>
          <p v-if="searchQuery">Aucun patient ne correspond à votre recherche</p>
          <p v-else>Commencez par ajouter vos premiers patients</p>
          <button class="add-patient-btn primary" @click="showAddPatient = true">
            <i class="fas fa-user-plus"></i>
            Ajouter le premier patient
          </button>
        </div>

        <!-- Liste des patients -->
        <div v-else class="patients-grid">
          <div v-for="patient in filteredPatients" :key="patient.id" class="patient-card">
            <div class="patient-header">
              <div class="patient-avatar">
                <i class="fas fa-user"></i>
              </div>
              <div class="patient-info">
                <h3 class="patient-name">{{ patient.nom }} {{ patient.prenom }}</h3>
                <div class="patient-id">ID: {{ patient.id }}</div>
              </div>
              <div class="patient-status">
                <span class="status-badge active">
                  <i class="fas fa-circle"></i>
                  Actif
                </span>
              </div>
            </div>

            <div class="patient-details">
              <div class="contact-info">
                <div class="contact-item" v-if="patient.telephone">
                  <i class="fas fa-phone"></i>
                  <span>{{ patient.telephone }}</span>
                </div>
                <div class="contact-item" v-if="patient.email">
                  <i class="fas fa-envelope"></i>
                  <span>{{ patient.email }}</span>
                </div>
                <div class="contact-item" v-if="patient.date_naissance">
                  <i class="fas fa-birthday-cake"></i>
                  <span>{{ formatDate(patient.date_naissance) }}</span>
                </div>
              </div>
            </div>

            <div class="patient-actions">
              <button class="action-btn primary" title="Voir le dossier" @click="viewPatientFile(patient)">
                <i class="fas fa-folder-open"></i>
                Dossier
              </button>
              <button class="action-btn secondary" title="Prendre un rendez-vous" @click="scheduleAppointment(patient)">
                <i class="fas fa-calendar-plus"></i>
                RDV
              </button>
              <button class="action-btn tertiary" title="Historique" @click="viewPatientHistory(patient)">
                <i class="fas fa-history"></i>
                Historique
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Analyses et Rapports -->
      <section v-show="activeTab === 'analytics'" class="analytics-section">
        <div class="analytics-header">
          <h2>Analyses et Rapports</h2>
          <div class="period-selector">
            <button
              v-for="period in ['semaine', 'mois', 'trimestre']"
              :key="period"
              :class="['period-btn', { active: selectedPeriod === period }]"
              @click="selectedPeriod = period"
            >
              {{ period }}
            </button>
          </div>
        </div>
        <div class="analytics-grid">
          <!-- Graphiques et statistiques à implémenter -->
        </div>
      </section>

      <!-- Base de données -->
      <section v-show="activeTab === 'database'" class="database-section">
        <DatabaseManager />
      </section>

    </main>

    <!-- Modal d'ajout de patient -->
    <AddPatientForm
      v-if="showAddPatient"
      @submit="handleAddPatient"
      @close="showAddPatient = false"
    />

    <!-- Modal de détails du patient -->
    <PatientDetails 
      v-if="showPatientDetails"
      :patient="selectedPatient"
      @close="showPatientDetails = false"
      @edit="editPatient"
    />

    <!-- Modal d'ajout/modification de rendez-vous -->
    <AppointmentEditor
      v-if="showAppointmentEditor"
      :patient="appointmentPatient"
      @close="showAppointmentEditor = false"
      @save="handleAppointmentSubmit"
    />
  </div>
</template>

<style scoped>
/* Importations */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* Variables CSS */
:root {
  --primary: #3b82f6;
  --primary-light: #93c5fd;
  --secondary: #10b981;
  --dark: #1e293b;
  --light: #f8fafc;
  --gray: #64748b;
  --danger: #ef4444;
}

/* Styles de base */
.doctor-view-container {
  font-family: 'Poppins', sans-serif;
  min-height: 100vh;
  background: #f8fafc;
}

/* En-tête */
.doctor-header {
  background: white;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.title-gradient {
  font-size: 1.8rem;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-icon {
  font-size: 2rem;
  color: #3b82f6;
}

.subtitle {
  color: #64748b;
  font-size: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e293b;
  font-weight: 500;
}

.user-name i {
  color: var(--primary);
}

.tab-navigation {
  background: white;
  padding: 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 0.5rem;
}

.tab-button:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.tab-button.active {
  background: #3b82f6;
  color: white;
}

.doctor-content {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.statistics-loading {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: var(--gray);
}

.statistics-loading i {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-left: 4px solid var(--primary);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-light), transparent);
  border-radius: 0 0 0 60px;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.stat-card h3 {
  margin: 0;
  color: var(--dark);
  font-size: 1rem;
  font-weight: 600;
}

.stat-badge {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-trend.positive {
  color: var(--success);
}

.stat-trend i {
  font-size: 0.8rem;
  opacity: 0.8;
}

.patients-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.add-patient-btn {
  padding: 0.75rem 1.5rem;
  background: #347ef6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-patient-btn:hover {
  background: #2563eb;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.period-selector {
  display: flex;
  gap: 0.5rem;
  background: white;
  padding: 0.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.period-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.period-btn.active {
  background: #3b82f6;
  color: white;
}

/* Styles pour éviter le chevauchement */
.calendar-section > * {
  position: relative;
  z-index: 1;
}

.calendar-section > *:last-child {
  margin-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-wrap: wrap;
  }

  .patients-tools {
    flex-direction: column;
    gap: 1rem;
  }

  .search-box {
    max-width: none;
  }

  .calendar-section {
    padding: 1rem;
    gap: 1rem;
  }
}

/* Section Calendrier */
.calendar-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  min-height: 600px;
}

/* Section Créneaux */
.creneaux-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Section Types de Créneaux */
.types-creneaux-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Section Types de Conflits */
.types-conflits-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Section Base de données */
.database-section {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

/* Section Patients */
.patients-section {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.patients-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.search-box {
  position: relative;
  flex: 1;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.add-patient-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.add-patient-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, #059669, #047857);
}

.add-patient-btn i {
  font-size: 1.1rem;
}

.patient-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.patient-item {
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.patient-item:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-weight: 500;
  color: var(--dark);
  margin-bottom: 0.25rem;
}

.patient-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--gray);
}

.patient-details span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.patient-details i {
  font-size: 0.75rem;
}

/* Styles pour la section patients */
.patients-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.patients-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
}

.patients-header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.patients-header h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.patients-stats {
  display: flex;
  gap: 1rem;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.patients-tools {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.95rem;
  backdrop-filter: blur(10px);
}

.search-box input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-box input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.add-patient-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.add-patient-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.add-patient-btn.primary {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* États de chargement et vide */
.loading-state, .empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--gray);
}

.loading-state i {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.empty-state i {
  font-size: 4rem;
  color: var(--gray-light);
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: var(--dark);
}

.empty-state p {
  margin: 0 0 2rem 0;
  color: var(--gray);
}

/* Grille des patients */
.patients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.patient-card {
  background: white;
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.patient-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.patient-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(30%, -30%);
  transition: all 0.4s ease;
}

.patient-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: rgba(102, 126, 234, 0.3);
}

.patient-card:hover::after {
  transform: translate(20%, -20%) scale(1.2);
  background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
}

.patient-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  position: relative;
  z-index: 1;
}

.patient-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
}

.patient-avatar::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.patient-card:hover .patient-avatar::before {
  opacity: 1;
}

.patient-info {
  flex: 1;
}

.patient-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--dark);
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.patient-id {
  font-size: 0.8rem;
  color: var(--gray);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  display: inline-block;
}

.patient-status {
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
}

.status-badge.active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.15));
  color: var(--success);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.status-badge.active i {
  color: var(--success);
  font-size: 0.7rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.patient-details {
  padding: 0 1.5rem 1rem 1.5rem;
  background: white;
}

.contact-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  border-radius: 12px;
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateX(4px);
}

.contact-item i {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  font-size: 0.8rem;
  flex-shrink: 0;
}

.patient-actions {
  display: flex;
  gap: 0.5rem;
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.8));
  border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.action-btn {
  flex: 1;
  min-width: 0;
  padding: 0.875rem 1rem;
  border: 2px solid transparent;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn i {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.action-btn:hover i {
  transform: scale(1.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  color: var(--success);
  border-color: rgba(16, 185, 129, 0.3);
}

.action-btn.secondary:hover {
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.action-btn.tertiary {
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.1), rgba(71, 85, 105, 0.1));
  color: var(--gray);
  border-color: rgba(100, 116, 139, 0.3);
}

.action-btn.tertiary:hover {
  background: linear-gradient(135deg, var(--gray), #475569);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(100, 116, 139, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .patients-tools {
    flex-direction: column;
  }
  
  .add-patient-btn {
    width: 100%;
    justify-content: center;
  }
  
  .patient-list {
    grid-template-columns: 1fr;
  }
}
</style>