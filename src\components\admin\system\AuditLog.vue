<template>
  <AdminCard title="Journal d'Activité" header-color="gray">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Utilisateur
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Action
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Détails
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(log, index) in logs" :key="index">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDateTime(log.timestamp) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10">
                  <img class="h-10 w-10 rounded-full" :src="log.user.avatar" alt="">
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    {{ log.user.name }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ log.user.role }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="getActionClass(log.action)">
                {{ log.action }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              {{ log.details }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <template #footer>
      <Pagination 
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="changePage"
      />
    </template>
  </AdminCard>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuditStore } from '@/stores/auditStore'
import Pagination from '../shared/Pagination.vue'
import AdminCard from '@/components/admin/system/AdminCard.vue'

const auditStore = useAuditStore()
const logs = ref([])
const currentPage = ref(1)
const totalPages = ref(1)

function formatDateTime(timestamp) {
  return new Date(timestamp).toLocaleString('fr-FR')
}

function getActionClass(action) {
  const classes = {
    'CREATE': 'bg-green-100 text-green-800',
    'UPDATE': 'bg-blue-100 text-blue-800',
    'DELETE': 'bg-red-100 text-red-800',
    'LOGIN': 'bg-indigo-100 text-indigo-800',
    'LOGOUT': 'bg-gray-100 text-gray-800'
  }
  return classes[action] || 'bg-gray-100 text-gray-800'
}

async function fetchLogs(page = 1) {
  const response = await auditStore.getLogs(page)
  logs.value = response.data
  currentPage.value = response.currentPage
  totalPages.value = response.totalPages
}

function changePage(page) {
  currentPage.value = page
  fetchLogs(page)
}

onMounted(() => {
  fetchLogs()
})
</script>