
<!-- components/shared/NotificationSystem.vue -->
<template>
  <div class="notification-system">
    <transition-group name="notification" tag="div" class="notifications-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="[
          'notification',
          `notification-${notification.type}`
        ]"
      >
        <div class="notification-icon">
          <span v-if="notification.type === 'success'">✓</span>
          <span v-else-if="notification.type === 'error'">✕</span>
          <span v-else-if="notification.type === 'warning'">⚠</span>
          <span v-else>ℹ</span>
        </div>
        
        <div class="notification-content">
          <div class="notification-title" v-if="notification.title">
            {{ notification.title }}
          </div>
          <div class="notification-message">
            {{ notification.message }}
          </div>
        </div>
        
        <button
          class="notification-close"
          @click="removeNotification(notification.id)"
        >
          ×
        </button>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'NotificationSystem',
  setup() {
    const notifications = ref([])
    let notificationId = 0
    
    const addNotification = (notification) => {
      const id = ++notificationId
      const newNotification = {
        id,
        type: notification.type || 'info',
        title: notification.title || '',
        message: notification.message || '',
        duration: notification.duration || 5000
      }
      
      notifications.value.push(newNotification)
      
      // Auto-suppression après la durée spécifiée
      if (newNotification.duration > 0) {
        setTimeout(() => {
          removeNotification(id)
        }, newNotification.duration)
      }
      
      return id
    }
    
    const removeNotification = (id) => {
      const index = notifications.value.findIndex(n => n.id === id)
      if (index > -1) {
        notifications.value.splice(index, 1)
      }
    }
    
    const clearAll = () => {
      notifications.value = []
    }
    
    // Méthodes raccourcies
    const success = (message, title = '') => {
      return addNotification({ type: 'success', message, title })
    }
    
    const error = (message, title = '') => {
      return addNotification({ type: 'error', message, title, duration: 0 })
    }
    
    const warning = (message, title = '') => {
      return addNotification({ type: 'warning', message, title })
    }
    
    const info = (message, title = '') => {
      return addNotification({ type: 'info', message, title })
    }
    
    // Exposition globale des méthodes
    onMounted(() => {
      window.$notify = {
        success,
        error,
        warning,
        info,
        add: addNotification,
        remove: removeNotification,
        clear: clearAll
      }
    })
    
    onUnmounted(() => {
      delete window.$notify
    })
    
    return {
      notifications,
      removeNotification
    }
  }
}
</script>

<style scoped>
.notification-system {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  pointer-events: none;
}

.notifications-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  pointer-events: auto;
  min-width: 320px;
  max-width: 400px;
}

.notification-success {
  background: rgba(34, 197, 94, 0.95);
  border-left: 4px solid #22c55e;
  color: white;
}

.notification-error {
  background: rgba(239, 68, 68, 0.95);
  border-left: 4px solid #ef4444;
  color: white;
}

.notification-warning {
  background: rgba(245, 158, 11, 0.95);
  border-left: 4px solid #f59e0b;
  color: white;
}

.notification-info {
  background: rgba(59, 130, 246, 0.95);
  border-left: 4px solid #3b82f6;
  color: white;
}

.notification-icon {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 0.125rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.notification-message {
  font-size: 0.875rem;
  opacity: 0.95;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.notification-close:hover {
  opacity: 1;
}

/* Animations */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
