<template>
    <div class="modal-overlay" @click.self="$emit('close')">
      <div class="modal-content">
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
        <h2>Historique médical</h2>
        <div v-if="medicalHistory && medicalHistory.length">
          <ul class="history-list">
            <li v-for="(entry, idx) in medicalHistory" :key="idx">
              <strong>{{ formatDate(entry.date) }} :</strong>
              <span>{{ entry.type }}</span>
              <span v-if="entry.doctor"> — {{ entry.doctor }}</span>
              <div class="notes" v-if="entry.notes">{{ entry.notes }}</div>
            </li>
          </ul>
        </div>
        <div v-else>
          <p>Aucun historique médical disponible.</p>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'MedicalHistoryModal',
    props: {
      medicalHistory: {
        type: Array,
        required: true
      }
    },
    methods: {
      formatDate(date) {
        if (!date) return '-'
        const d = new Date(date)
        return d.toLocaleDateString('fr-FR')
      }
    }
  }
  </script>
  
  <style scoped>
  .modal-overlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.35);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
  }
  .modal-content {
    background: #fff;
    border-radius: 10px;
    padding: 32px 24px 24px 24px;
    min-width: 340px;
    max-width: 480px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.12);
    position: relative;
  }
  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: transparent;
    border: none;
    font-size: 22px;
    color: #888;
    cursor: pointer;
  }
  .history-list {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .history-list li {
    margin-bottom: 18px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
  }
  .notes {
    color: #4a5568;
    font-size: 0.97em;
    margin-top: 4px;
  }
  </style>
  