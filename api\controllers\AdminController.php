<?php

class AdminController {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function getUsers() {
        $stmt = $this->db->query("SELECT id, nom, prenom, email, role FROM utilisateur");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($users);
    }

    public function createUser() {
        $data = json_decode(file_get_contents("php://input"), true);
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

        $stmt = $this->db->prepare("INSERT INTO utilisateur (nom, prenom, email, password, role) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$data['nom'], $data['prenom'], $data['email'], $hashedPassword, $data['role']]);

        http_response_code(201);
        echo json_encode(['message' => 'Utilisateur créé avec succès']);
    }

    public function updateUser($id) {
        $data = json_decode(file_get_contents("php://input"), true);

        $stmt = $this->db->prepare("UPDATE utilisateur SET nom = ?, prenom = ?, email = ?, role = ? WHERE id = ?");
        $stmt->execute([$data['nom'], $data['prenom'], $data['email'], $data['role'], $id]);

        echo json_encode(['message' => 'Utilisateur mis à jour avec succès']);
    }

    public function deleteUser($id) {
        $stmt = $this->db->prepare("DELETE FROM utilisateur WHERE id = ?");
        $stmt->execute([$id]);

        echo json_encode(['message' => 'Utilisateur supprimé avec succès']);
    }
}