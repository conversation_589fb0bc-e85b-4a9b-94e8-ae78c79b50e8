<?php
require_once __DIR__ . '/../config/database.php';

class SystemController {
    private $db;

    public function __construct() {
        // Fix: Use the correct method to get database connection
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Récupère les paramètres système
     */
    public function getSettings() {
        try {
            $query = "SELECT * FROM parametres_systeme LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            $parametres = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$parametres) {
                // Valeurs par défaut
                $parametres = [
                    'nom_clinique' => 'Ma Clinique',
                    'heures_ouverture' => '09:00-17:00',
                    'duree_rendezvous' => 30,
                    'patients_max_par_jour' => 20,
                    'duree_consultation_par_defaut' => 30
                ];
            }
            
            http_response_code(200);
            header('Content-Type: application/json');
            echo json_encode($parametres);
            
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['erreur' => 'Erreur de base de données: ' . $e->getMessage()]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['erreur' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    /**
     * Met à jour les paramètres système
     */
    public function updateSettings() {
        try {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            // Fix: Better JSON validation
            if (json_last_error() !== JSON_ERROR_NONE || empty($data)) {
                http_response_code(400);
                echo json_encode(['erreur' => 'Données JSON invalides']);
                return;
            }
            
            // Check if settings exist
            $checkQuery = "SELECT COUNT(*) FROM parametres_systeme";
            $checkStmt = $this->db->prepare($checkQuery);
            $checkStmt->execute();
            $count = $checkStmt->fetchColumn();
            
            if ($count > 0) {
                // Update existing settings
                $setParts = [];
                $params = [];
                
                foreach ($data as $key => $value) {
                    $setParts[] = "$key = ?";
                    $params[] = $value;
                }
                
                $setClause = implode(', ', $setParts);
                $query = "UPDATE parametres_systeme SET $setClause, date_mise_a_jour = CURRENT_TIMESTAMP";
                
            } else {
                // Insert new settings
                $columns = array_keys($data);
                $placeholders = array_fill(0, count($data), '?');
                
                $columnsStr = implode(', ', $columns);
                $placeholdersStr = implode(', ', $placeholders);
                
                $query = "INSERT INTO parametres_systeme ($columnsStr) VALUES ($placeholdersStr)";
                $params = array_values($data);
            }
            
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                http_response_code(200);
                header('Content-Type: application/json');
                echo json_encode(['succes' => true, 'message' => 'Paramètres mis à jour avec succès']);
            } else {
                http_response_code(500);
                echo json_encode(['erreur' => 'Échec de la mise à jour des paramètres']);
            }
            
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['erreur' => 'Erreur de base de données: ' . $e->getMessage()]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['erreur' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function getAdvancedStats($period = 'month') {
        try {
            // Calcul de la période
            $startDate = date('Y-m-d', strtotime('-1 ' . $period));
            $endDate = date('Y-m-d');

            // Statistiques des rendez-vous
            $appointmentStats = $this->getAppointmentStats($startDate, $endDate);
            
            // Performance des médecins
            $doctorPerformance = $this->getDoctorPerformance($startDate, $endDate);
            
            // Statistiques des pathologies
            $pathologyStats = $this->getPathologyStats($startDate, $endDate);
            
            // Statistiques de satisfaction
            $satisfactionStats = $this->getSatisfactionStats($startDate, $endDate);

            echo json_encode([
                'appointmentStats' => $appointmentStats,
                'doctorPerformance' => $doctorPerformance,
                'pathologyStats' => $pathologyStats,
                'satisfactionStats' => $satisfactionStats
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    private function getAppointmentStats($startDate, $endDate) {
        $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN statut = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN statut = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM rendez_vous 
                WHERE date_rendez_vous BETWEEN ? AND ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $total = $result['total'] ?: 1; // Éviter la division par zéro
        return [
            'attendanceRate' => round(($result['completed'] / $total) * 100, 2),
            'cancellationRate' => round(($result['cancelled'] / $total) * 100, 2)
        ];
    }

    private function getDoctorPerformance($startDate, $endDate) {
        $sql = "SELECT 
                m.id, m.nom, m.prenom, m.specialite,
                COUNT(rv.id) as total_appointments,
                AVG(CASE WHEN rv.statut = 'completed' THEN 1 ELSE 0 END) as completion_rate
                FROM medecins m
                LEFT JOIN rendez_vous rv ON m.id = rv.id_medecin
                WHERE rv.date_rendez_vous BETWEEN ? AND ?
                GROUP BY m.id, m.nom, m.prenom, m.specialite
                ORDER BY completion_rate DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getPathologyStats($startDate, $endDate) {
        $sql = "SELECT 
                diagnostic,
                COUNT(*) as count
                FROM rendez_vous
                WHERE date_rendez_vous BETWEEN ? AND ?
                AND diagnostic IS NOT NULL
                GROUP BY diagnostic
                ORDER BY count DESC
                LIMIT 10";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getSatisfactionStats($startDate, $endDate) {
        $sql = "SELECT 
                AVG(satisfaction_score) as average_score,
                COUNT(*) as total_ratings,
                satisfaction_score,
                COUNT(*) as count
                FROM rendez_vous
                WHERE date_rendez_vous BETWEEN ? AND ?
                AND satisfaction_score IS NOT NULL
                GROUP BY satisfaction_score
                ORDER BY satisfaction_score DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        $ratings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $totalRatings = array_sum(array_column($ratings, 'count'));
        $distribution = [];
        
        foreach ($ratings as $rating) {
            $distribution[] = [
                'stars' => $rating['satisfaction_score'],
                'count' => $rating['count'],
                'percentage' => round(($rating['count'] / $totalRatings) * 100, 2)
            ];
        }
        
        return [
            'overallSatisfaction' => round(array_sum(array_map(function($r) {
                return $r['satisfaction_score'] * $r['count'];
            }, $ratings)) / $totalRatings, 2),
            'distribution' => $distribution
        ];
    }
}