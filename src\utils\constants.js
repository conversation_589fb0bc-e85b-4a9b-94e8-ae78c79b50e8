
// utils/constants.js
export const APPOINTMENT_TYPES = {
  CONSULTATION: 'consultation',
  URGENCE: 'urgence',
  CONTROLE: 'controle',
  VACCINATION: 'vaccination',
  EXAMEN: 'examen'
}

export const APPOINTMENT_STATUS = {
  SCHEDULED: 'scheduled',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed'
}

export const CONFLICT_TYPES = {
  OVERLAP: 'overlap',
  RESOURCE_UNAVAILABLE: 'resource_unavailable',
  BREAK_VIOLATION: 'break_violation',
  CAPACITY_EXCEEDED: 'capacity_exceeded'
}

export const CONFLICT_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

export const SPECIALTIES = [
  'Médecine générale',
  'Cardiologie',
  'Dermatologie',
  'Pédiatrie',
  'Gynécologie',
  'Orthopédie',
  'Ophtalmologie',
  'ORL',
  'Psychiatrie',
  'Radiologie'
]

export const DEFAULT_WORKING_HOURS = {
  0: { available: false }, // Dimanche
  1: { available: true, start: '08:00', end: '18:00' }, // Lundi
  2: { available: true, start: '08:00', end: '18:00' }, // Mardi
  3: { available: true, start: '08:00', end: '18:00' }, // Mercredi
  4: { available: true, start: '08:00', end: '18:00' }, // Jeudi
  5: { available: true, start: '08:00', end: '18:00' }, // Vendredi
  6: { available: false } // Samedi
}