import { pool } from '@/config/database'

class DatabaseService {
  static async query(sql, params = []) {
    try {
      const [results] = await pool.execute(sql, params)
      return results
    } catch (error) {
      console.error('Erreur lors de l\'exécution de la requête:', error)
      throw error
    }
  }

  static async getAppointments() {
    const sql = `
      SELECT 
        a.*,
        p.firstName as patientFirstName,
        p.lastName as patientLastName,
        d.firstName as doctor<PERSON><PERSON>tN<PERSON>,
        d.lastName as doctorLastName
      FROM appointments a
      JOIN patients p ON a.patientId = p.id
      JOIN doctors d ON a.doctorId = d.id
      ORDER BY a.date DESC
    `
    return await this.query(sql)
  }

  static async getPatients() {
    const sql = 'SELECT * FROM patients ORDER BY lastName, firstName'
    return await this.query(sql)
  }

  static async getDoctors() {
    const sql = 'SELECT * FROM doctors ORDER BY lastName, firstName'
    return await this.query(sql)
  }

  static async createAppointment(appointmentData) {
    const sql = `
      INSERT INTO appointments 
      (date, reason, status, notes, patientId, doctorId) 
      VALUES (?, ?, ?, ?, ?, ?)
    `
    const params = [
      appointmentData.date,
      appointmentData.reason,
      appointmentData.status || 'scheduled',
      appointmentData.notes,
      appointmentData.patientId,
      appointmentData.doctorId
    ]
    return await this.query(sql, params)
  }

  static async updateAppointment(id, appointmentData) {
    const sql = `
      UPDATE appointments 
      SET date = ?, reason = ?, status = ?, notes = ?, 
          patientId = ?, doctorId = ?
      WHERE id = ?
    `
    const params = [
      appointmentData.date,
      appointmentData.reason,
      appointmentData.status,
      appointmentData.notes,
      appointmentData.patientId,
      appointmentData.doctorId,
      id
    ]
    return await this.query(sql, params)
  }

  static async deleteAppointment(id) {
    const sql = 'DELETE FROM appointments WHERE id = ?'
    return await this.query(sql, [id])
  }
}

export default DatabaseService 