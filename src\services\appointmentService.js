import api from './api.js'

/**
 * Service pour la gestion des rendez-vous
 */
export const appointmentService = {
  /**
   * Récupérer tous les rendez-vous
   */
  async getAll(params = {}) {
    try {
      console.log('📅 Récupération de tous les rendez-vous...')
      const response = await api.get('/appointments', { params })
      console.log('✅ Rendez-vous récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des rendez-vous:', error)
      throw error
    }
  },

  /**
   * Récupérer un rendez-vous par ID
   */
  async getById(id) {
    try {
      console.log(`📅 Récupération du rendez-vous ${id}...`)
      const response = await api.get(`/appointments/${id}`)
      console.log('✅ Rendez-vous récupéré:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la récupération du rendez-vous ${id}:`, error)
      throw error
    }
  },

  /**
   * R<PERSON><PERSON><PERSON>rer les rendez-vous d'un médecin
   */
  async getByDoctor(doctorId, params = {}) {
    try {
      console.log(`👨‍⚕️ Récupération des rendez-vous du médecin ${doctorId}...`)
      const response = await api.get(`/appointments/doctor/${doctorId}`, { params })
      console.log('✅ Rendez-vous du médecin récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la récupération des rendez-vous du médecin ${doctorId}:`, error)
      throw error
    }
  },

  /**
   * Vérifier les disponibilités et conflits d'un médecin
   */
  async checkAvailability(doctorId, date = null) {
    try {
      const params = date ? { date } : {}
      console.log(`🔍 Vérification des disponibilités du médecin ${doctorId}...`)
      const response = await api.get(`/appointments/availability/${doctorId}`, { params })
      console.log('✅ Disponibilités vérifiées:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la vérification des disponibilités:`, error)
      throw error
    }
  },

  /**
   * Créer un nouveau rendez-vous
   */
  async create(appointmentData) {
    try {
      console.log('📅 Création d\'un nouveau rendez-vous...', appointmentData)
      const response = await api.post('/appointments', appointmentData)
      console.log('✅ Rendez-vous créé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la création du rendez-vous:', error)
      throw error
    }
  },

  /**
   * Mettre à jour un rendez-vous
   */
  async update(id, appointmentData) {
    try {
      console.log(`📅 Mise à jour du rendez-vous ${id}...`, appointmentData)
      const response = await api.put(`/appointments/${id}`, appointmentData)
      console.log('✅ Rendez-vous mis à jour:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la mise à jour du rendez-vous ${id}:`, error)
      throw error
    }
  },

  /**
   * Supprimer un rendez-vous
   */
  async delete(id) {
    try {
      console.log(`📅 Suppression du rendez-vous ${id}...`)
      const response = await api.delete(`/appointments/${id}`)
      console.log('✅ Rendez-vous supprimé:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la suppression du rendez-vous ${id}:`, error)
      throw error
    }
  },

  /**
   * Reprogrammer un rendez-vous
   */
  async reschedule(id, newDateTime, reason = '') {
    try {
      console.log(`📅 Reprogrammation du rendez-vous ${id}...`)
      const response = await api.put(`/appointments/${id}`, {
        date_rendez_vous: newDateTime,
        notes: reason ? `Reprogrammé: ${reason}` : 'Reprogrammé'
      })
      console.log('✅ Rendez-vous reprogrammé:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors de la reprogrammation du rendez-vous ${id}:`, error)
      throw error
    }
  },

  /**
   * Changer le statut d'un rendez-vous
   */
  async updateStatus(id, status) {
    try {
      console.log(`📅 Changement de statut du rendez-vous ${id} vers ${status}...`)
      const response = await api.put(`/appointments/${id}`, { statut: status })
      console.log('✅ Statut mis à jour:', response.data)
      return response.data
    } catch (error) {
      console.error(`❌ Erreur lors du changement de statut:`, error)
      throw error
    }
  },

  /**
   * Rechercher des rendez-vous
   */
  async search(criteria) {
    try {
      console.log('🔍 Recherche de rendez-vous...', criteria)
      const response = await api.get('/appointments', { params: criteria })
      console.log('✅ Résultats de recherche:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la recherche:', error)
      throw error
    }
  },

  /**
   * Obtenir les statistiques des rendez-vous
   */
  async getStats(doctorId = null, period = 'month') {
    try {
      const params = { period }
      if (doctorId) params.doctor_id = doctorId
      
      console.log('📊 Récupération des statistiques...', params)
      const response = await api.get('/appointments/stats', { params })
      console.log('✅ Statistiques récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      throw error
    }
  }
}

export default appointmentService
