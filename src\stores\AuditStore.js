import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/services/api'


export const useAuditStore = defineStore('audit', () => {
  const logs = ref([])
  const currentPage = ref(1)
  const totalPages = ref(1)
  const isLoading = ref(false)
  const error = ref(null)

  async function getLogs(page = 1, limit = 10) {
    try {
      isLoading.value = true
      const response = await api.get('/audit/logs', {
        params: { page, limit }
      })
      logs.value = response.data.logs
      currentPage.value = response.data.currentPage
      totalPages.value = response.data.totalPages
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function logAction(action, details = {}) {
    try {
      await api.post('/audit/log', { action, details })
    } catch (err) {
      console.error('Failed to log action:', err)
    }
  }

  return {
    logs,
    currentPage,
    totalPages,
    isLoading,
    error,
    getLogs,
    logAction
  }
})