

<!-- components/shared/Calendar.vue -->
<template>
  <div class="calendar">
    <div class="calendar-header">
      <div class="calendar-navigation">
        <button @click="previousPeriod" class="nav-button">
          ‹
        </button>
        <h2 class="calendar-title">{{ periodTitle }}</h2>
        <button @click="nextPeriod" class="nav-button">
          ›
        </button>
      </div>
      
      <div class="calendar-controls">
        <div class="view-selector">
          <button
            v-for="view in availableViews"
            :key="view.value"
            @click="changeView(view.value)"
            :class="[
              'view-button',
              { 'active': currentView === view.value }
            ]"
          >
            {{ view.label }}
          </button>
        </div>
        
        <button @click="goToToday" class="today-button">
          Aujourd'hui
        </button>
      </div>
    </div>
    
    <!-- Vue Semaine -->
    <div v-if="currentView === 'week'" class="calendar-week">
      <div class="week-header">
        <div class="time-column"></div>
        <div
          v-for="day in weekDays"
          :key="day.date"
          class="day-header"
          :class="{ 'today': day.isToday }"
        >
          <div class="day-name">{{ day.name }}</div>
          <div class="day-date">{{ day.dateNum }}</div>
        </div>
      </div>
      
      <div class="week-body">
        <div class="time-slots">
          <div
            v-for="hour in timeSlots"
            :key="hour"
            class="time-slot"
          >
            {{ hour }}
          </div>
        </div>
        
        <div class="days-grid">
          <div
            v-for="day in weekDays"
            :key="day.date"
            class="day-column"
          >
            <div
              v-for="event in getEventsForDay(day.date)"
              :key="event.id"
              class="calendar-event"
              :class="[
                `event-${event.type}`,
                { 'conflict': event.hasConflict }
              ]"
              :style="getEventStyle(event)"
              @click="$emit('event-click', event)"
              draggable="true"
              @dragstart="startDrag($event, event)"
              @dragend="endDrag"
            >
              <div class="event-time">
                {{ formatEventTime(event) }}
              </div>
              <div class="event-title">
                {{ event.title }}
              </div>
              <div class="event-patient" v-if="event.patient">
                {{ event.patient }}
              </div>
            </div>
            
            <!-- Zone de drop pour chaque heure -->
            <div
              v-for="hour in timeSlots"
              :key="`${day.date}-${hour}`"
              class="drop-zone"
              :data-date="day.date"
              :data-time="hour"
              @dragover.prevent
              @drop="onDrop"
            ></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Vue Mois -->
    <div v-else-if="currentView === 'month'" class="calendar-month">
      <div class="month-header">
        <div
          v-for="dayName in dayNames"
          :key="dayName"
          class="month-day-header"
        >
          {{ dayName }}
        </div>
      </div>
      
      <div class="month-body">
        <div
          v-for="week in monthWeeks"
          :key="week[0].date"
          class="month-week"
        >
          <div
            v-for="day in week"
            :key="day.date"
            class="month-day"
            :class="{
              'today': day.isToday,
              'other-month': !day.currentMonth,
              'has-events': getEventsForDay(day.date).length > 0
            }"
            @click="$emit('day-click', day)"
          >
            <div class="month-day-number">{{ day.dateNum }}</div>
            <div class="month-day-events">
              <div
                v-for="event in getEventsForDay(day.date).slice(0, 3)"
                :key="event.id"
                class="month-event"
                :class="`event-${event.type}`"
                @click.stop="$emit('event-click', event)"
              >
                {{ event.title }}
              </div>
              <div
                v-if="getEventsForDay(day.date).length > 3"
                class="month-more-events"
              >
                +{{ getEventsForDay(day.date).length - 3 }} autres
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { dateHelpers } from '@/utils/dateHelpers.js'

export default {
  name: 'Calendar',
  props: {
    events: {
      type: Array,
      default: () => []
    },
    view: {
      type: String,
      default: 'week'
    },
    startHour: {
      type: Number,
      default: 8
    },
    endHour: {
      type: Number,
      default: 19
    }
  },
  emits: ['event-click', 'day-click', 'event-drop', 'date-select'],
  setup(props, { emit }) {
    const currentDate = ref(new Date())
    const currentView = ref(props.view)
    const draggedEvent = ref(null)
    
    const availableViews = [
      { value: 'week', label: 'Semaine' },
      { value: 'month', label: 'Mois' }
    ]
    
    const dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam']
    
    const timeSlots = computed(() => {
      const slots = []
      for (let hour = props.startHour; hour <= props.endHour; hour++) {
        slots.push(`${hour.toString().padStart(2, '0')}:00`)
      }
      return slots
    })
    
    const periodTitle = computed(() => {
      if (currentView.value === 'week') {
        const weekDates = dateHelpers.getWeekDates(currentDate.value)
        const start = dateHelpers.formatDate(weekDates[0], 'DD/MM')
        const end = dateHelpers.formatDate(weekDates[6], 'DD/MM/YYYY')
        return `${start} - ${end}`
      } else {
        return dateHelpers.formatDate(currentDate.value, 'MM/YYYY')
      }
    })
    
    const weekDays = computed(() => {
      return dateHelpers.getWeekDates(currentDate.value).map(date => ({
        date: dateHelpers.formatDate(date, 'YYYY-MM-DD'),
        name: dayNames[date.getDay()],
        dateNum: date.getDate(),
        isToday: dateHelpers.isToday(date)
      }))
    })
    
    const monthWeeks = computed(() => {
      // Logique pour générer les semaines du mois
      const year = currentDate.value.getFullYear()
      const month = currentDate.value.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      
      const weeks = []
      let currentWeek = []
      
      // Ajouter les jours du mois précédent si nécessaire
      const startDay = firstDay.getDay()
      for (let i = startDay - 1; i >= 0; i--) {
        const date = new Date(year, month, -i)
        currentWeek.push({
          date: dateHelpers.formatDate(date, 'YYYY-MM-DD'),
          dateNum: date.getDate(),
          isToday: dateHelpers.isToday(date),
          currentMonth: false
        })
      }
      
      // Ajouter les jours du mois courant
      for (let day = 1; day <= lastDay.getDate(); day++) {
        const date = new Date(year, month, day)
        currentWeek.push({
          date: dateHelpers.formatDate(date, 'YYYY-MM-DD'),
          dateNum: day,
          isToday: dateHelpers.isToday(date),
          currentMonth: true
        })
        
        if (currentWeek.length === 7) {
          weeks.push(currentWeek)
          currentWeek = []
        }
      }
      
      // Compléter la dernière semaine si nécessaire
      if (currentWeek.length > 0) {
        const remainingDays = 7 - currentWeek.length
        for (let i = 1; i <= remainingDays; i++) {
          const date = new Date(year, month + 1, i)
          currentWeek.push({
            date: dateHelpers.formatDate(date, 'YYYY-MM-DD'),
            dateNum: i,
            isToday: dateHelpers.isToday(date),
            currentMonth: false
          })
        }
        weeks.push(currentWeek)
      }
      
      return weeks
    })
    
    const getEventsForDay = (date) => {
      return props.events.filter(event => {
        const eventDate = dateHelpers.formatDate(event.start || event.appointment_date, 'YYYY-MM-DD')
        return eventDate === date
      })
    }
    
    const getEventStyle = (event) => {
      const startTime = new Date(event.start || event.appointment_date)
      const startHour = startTime.getHours()
      const startMinute = startTime.getMinutes()
      
      const top = ((startHour - props.startHour) * 60 + startMinute) * (60 / 60) // 60px par heure
      const height = (event.duration || 30) * (60 / 60) // durée en minutes * pixels par minute
      
      return {
        top: `${top}px`,
        height: `${height}px`,
        position: 'absolute',
        left: '4px',
        right: '4px',
        zIndex: 10
      }
    }
    
    const formatEventTime = (event) => {
      const start = new Date(event.start || event.appointment_date)
      const end = new Date(start.getTime() + (event.duration || 30) * 60000)
      return `${dateHelpers.formatDate(start, 'HH:mm')} - ${dateHelpers.formatDate(end, 'HH:mm')}`
    }
    
    const previousPeriod = () => {
      if (currentView.value === 'week') {
        currentDate.value = dateHelpers.addDays(currentDate.value, -7)
      } else {
        currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
      }
    }
    
    const nextPeriod = () => {
      if (currentView.value === 'week') {
        currentDate.value = dateHelpers.addDays(currentDate.value, 7)
      } else {
        currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
      }
    }
    
    const goToToday = () => {
      currentDate.value = new Date()
    }
    
    const changeView = (view) => {
      currentView.value = view
    }
    
    const startDrag = (e, event) => {
      draggedEvent.value = event
      e.dataTransfer.effectAllowed = 'move'
    }
    
    const endDrag = () => {
      draggedEvent.value = null
    }
    
    const onDrop = (e) => {
      e.preventDefault()
      if (!draggedEvent.value) return
      
      const date = e.target.dataset.date
      const time = e.target.dataset.time
      
      if (date && time) {
        const newDateTime = `${date}T${time}:00`
        emit('event-drop', {
          ...draggedEvent.value,
          start: newDateTime,
          appointment_date: newDateTime
        })
      }
      
      draggedEvent.value = null
    }
    
    return {
      currentView,
      currentDate,
      availableViews,
      dayNames,
      timeSlots,
      periodTitle,
      weekDays,
      monthWeeks,
      getEventsForDay,
      getEventStyle,
      formatEventTime,
      previousPeriod,
      nextPeriod,
      goToToday,
      changeView,
      startDrag,
      endDrag,
      onDrop
    }
  }
}
</script>

<style scoped>
.calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-button {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s;
}

.nav-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.calendar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  min-width: 200px;
  text-align: center;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-selector {
  display: flex;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.view-button {
  padding: 0.5rem 1rem;
  background: white;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.view-button:not(:last-child) {
  border-right: 1px solid #d1d5db;
}

.view-button:hover {
  background: #f3f4f6;
}

.view-button.active {
  background: #3b82f6;
  color: white;
}

.today-button {
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.today-button:hover {
  background: #2563eb;
}

/* Vue Semaine */
.calendar-week {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.week-header {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.time-column {
  border-right: 1px solid #e5e7eb;
}

.day-header {
  padding: 0.75rem;
  text-align: center;
  border-right: 1px solid #e5e7eb;
}

.day-header.today {
  background: #eff6ff;
  color: #3b82f6;
  font-weight: 600;
}

.day-name {
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 600;
  opacity: 0.7;
}

.day-date {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 0.25rem;
}

.week-body {
  flex: 1;
  display: grid;
  grid-template-columns: 80px 1fr;
  overflow-y: auto;
}

.time-slots {
  border-right: 1px solid #e5e7eb;
  background: #f9fafb;
}

.time-slot {
  height: 60px;
  padding: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: flex-start;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  position: relative;
}

.day-column {
  border-right: 1px solid #e5e7eb;
  position: relative;
}

.drop-zone {
  height: 60px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.drop-zone:hover {
  background: rgba(59, 130, 246, 0.05);
}

.calendar-event {
  background: #3b82f6;
  color: white;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.calendar-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.calendar-event.conflict {
  background: #ef4444;
  border-color: #dc2626;
}

.event-consultation { background: #3b82f6; }
.event-urgence { background: #ef4444; }
.event-controle { background: #10b981; }
.event-vaccination { background: #f59e0b; }
.event-examen { background: #8b5cf6; }

.event-time {
  font-weight: 600;
  margin-bottom: 0.125rem;
}

.event-title {
  font-weight: 500;
  margin-bottom: 0.125rem;
}

.event-patient {
  opacity: 0.9;
  font-size: 0.6875rem;
}

/* Vue Mois */
.calendar-month {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.month-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.month-day-header {
  padding: 0.75rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  border-right: 1px solid #e5e7eb;
}

.month-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.month-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  flex: 1;
}

.month-day {
  border-right: 1px solid #e5e7eb;
  
  }