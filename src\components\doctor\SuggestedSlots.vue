<template>
  <div class="suggested-slots">
    <!-- Header avec statistiques -->
    <div class="slots-header">
      <div class="header-content">
        <h2>
          <i class="fas fa-clock"></i>
          Créneaux Disponibles
        </h2>
        <div class="subtitle-container">
          <p class="subtitle">Gérez vos créneaux de disponibilité</p>
          <div class="data-source-badge">
            <i class="fas fa-database"></i>
            Données réelles
          </div>
        </div>
      </div>
      
      <div class="header-actions">
        <button @click="showCreateModal = true" class="btn-primary">
          <i class="fas fa-plus"></i>
          Nouveau Créneau
        </button>
        <button @click="cleanExpiredSlots" class="btn-secondary" :disabled="loading">
          <i class="fas fa-broom"></i>
          Nettoyer Expirés
        </button>
      </div>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total || 0 }}</div>
          <div class="stat-label">Total</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.en_attente || 0 }}</div>
          <div class="stat-label">En attente</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon accepted">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.acceptes || 0 }}</div>
          <div class="stat-label">Acceptés</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon score">
          <i class="fas fa-star"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ Math.round((stats.score_moyen || 0) * 100) }}%</div>
          <div class="stat-label">Score moyen</div>
        </div>
      </div>
    </div>

    <!-- Filtres -->
    <div class="filters-section">
      <div class="filters">
        <select v-model="filters.status" class="filter-select">
          <option value="">Tous les statuts</option>
          <option value="en_attente">En attente</option>
          <option value="accepte">Acceptés</option>
          <option value="refuse">Refusés</option>
          <option value="expire">Expirés</option>
        </select>
        
        <input 
          v-model="filters.search" 
          type="text" 
          placeholder="Rechercher un patient..."
          class="filter-input"
        >
        
        <label class="filter-checkbox">
          <input v-model="filters.includeExpired" type="checkbox">
          Inclure les expirés
        </label>
      </div>
    </div>

    <!-- État de chargement -->
    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Chargement des créneaux suggérés...</p>
    </div>

    <!-- État d'erreur -->
    <div v-else-if="error" class="error-state">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button @click="retryLoad" class="retry-btn">Réessayer</button>
    </div>

    <!-- Liste des créneaux -->
    <div v-else-if="filteredSlots.length > 0" class="slots-list">
      <div 
        v-for="slot in filteredSlots" 
        :key="slot.id"
        :class="['slot-card', `status-${slot.statut}`]"
      >
        <div class="slot-header">
          <div class="slot-patient">
            <h3>{{ slot.patient_nom }}</h3>
            <div class="patient-contact">
              <span v-if="slot.patient_telephone">
                <i class="fas fa-phone"></i>
                {{ slot.patient_telephone }}
              </span>
              <span v-if="slot.patient_email">
                <i class="fas fa-envelope"></i>
                {{ slot.patient_email }}
              </span>
            </div>
          </div>
          
          <div class="slot-status">
            <span :class="['status-badge', slot.statut]">
              <i :class="getStatusIcon(slot.statut)"></i>
              {{ getStatusLabel(slot.statut) }}
            </span>
          </div>
        </div>

        <div class="slot-details">
          <div class="slot-datetime">
            <div class="datetime-info">
              <i class="fas fa-calendar"></i>
              <span class="date">{{ formatDate(slot.date_heure_suggeree) }}</span>
            </div>
            <div class="datetime-info">
              <i class="fas fa-clock"></i>
              <span class="time">{{ formatTime(slot.date_heure_suggeree) }}</span>
              <span class="duration">({{ slot.duree }} min)</span>
            </div>
          </div>

          <div v-if="slot.raison" class="slot-reason">
            <i class="fas fa-comment"></i>
            <span>{{ slot.raison }}</span>
          </div>

          <div class="slot-meta">
            <div class="confidence-score">
              <i class="fas fa-star"></i>
              <span>Score: {{ Math.round((slot.score_confiance || 0) * 100) }}%</span>
            </div>
            
            <div class="expiry-info">
              <i class="fas fa-hourglass-half"></i>
              <span v-if="isExpired(slot.expire_le)">Expiré</span>
              <span v-else>Expire dans {{ getDaysUntilExpiry(slot.expire_le) }} jour(s)</span>
            </div>
          </div>
        </div>

        <div class="slot-actions">
          <button 
            v-if="slot.statut === 'en_attente'"
            @click="markAsAccepted(slot.id)"
            class="action-btn accept"
            :disabled="loading"
          >
            <i class="fas fa-check"></i>
            Marquer accepté
          </button>
          
          <button 
            v-if="slot.statut === 'en_attente'"
            @click="markAsRefused(slot.id)"
            class="action-btn refuse"
            :disabled="loading"
          >
            <i class="fas fa-times"></i>
            Marquer refusé
          </button>
          
          <button 
            @click="editSlot(slot)"
            class="action-btn edit"
            :disabled="loading"
          >
            <i class="fas fa-edit"></i>
            Modifier
          </button>
          
          <button 
            @click="deleteSlot(slot.id)"
            class="action-btn delete"
            :disabled="loading"
          >
            <i class="fas fa-trash"></i>
            Supprimer
          </button>
        </div>
      </div>
    </div>

    <!-- État vide -->
    <div v-else class="empty-state">
      <i class="fas fa-calendar-plus"></i>
      <h3>Aucun créneau suggéré</h3>
      <p>Commencez par créer des créneaux pour vos patients</p>
      <button @click="showCreateModal = true" class="btn-primary">
        <i class="fas fa-plus"></i>
        Créer le premier créneau
      </button>
    </div>

    <!-- Modal de création/édition -->
    <CreateSlotModal 
      v-if="showCreateModal"
      :slot-data="editingSlot"
      @close="closeCreateModal"
      @created="handleSlotCreated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import { useAuthStore } from '@/stores/authStore'
import CreateSlotModal from './CreateSlotModal.vue'
import suggestedSlotService from '@/services/suggestedSlotService'

const suggestedSlotStore = useSuggestedSlotStore()
const authStore = useAuthStore()

// État local
const showCreateModal = ref(false)
const editingSlot = ref(null)
const filters = ref({
  status: '',
  search: '',
  includeExpired: false
})

// Computed properties
const slots = computed(() => suggestedSlotStore.slots)
const stats = computed(() => suggestedSlotStore.stats)
const loading = computed(() => suggestedSlotStore.loading)
const error = computed(() => suggestedSlotStore.error)

const filteredSlots = computed(() => {
  let filtered = slots.value

  // Filtrer par statut
  if (filters.value.status) {
    filtered = filtered.filter(slot => slot.statut === filters.value.status)
  }

  // Filtrer par recherche
  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    filtered = filtered.filter(slot => 
      slot.patient_nom?.toLowerCase().includes(search) ||
      slot.patient_telephone?.includes(search) ||
      slot.patient_email?.toLowerCase().includes(search)
    )
  }

  // Filtrer les expirés
  if (!filters.value.includeExpired) {
    filtered = filtered.filter(slot => !isExpired(slot.expire_le))
  }

  return filtered.sort((a, b) => new Date(a.date_heure_suggeree) - new Date(b.date_heure_suggeree))
})

// Méthodes
const loadData = async () => {
  try {
    const doctorId = authStore.user?.id || 1 // Fallback pour les tests
    await Promise.all([
      suggestedSlotStore.loadSlotsByDoctor(doctorId, filters.value.includeExpired),
      suggestedSlotStore.loadStats(doctorId)
    ])
  } catch (error) {
    console.error('Erreur lors du chargement:', error)
  }
}

const retryLoad = () => {
  suggestedSlotStore.clearError()
  loadData()
}

const cleanExpiredSlots = async () => {
  try {
    const result = await suggestedSlotStore.cleanExpired()
    if (result.success) {
      // Notification de succès
      console.log('Créneaux expirés nettoyés:', result.data.deleted_count)
    }
  } catch (error) {
    console.error('Erreur lors du nettoyage:', error)
  }
}

const markAsAccepted = async (slotId) => {
  try {
    const result = await suggestedSlotStore.updateAcceptance(slotId, true, authStore.user?.id)
    if (result.success) {
      console.log('Créneau marqué comme accepté')
    }
  } catch (error) {
    console.error('Erreur lors de l\'acceptation:', error)
  }
}

const markAsRefused = async (slotId) => {
  try {
    const result = await suggestedSlotStore.updateAcceptance(slotId, false, authStore.user?.id)
    if (result.success) {
      console.log('Créneau marqué comme refusé')
    }
  } catch (error) {
    console.error('Erreur lors du refus:', error)
  }
}

const editSlot = (slot) => {
  editingSlot.value = { ...slot }
  showCreateModal.value = true
}

const deleteSlot = async (slotId) => {
  if (confirm('Êtes-vous sûr de vouloir supprimer ce créneau suggéré ?')) {
    try {
      const result = await suggestedSlotStore.deleteSlot(slotId)
      if (result.success) {
        console.log('Créneau supprimé')
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error)
    }
  }
}

const closeCreateModal = () => {
  showCreateModal.value = false
  editingSlot.value = null
}

const handleSlotCreated = () => {
  closeCreateModal()
  loadData() // Recharger les données
}

// Utilitaires
const formatDate = (dateString) => {
  return suggestedSlotService.formatDate(dateString)
}

const formatTime = (dateString) => {
  return suggestedSlotService.formatTime(dateString)
}

const getDaysUntilExpiry = (expiryDate) => {
  return suggestedSlotService.getDaysUntilExpiry(expiryDate)
}

const isExpired = (expiryDate) => {
  return new Date(expiryDate) < new Date()
}

const getStatusIcon = (status) => {
  return suggestedSlotService.getStatusIcon(status)
}

const getStatusLabel = (status) => {
  return suggestedSlotService.getStatusLabel(status)
}

// Watchers
watch(() => filters.value.includeExpired, () => {
  loadData()
})

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.suggested-slots {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.slots-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.header-content h2 {
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  font-size: 1.875rem;
  font-weight: 700;
}

.header-content h2 i {
  color: #3b82f6;
  margin-right: 0.75rem;
}

.subtitle-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.subtitle {
  color: #6b7280;
  margin: 0;
  font-size: 1rem;
}

.data-source-badge {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  border: 1px solid rgba(46, 204, 113, 0.2);
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary, .btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.stat-icon.total { background: #dbeafe; color: #3b82f6; }
.stat-icon.pending { background: #fef3c7; color: #f59e0b; }
.stat-icon.accepted { background: #dcfce7; color: #10b981; }
.stat-icon.score { background: #fce7f3; color: #ec4899; }

.stat-number {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.filters-section {
  margin-bottom: 2rem;
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select, .filter-input {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-state i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-state i {
  font-size: 2rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-state i {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.slots-list {
  display: grid;
  gap: 1.5rem;
}

.slot-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e5e7eb;
}

.slot-card.status-en_attente { border-left-color: #f59e0b; }
.slot-card.status-accepte { border-left-color: #10b981; }
.slot-card.status-refuse { border-left-color: #ef4444; }
.slot-card.status-expire { border-left-color: #6b7280; }

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.slot-patient h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.125rem;
}

.patient-contact {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.patient-contact i {
  margin-right: 0.25rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-badge.en_attente { background: #fef3c7; color: #92400e; }
.status-badge.accepte { background: #dcfce7; color: #166534; }
.status-badge.refuse { background: #fecaca; color: #dc2626; }
.status-badge.expire { background: #f3f4f6; color: #6b7280; }

.slot-details {
  margin-bottom: 1.5rem;
}

.slot-datetime {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.datetime-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
}

.datetime-info i {
  color: #6b7280;
}

.duration {
  color: #6b7280;
  font-size: 0.875rem;
}

.slot-reason {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
  color: #374151;
  font-style: italic;
}

.slot-meta {
  display: flex;
  gap: 2rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.confidence-score, .expiry-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.slot-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.action-btn.accept {
  background: #dcfce7;
  color: #166534;
}

.action-btn.accept:hover {
  background: #bbf7d0;
}

.action-btn.refuse {
  background: #fecaca;
  color: #dc2626;
}

.action-btn.refuse:hover {
  background: #fca5a5;
}

.action-btn.edit {
  background: #dbeafe;
  color: #2563eb;
}

.action-btn.edit:hover {
  background: #bfdbfe;
}

.action-btn.delete {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn.delete:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .suggested-slots {
    padding: 1rem;
  }
  
  .slots-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .header-actions button {
    flex: 1;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .slot-datetime {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .slot-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .slot-actions {
    justify-content: stretch;
  }
  
  .action-btn {
    flex: 1;
    justify-content: center;
  }
}
</style>
