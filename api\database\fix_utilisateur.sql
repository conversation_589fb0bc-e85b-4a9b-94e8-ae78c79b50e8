-- Sauvegarde des données existantes
CREATE TEMPORARY TABLE IF NOT EXISTS temp_utilisateur AS SELECT * FROM utilisateur;

-- Modification de la table utilisateur
ALTER TABLE utilisateur
    MODIFY COLUMN nom VARCHAR(100) NOT NULL,
    MODIFY COLUMN prenom VARCHAR(100) NOT NULL,
    MODIFY COLUMN email VARCHAR(100) NOT NULL,
    MODIFY COLUMN role ENUM('doctor', 'patient', 'admin') NOT NULL,
    MODIFY COLUMN password VARCHAR(255) NOT NULL;

-- Ajout de la contrainte d'unicité sur l'email
ALTER TABLE utilisateur ADD CONSTRAINT unique_email UNIQUE (email);

-- Ajout des colonnes de timestamp
ALTER TABLE utilisateur 
    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP; 