<?php

class EmailService {
    private $smtpHost;
    private $smtpPort;
    private $smtpUsername;
    private $smtpPassword;
    private $fromEmail;
    private $fromName;
    
    public function __construct() {
        // Configuration SMTP (à adapter selon votre fournisseur)
        $this->smtpHost = $_ENV['SMTP_HOST'] ?? 'localhost';
        $this->smtpPort = $_ENV['SMTP_PORT'] ?? 587;
        $this->smtpUsername = $_ENV['SMTP_USERNAME'] ?? '';
        $this->smtpPassword = $_ENV['SMTP_PASSWORD'] ?? '';
        $this->fromEmail = $_ENV['FROM_EMAIL'] ?? '<EMAIL>';
        $this->fromName = $_ENV['FROM_NAME'] ?? 'Système Médical';
    }
    
    /**
     * Envoyer un email de confirmation de RDV au patient
     */
    public function sendAppointmentConfirmationToPatient($appointmentData) {
        $subject = "Confirmation de votre rendez-vous médical";
        
        $body = $this->getPatientConfirmationTemplate($appointmentData);
        
        return $this->sendEmail(
            $appointmentData['patient_email'],
            $appointmentData['patient_nom'],
            $subject,
            $body
        );
    }
    
    /**
     * Envoyer un email de notification au médecin
     */
    public function sendAppointmentNotificationToDoctor($appointmentData) {
        $subject = "Nouveau rendez-vous programmé";
        
        $body = $this->getDoctorNotificationTemplate($appointmentData);
        
        return $this->sendEmail(
            $appointmentData['medecin_email'],
            $appointmentData['medecin_nom'],
            $subject,
            $body
        );
    }
    
    /**
     * Envoyer un rappel de RDV au patient
     */
    public function sendAppointmentReminderToPatient($appointmentData) {
        $subject = "Rappel : Votre rendez-vous médical demain";
        
        $body = $this->getPatientReminderTemplate($appointmentData);
        
        return $this->sendEmail(
            $appointmentData['patient_email'],
            $appointmentData['patient_nom'],
            $subject,
            $body
        );
    }
    
    /**
     * Envoyer un rappel de RDV au médecin
     */
    public function sendAppointmentReminderToDoctor($appointmentData) {
        $subject = "Rappel : Rendez-vous patient demain";
        
        $body = $this->getDoctorReminderTemplate($appointmentData);
        
        return $this->sendEmail(
            $appointmentData['medecin_email'],
            $appointmentData['medecin_nom'],
            $subject,
            $body
        );
    }
    
    /**
     * Envoyer un email d'annulation
     */
    public function sendAppointmentCancellation($appointmentData, $isPatientCancellation = true) {
        $subject = "Annulation de rendez-vous médical";
        
        if ($isPatientCancellation) {
            // Email au médecin
            $body = $this->getCancellationToDoctorTemplate($appointmentData);
            $toEmail = $appointmentData['medecin_email'];
            $toName = $appointmentData['medecin_nom'];
        } else {
            // Email au patient
            $body = $this->getCancellationToPatientTemplate($appointmentData);
            $toEmail = $appointmentData['patient_email'];
            $toName = $appointmentData['patient_nom'];
        }
        
        return $this->sendEmail($toEmail, $toName, $subject, $body);
    }
    
    /**
     * Template de confirmation pour le patient
     */
    private function getPatientConfirmationTemplate($data) {
        $date = date('d/m/Y', strtotime($data['date_rendez_vous']));
        $time = date('H:i', strtotime($data['date_rendez_vous']));
        
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9fafb; }
                .appointment-details { background: white; padding: 15px; border-radius: 8px; margin: 15px 0; }
                .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 12px; }
                .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>✅ Rendez-vous Confirmé</h1>
                </div>
                
                <div class='content'>
                    <p>Bonjour <strong>{$data['patient_nom']}</strong>,</p>
                    
                    <p>Votre rendez-vous médical a été confirmé avec succès.</p>
                    
                    <div class='appointment-details'>
                        <h3>📋 Détails du rendez-vous</h3>
                        <p><strong>📅 Date :</strong> {$date}</p>
                        <p><strong>🕒 Heure :</strong> {$time}</p>
                        <p><strong>⏱️ Durée :</strong> {$data['duree']} minutes</p>
                        <p><strong>👨‍⚕️ Médecin :</strong> {$data['medecin_nom']}</p>
                        <p><strong>🏥 Spécialité :</strong> {$data['specialite']}</p>
                        <p><strong>📝 Type :</strong> {$data['type']}</p>
                        " . (!empty($data['notes']) ? "<p><strong>💬 Notes :</strong> {$data['notes']}</p>" : "") . "
                    </div>
                    
                    <p><strong>📍 Adresse du cabinet :</strong><br>
                    123 Rue de la Santé<br>
                    75000 Paris</p>
                    
                    <p><strong>📞 Contact :</strong> 01 23 45 67 89</p>
                    
                    <div style='text-align: center;'>
                        <a href='#' class='button'>📅 Ajouter au calendrier</a>
                        <a href='#' class='button' style='background: #ef4444;'>❌ Annuler le RDV</a>
                    </div>
                    
                    <p><strong>⚠️ Important :</strong></p>
                    <ul>
                        <li>Merci d'arriver 10 minutes avant l'heure du rendez-vous</li>
                        <li>N'oubliez pas d'apporter votre carte vitale et votre mutuelle</li>
                        <li>En cas d'empêchement, merci de prévenir au moins 24h à l'avance</li>
                    </ul>
                    
                    <p>Vous recevrez un rappel par email 24h avant votre rendez-vous.</p>
                </div>
                
                <div class='footer'>
                    <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                    <p>© 2024 Système Médical - Tous droits réservés</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de notification pour le médecin
     */
    private function getDoctorNotificationTemplate($data) {
        $date = date('d/m/Y', strtotime($data['date_rendez_vous']));
        $time = date('H:i', strtotime($data['date_rendez_vous']));
        
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #1f2937; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9fafb; }
                .patient-info { background: white; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #3b82f6; }
                .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>📅 Nouveau Rendez-vous</h1>
                </div>
                
                <div class='content'>
                    <p>Bonjour <strong>Dr. {$data['medecin_nom']}</strong>,</p>
                    
                    <p>Un nouveau rendez-vous a été programmé dans votre planning.</p>
                    
                    <div class='patient-info'>
                        <h3>👤 Informations du patient</h3>
                        <p><strong>Nom :</strong> {$data['patient_nom']}</p>
                        <p><strong>📞 Téléphone :</strong> {$data['patient_telephone']}</p>
                        <p><strong>📧 Email :</strong> {$data['patient_email']}</p>
                        
                        <h3>📋 Détails du rendez-vous</h3>
                        <p><strong>📅 Date :</strong> {$date}</p>
                        <p><strong>🕒 Heure :</strong> {$time}</p>
                        <p><strong>⏱️ Durée :</strong> {$data['duree']} minutes</p>
                        <p><strong>📝 Type :</strong> {$data['type']}</p>
                        " . (!empty($data['notes']) ? "<p><strong>💬 Notes du patient :</strong> {$data['notes']}</p>" : "") . "
                    </div>
                    
                    <p>Le patient a reçu un email de confirmation avec tous les détails.</p>
                </div>
                
                <div class='footer'>
                    <p>Cet email a été envoyé automatiquement depuis votre système de gestion.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de rappel pour le patient
     */
    private function getPatientReminderTemplate($data) {
        $date = date('d/m/Y', strtotime($data['date_rendez_vous']));
        $time = date('H:i', strtotime($data['date_rendez_vous']));
        
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f59e0b; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #fffbeb; }
                .reminder-box { background: white; padding: 15px; border-radius: 8px; margin: 15px 0; border: 2px solid #f59e0b; }
                .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>⏰ Rappel de Rendez-vous</h1>
                </div>
                
                <div class='content'>
                    <p>Bonjour <strong>{$data['patient_nom']}</strong>,</p>
                    
                    <p>Nous vous rappelons que vous avez un rendez-vous médical <strong>demain</strong>.</p>
                    
                    <div class='reminder-box'>
                        <h3>📋 Rappel de votre rendez-vous</h3>
                        <p><strong>📅 Date :</strong> {$date}</p>
                        <p><strong>🕒 Heure :</strong> {$time}</p>
                        <p><strong>👨‍⚕️ Médecin :</strong> {$data['medecin_nom']}</p>
                        <p><strong>⏱️ Durée :</strong> {$data['duree']} minutes</p>
                    </div>
                    
                    <p><strong>📍 Adresse :</strong> 123 Rue de la Santé, 75000 Paris</p>
                    
                    <p><strong>✅ À ne pas oublier :</strong></p>
                    <ul>
                        <li>Carte vitale et mutuelle</li>
                        <li>Ordonnances en cours</li>
                        <li>Résultats d'examens récents</li>
                        <li>Arriver 10 minutes avant l'heure</li>
                    </ul>
                    
                    <p>En cas d'empêchement de dernière minute, merci de nous contacter au <strong>01 23 45 67 89</strong>.</p>
                </div>
                
                <div class='footer'>
                    <p>Cet email de rappel a été envoyé automatiquement.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template de rappel pour le médecin
     */
    private function getDoctorReminderTemplate($data) {
        $date = date('d/m/Y', strtotime($data['date_rendez_vous']));
        $time = date('H:i', strtotime($data['date_rendez_vous']));
        
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f0f9ff; }
                .appointment-summary { background: white; padding: 15px; border-radius: 8px; margin: 15px 0; }
                .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>📅 Rappel Planning</h1>
                </div>
                
                <div class='content'>
                    <p>Bonjour <strong>Dr. {$data['medecin_nom']}</strong>,</p>
                    
                    <p>Rappel de votre rendez-vous de <strong>demain</strong> :</p>
                    
                    <div class='appointment-summary'>
                        <h3>👤 {$data['patient_nom']}</h3>
                        <p><strong>📅 Date :</strong> {$date}</p>
                        <p><strong>🕒 Heure :</strong> {$time}</p>
                        <p><strong>⏱️ Durée :</strong> {$data['duree']} minutes</p>
                        <p><strong>📞 Contact :</strong> {$data['patient_telephone']}</p>
                        " . (!empty($data['notes']) ? "<p><strong>💬 Notes :</strong> {$data['notes']}</p>" : "") . "
                    </div>
                    
                    <p>Le patient a également reçu un rappel par email.</p>
                </div>
                
                <div class='footer'>
                    <p>Rappel automatique de votre système de gestion.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Template d'annulation
     */
    private function getCancellationToPatientTemplate($data) {
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #ef4444; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #fef2f2; }
                .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>❌ Rendez-vous Annulé</h1>
                </div>
                
                <div class='content'>
                    <p>Bonjour <strong>{$data['patient_nom']}</strong>,</p>
                    
                    <p>Nous vous informons que votre rendez-vous du <strong>" . date('d/m/Y à H:i', strtotime($data['date_rendez_vous'])) . "</strong> avec <strong>{$data['medecin_nom']}</strong> a été annulé.</p>
                    
                    <p>Pour reprendre un nouveau rendez-vous, vous pouvez :</p>
                    <ul>
                        <li>Nous contacter au 01 23 45 67 89</li>
                        <li>Utiliser notre système de prise de rendez-vous en ligne</li>
                    </ul>
                    
                    <p>Nous nous excusons pour la gêne occasionnée.</p>
                </div>
                
                <div class='footer'>
                    <p>Équipe médicale</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Envoyer un email (version simplifiée pour développement)
     */
    private function sendEmail($toEmail, $toName, $subject, $body) {
        // Pour le développement, on simule l'envoi et on log
        $logMessage = "
=== EMAIL ENVOYÉ ===
À: {$toName} <{$toEmail}>
Sujet: {$subject}
Date: " . date('Y-m-d H:i:s') . "
===================
        ";
        
        error_log($logMessage);
        
        // En production, utiliser PHPMailer ou une API comme SendGrid
        /*
        require_once 'vendor/autoload.php';
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        try {
            $mail->isSMTP();
            $mail->Host = $this->smtpHost;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtpUsername;
            $mail->Password = $this->smtpPassword;
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->smtpPort;
            
            $mail->setFrom($this->fromEmail, $this->fromName);
            $mail->addAddress($toEmail, $toName);
            
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            
            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log("Erreur envoi email: " . $mail->ErrorInfo);
            return false;
        }
        */
        
        // Pour le développement, on retourne toujours true
        return true;
    }
}
?>
