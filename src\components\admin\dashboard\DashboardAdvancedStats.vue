<template>
  <div class="advanced-stats">
    <!-- Filtres temporels -->
    <div class="filters">
      <select v-model="timeFilter" @change="fetchStats" class="time-filter">
        <option value="day">Aujourd'hui</option>
        <option value="week">Cette semaine</option>
        <option value="month">Ce mois</option>
        <option value="year">Cette année</option>
      </select>
    </div>

    <!-- Section Analyses Avancées -->
    <section class="analysis-grid">
      <!-- Performance des Médecins -->
      <div class="analysis-card">
        <h3>Performance des Médecins</h3>
        <div class="performance-list">
          <div v-for="doctor in doctorPerformance" :key="doctor.id" class="performance-item">
            <div class="doctor-info">
              <img :src="doctor.avatar" :alt="doctor.name" class="doctor-avatar">
              <div class="doctor-details">
                <h4>{{ doctor.name }}</h4>
                <p>{{ doctor.specialty }}</p>
              </div>
            </div>
            <div class="stats-info">
              <div class="stat">
                <span class="label">Patients</span>
                <span class="value">{{ doctor.patientCount }}</span>
              </div>
              <div class="stat">
                <span class="label">Satisfaction</span>
                <span class="value">{{ doctor.satisfaction }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analyse des Rendez-vous -->
      <div class="analysis-card">
        <h3>Analyse des Rendez-vous</h3>
        <div class="appointment-stats">
          <div class="stat-row">
            <div class="stat-item">
              <span class="label">Taux de Présence</span>
              <div class="progress-circle" :style="{ '--progress': stats.attendanceRate + '%' }">
                <span>{{ stats.attendanceRate }}%</span>
              </div>
            </div>
            <div class="stat-item">
              <span class="label">Annulations</span>
              <div class="progress-circle" :style="{ '--progress': stats.cancellationRate + '%' }">
                <span>{{ stats.cancellationRate }}%</span>
              </div>
            </div>
          </div>
          <div class="trend-graph">
            <!-- Graphique des tendances -->
          </div>
        </div>
      </div>

      <!-- Répartition des Pathologies -->
      <div class="analysis-card">
        <h3>Répartition des Pathologies</h3>
        <div class="pathology-distribution">
          <div v-for="path in pathologyStats" :key="path.name" class="pathology-item">
            <div class="path-info">
              <span class="path-name">{{ path.name }}</span>
              <span class="path-count">{{ path.count }} cas</span>
            </div>
            <div class="path-progress">
              <div class="progress" :style="{ width: path.percentage + '%' }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Satisfaction Patients -->
      <div class="analysis-card">
        <h3>Satisfaction Patients</h3>
        <div class="satisfaction-stats">
          <div class="rating-distribution">
            <div v-for="rating in satisfactionStats" :key="rating.stars" class="rating-bar">
              <span class="stars">{{ rating.stars }}★</span>
              <div class="bar-container">
                <div class="bar" :style="{ width: rating.percentage + '%' }"></div>
              </div>
              <span class="percentage">{{ rating.percentage }}%</span>
            </div>
          </div>
          <div class="overall-satisfaction">
            <span class="label">Satisfaction Globale</span>
            <span class="value">{{ stats.overallSatisfaction }}/5</span>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'

const timeFilter = ref('month')
const stats = ref({
  attendanceRate: 0,
  cancellationRate: 0,
  overallSatisfaction: 0
})

const doctorPerformance = ref([])
const pathologyStats = ref([])
const satisfactionStats = ref([])

async function fetchStats() {
  try {
    const response = await axios.get(`http://localhost:8000/api/dashboard/advanced-stats`, {
      params: { period: timeFilter.value }
    })
    
    const data = response.data
    
    // Mise à jour des statistiques
    stats.value = {
      attendanceRate: data.appointmentStats.attendanceRate,
      cancellationRate: data.appointmentStats.cancellationRate,
      overallSatisfaction: data.satisfactionStats.overallSatisfaction
    }
    
    doctorPerformance.value = data.doctorPerformance
    pathologyStats.value = data.pathologyStats
    satisfactionStats.value = data.satisfactionStats.distribution
  } catch (error) {
    console.error('Erreur lors du chargement des statistiques:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.advanced-stats {
  padding: 1.5rem;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.analysis-card {
  background:rgb(32, 155, 34);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.analysis-card h3 {
  font-size: 1.5rem;
  color:rgb(236, 239, 244);
  margin: 0 0 1.5rem 0;
}

/* Performance des Médecins */
.performance-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.doctor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.doctor-details h4 {
  margin: 0;
  font-size: 0.9rem;
  color:rgb(236, 239, 243);
}

.doctor-details p {
  margin: 0;
  font-size: 0.8rem;
  color:rgb(234, 239, 246);
}

.stats-info {
  display: flex;
  gap: 1rem;
}

.stat {
  text-align: center;
}

.stat .label {
  font-size: 0.8rem;
  color:rgb(239, 242, 246);
  display: block;
}

.stat .value {
  font-size: 1rem;
  font-weight: 600;
  color:rgb(237, 241, 248);
}

/* Analyse des Rendez-vous */
.appointment-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-row {
  display: flex;
  justify-content: space-around;
}

.progress-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(
    var(--admin-primary) calc(var(--progress) * 1%),
    #e2e8f0 0
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  position: absolute;
  width: 70px;
  height: 70px;
  background: white;
  border-radius: 50%;
}

.progress-circle span {
  position: relative;
  font-weight: 600;
  color:rgb(22, 241, 14);
}

/* Répartition des Pathologies */
.pathology-distribution {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pathology-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.path-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.path-name {
  color:rgb(245, 247, 250);
}

.path-count {
  font-weight: 500;
  color:rgb(239, 241, 244);
}

.path-progress {
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.path-progress .progress {
  height: 100%;
  background: var(--admin-primary);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Satisfaction Patients */
.satisfaction-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.rating-distribution {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
}

.stars {
  width: 30px;
  color:rgb(252, 249, 245);
}

.bar-container {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.bar {
  height: 100%;
  background: #f59e0b;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.percentage {
  width: 40px;
  text-align: right;
  color: #64748b;
}

.overall-satisfaction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.overall-satisfaction .label {
  color: #64748b;
}

.overall-satisfaction .value {
  font-size: 1.2rem;
  font-weight: 600;
  color:rgb(247, 244, 239);
}

.filters {
  margin-bottom: 2rem;
  display: flex;
  justify-content: flex-end;
}

.time-filter {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #1e293b;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-filter:hover {
  border-color: var(--admin-primary);
}

.time-filter:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

@media (max-width: 768px) {
  .advanced-stats {
    padding: 1rem;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
  }

  .stat-row {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }

  .filters {
    justify-content: center;
    margin-bottom: 1.5rem;
  }
}
.label{
  color:#ffffff;
  font-size:17px;
}
.overall-satisfaction .label{
  color:#ffffff;
  font-size: 18px
}
</style> 