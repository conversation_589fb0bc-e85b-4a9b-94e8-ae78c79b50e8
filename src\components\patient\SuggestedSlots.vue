<template>
  <div class="suggested-slots">
    <div class="slots-container">
      <!-- En-tête -->
      <div class="slots-header">
        <div class="header-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM15.07 11.25L14.17 12.17C13.45 12.9 13 13.5 13 15H11V14.5C11 13.4 11.45 12.4 12.17 11.67L13.41 10.41C13.78 10.05 14 9.55 14 9C14 7.9 13.1 7 12 7S10 7.9 10 9H8C8 6.79 9.79 5 12 5S16 6.79 16 9C16 9.88 15.64 10.68 15.07 11.25Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="header-content">
          <h4 class="slots-title">Créneaux suggérés</h4>
          <p class="slots-subtitle">Sélections recommandées pour vous</p>
        </div>
        <div class="slots-count" v-if="slots.length > 0">
          <span class="count-badge">{{ slots.length }}</span>
        </div>
      </div>

      <!-- Message si aucun créneau -->
      <div v-if="slots.length === 0" class="no-slots-message">
        <div class="no-slots-icon">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
          </svg>
        </div>
        <h5>Aucun créneau disponible</h5>
        <p>Essayez de modifier vos critères de recherche ou revenez plus tard.</p>
        <button class="btn-refresh" @click="refreshSlots">
          <svg class="refresh-icon" viewBox="0 0 24 24" fill="none">
            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z" fill="currentColor"/>
          </svg>
          Actualiser
        </button>
      </div>

      <!-- Liste des créneaux -->
      <div v-else class="slots-list">
        <div 
          v-for="(slot, index) in slots" 
          :key="slot.id" 
          class="slot-card"
          :class="{ 'selected': selected?.id === slot.id }"
          @click="selectSlot(slot)"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <!-- Indicateur de sélection -->
          <div class="selection-indicator">
            <svg v-if="selected?.id === slot.id" viewBox="0 0 24 24" fill="none">
              <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
            </svg>
          </div>

          <!-- Contenu du créneau -->
          <div class="slot-content">
            <div class="slot-datetime">
              <div class="slot-date">
                <svg class="date-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19Z" fill="currentColor"/>
                </svg>
                <span>{{ formatDate(slot.date) }}</span>
              </div>
              <div class="slot-time">
                <svg class="time-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2ZM17 13H11V7H12.5V11.5H17V13Z" fill="currentColor"/>
                </svg>
                <span>{{ slot.time }}</span>
              </div>
            </div>

            <!-- Badge de disponibilité -->
            <div class="availability-badge">
              <div class="badge-dot"></div>
              <span>Disponible</span>
            </div>
          </div>

          <!-- Bouton de sélection -->
          <button 
            class="slot-select-btn"
            :class="{ 'selected': selected?.id === slot.id }"
            @click.stop="selectSlot(slot)"
          >
            <svg v-if="selected?.id === slot.id" viewBox="0 0 24 24" fill="none">
              <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 6.5V7.5L21 9ZM3 9L9 7.5V6.5L3 7V9Z" fill="currentColor"/>
            </svg>
            {{ selected?.id === slot.id ? 'Sélectionné' : 'Choisir' }}
          </button>

          <!-- Effet de survol -->
          <div class="slot-hover-effect"></div>
        </div>
      </div>

      <!-- Confirmation de sélection -->
      <div v-if="selected" class="selection-confirmation">
        <div class="confirmation-content">
          <div class="confirmation-icon">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="confirmation-text">
            <h5>Créneau sélectionné</h5>
            <p>{{ formatDate(selected.date) }} à {{ selected.time }}</p>
          </div>
          <button class="btn-confirm" @click="confirmSelection">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
            </svg>
            Confirmer
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import api from '@/services/api'

const slots = ref([])
const selected = ref(null)
const loading = ref(false)
const error = ref(null)

const authStore = useAuthStore()

const loadSuggestedSlots = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await api.get(`/slots/suggested/${authStore.user.id}`)
    if (response.data && response.data.success && Array.isArray(response.data.data)) {
      slots.value = response.data.data.map(slot => ({
        id: slot.id,
        date: slot.date_heure_suggeree,
        time: new Date(slot.date_heure_suggeree).toLocaleTimeString('fr-FR', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        doctor: `${slot.medecin_prenom} ${slot.medecin_nom}`,
        duration: slot.duree,
        confidence: slot.score_confiance,
        reason: slot.raison
      }))
    } else {
      slots.value = []
      error.value = "Format de réponse invalide"
    }
  } catch (err) {
    error.value = "Erreur lors du chargement des créneaux suggérés"
    console.error('Erreur détaillée:', err)
    slots.value = []
  } finally {
    loading.value = false
  }
}

const selectSlot = async (slot) => {
  try {
    const response = await api.post(`/slots/${slot.id}/accept`)
    if (response.data.success) {
      selected.value = slot
      // Émettre un événement pour informer le parent
      emit('slot-selected', slot)
    } else {
      error.value = "Impossible d'accepter ce créneau"
    }
  } catch (err) {
    error.value = "Erreur lors de l'acceptation du créneau"
    console.error(err)
  }
}

const refreshSlots = () => {
  loadSuggestedSlots()
}

onMounted(() => {
  loadSuggestedSlots()
})

function confirmSelection() {
  if (selected.value) {
    alert(`Créneau confirmé : ${formatDate(selected.value.date)} à ${selected.value.time}`)
    // Ici tu peux rediriger vers le formulaire de réservation ou enregistrer la sélection
  }
}

function formatDate(dateString) {
  const date = new Date(dateString)
  const options = { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long',
    year: 'numeric'
  }
  return date.toLocaleDateString('fr-FR', options)
}
</script>

<style scoped>
.suggested-slots {
  margin-bottom: 30px;
}

.slots-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideIn 0.6s ease-out;
}

.slots-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.header-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
  flex-shrink: 0;
}

.header-icon svg {
  width: 24px;
  height: 24px;
}

.header-content {
  flex: 1;
}

.slots-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 5px;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.slots-subtitle {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

.count-badge {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(251, 191, 36, 0.3);
}

.no-slots-message {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.no-slots-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #fbb6ce, #f687b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
}

.no-slots-icon svg {
  width: 40px;
  height: 40px;
}

.no-slots-message h5 {
  color: #4a5568;
  font-weight: 600;
  margin-bottom: 10px;
}

.btn-refresh {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 20px auto 0;
  transition: all 0.3s ease;
}

.btn-refresh:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.refresh-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.btn-refresh.refreshing .refresh-icon {
  animation: spin 1s linear infinite;
}

.slots-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.slot-card {
  position: relative;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20px;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.slot-card:hover {
  border-color: #10b981;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(16, 185, 129, 0.15);
}

.slot-card.selected {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.2);
}

.slot-card.selecting {
  animation: pulse 0.3s ease-out;
}

.selection-indicator {
  width: 30px;
  height: 30px;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.slot-card.selected .selection-indicator {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.selection-indicator svg {
  width: 16px;
  height: 16px;
}

.slot-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
}

.slot-datetime {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slot-date, .slot-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.slot-date {
  color: #4a5568;
  font-size: 16px;
}

.slot-time {
  color: #10b981;
  font-size: 18px;
  font-weight: 600;
}

.date-icon, .time-icon {
  width: 16px;
  height: 16px;
  color: #a0aec0;
}

.availability-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 20px;
  color: #059669;
  font-size: 12px;
  font-weight: 500;
}

.badge-dot {
  width: 6px;
  height: 6px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

.slot-select-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.slot-select-btn.selected {
  background: linear-gradient(135deg, #059669, #047857);
}

.slot-select-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.slot-select-btn svg {
  width: 14px;
  height: 14px;
}

.slot-hover-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.6s ease;
}

.slot-card:hover .slot-hover-effect {
  left: 100%;
}

.selection-confirmation {
  margin-top: 25px;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  border: 1px solid #10b981;
  border-radius: 16px;
  padding: 20px;
  animation: slideDown 0.4s ease-out;
}

.confirmation-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.confirmation-icon {
  width: 40px;
  height: 40px;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.confirmation-icon svg {
  width: 20px;
  height: 20px;
}

.confirmation-text {
  flex: 1;
}

.confirmation-text h5 {
  color: #047857;
  font-weight: 600;
  margin: 0 0 5px;
}

.confirmation-text p {
  color: #059669;
  margin: 0;
  font-weight: 500;
}

.btn-confirm {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
}

.btn-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-confirm svg {
  width: 14px;
  height: 14px;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 100px;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .slots-container {
    padding: 20px;
  }
  
  .slots-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .slot-card {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
    padding: 15px;
  }
  
  .slot-content {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .confirmation-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .btn-confirm {
    justify-content: center;
  }
}
</style>