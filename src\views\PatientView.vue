<template>
  <div class="patient-container">
    <!-- En-tête moderne -->
    <header class="patient-header">
      <div class="header-content">
        <h1 class="title-gradient">Espace Patient</h1>
        <div class="user-info">
          <span class="user-name">
            <i class="fas fa-user"></i>
            {{ fullName }}
          </span>
          <LogoutButton class="logout-btn" />
        </div>
      </div>
      <p class="welcome-text">Bienvenue sur votre espace personnel !</p>
    </header>

    <!-- Navigation des modules - Version desktop -->
    <nav class="module-nav desktop-nav">
      <button 
        v-for="module in modules" 
        :key="module.id"
        :class="['module-btn', { active: activeModule === module.id }]"
        @click="activeModule = module.id"
      >
        <i :class="module.icon"></i>
        {{ module.name }}
      </button>
    </nav>

    <!-- Navigation des modules - Version mobile -->
    <div class="mobile-nav">
      <button class="mobile-menu-btn" @click="isMenuOpen = !isMenuOpen">
        <i :class="isMenuOpen ? 'fas fa-times' : 'fas fa-bars'"></i>
        {{ activeModuleName }}
      </button>
      <nav :class="['mobile-menu', { 'menu-open': isMenuOpen }]">
        <button 
          v-for="module in modules" 
          :key="module.id"
          :class="['mobile-module-btn', { active: activeModule === module.id }]"
          @click="selectModule(module.id)"
        >
          <i :class="module.icon"></i>
          {{ module.name }}
        </button>
      </nav>
    </div>

    <!-- Contenu principal avec modules -->
    <main class="patient-content">
      <TransitionGroup name="module">
        <!-- Section Rendez-vous -->
        <section v-show="activeModule === 'appointments'" key="appointments" class="module-section">
          <div class="module-grid">
            <PatientBooking class="module-card" />
            <AvailabilityFilter class="module-card" />
            <PatientSlotCalendar class="module-card" />
          </div>
        </section>

        <!-- Section Profil de Santé -->
        <section v-show="activeModule === 'health'" key="health" class="module-section">
          <HealthProfile class="module-card full-width" />
        </section>

        <!-- Section Historique Médical -->
        <section v-show="activeModule === 'history'" key="history" class="module-section">
          <MedicalHistory class="module-card full-width" />
        </section>

        <!-- Section Documents Administratifs -->
        <section v-show="activeModule === 'documents'" key="documents" class="module-section">
          <AdminDocuments class="module-card full-width" />
        </section>
      </TransitionGroup>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import PatientBooking from '@/components/patient/PatientBooking.vue'
import AvailabilityFilter from '@/components/patient/AvailabilityFilter.vue'
import PatientSlotCalendar from '@/components/patient/PatientSlotCalendar.vue'
import HealthProfile from '@/components/patient/HealthProfile.vue'
import MedicalHistory from '@/components/patient/MedicalHistory.vue'
import AdminDocuments from '@/components/patient/AdminDocuments.vue'
import LogoutButton from '@/components/shared/LogoutButton.vue'
import { useAuthStore } from '@/stores/authStore'
import { storeToRefs } from 'pinia'

const { fullName } = storeToRefs(useAuthStore())
const activeModule = ref('appointments')
const isMenuOpen = ref(false)

const modules = [
  { 
    id: 'appointments', 
    name: 'Rendez-vous', 
    icon: 'fas fa-calendar-alt' 
  },
  { 
    id: 'health', 
    name: 'Profil de Santé', 
    icon: 'fas fa-heartbeat' 
  },
  { 
    id: 'history', 
    name: 'Historique', 
    icon: 'fas fa-history' 
  },
  { 
    id: 'documents', 
    name: 'Documents', 
    icon: 'fas fa-file-alt' 
  }
]

const activeModuleName = computed(() => {
  const activeModuleObj = modules.find(m => m.id === activeModule.value)
  return activeModuleObj ? activeModuleObj.name : ''
})

const selectModule = (moduleId) => {
  activeModule.value = moduleId
  isMenuOpen.value = false
}
</script>

<style scoped>
/* Police moderne */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

.patient-container {
  font-family: 'Poppins', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* En-tête */
.patient-header {
  text-align: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
  padding: 2rem;
  border-radius: 16px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-weight: 500;
}

.user-name i {
  color: #3b82f6;
}

.title-gradient {
  font-size: 2.5rem;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.5rem;
}

.welcome-text {
  color: #64748b;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Navigation des modules - Desktop */
.desktop-nav {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.module-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: #f8fafc;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.module-btn:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
}

.module-btn.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Navigation des modules - Mobile */
.mobile-nav {
  display: none;
  position: relative;
  margin-bottom: 2rem;
}

.mobile-menu-btn {
  width: 100%;
  padding: 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.mobile-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-10px);
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.mobile-menu.menu-open {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.mobile-module-btn {
  width: 100%;
  padding: 1rem;
  text-align: left;
  background: none;
  border: none;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-module-btn:hover {
  background: #f8fafc;
}

.mobile-module-btn.active {
  color: #3b82f6;
  background: #f0f9ff;
}

/* Contenu principal */
.patient-content {
  min-height: 500px;
}

.module-section {
  animation: fadeIn 0.3s ease;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.full-width {
  grid-column: 1 / -1;
}

/* Animations */
.module-enter-active,
.module-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.module-enter-from,
.module-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .title-gradient {
    font-size: 2rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .user-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-nav {
    display: block;
  }

  .module-grid {
    grid-template-columns: 1fr;
  }

  .module-card {
    padding: 1rem;
  }
}
</style>