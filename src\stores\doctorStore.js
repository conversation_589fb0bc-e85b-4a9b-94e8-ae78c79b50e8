import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { doctorService } from '@/services/doctorService'

export const useDoctorStore = defineStore('doctor', () => {
  // État
  const doctors = ref([])
  const currentDoctor = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const formErrors = ref({})

  // Getters
  const doctorsBySpecialty = computed(() =>
    doctors.value.reduce((acc, doctor) => {
      const specialty = doctor.specialite || 'Autre'
      if (!acc[specialty]) acc[specialty] = []
      acc[specialty].push(doctor)
      return acc
    }, {})
  )

  const availableDoctors = computed(() =>
    doctors.value.filter(doctor => doctor.isAvailable)
  )

  // Actions
  const fetchDoctors = async () => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.getAllDoctors();
      if (!response || !response.data) {
        throw new Error('Réponse invalide du serveur');
      }
      doctors.value = response.data.map(doctor => ({
        ...doctor,
        id: doctor.id || doctor.medecin_id, // Utiliser medecin_id comme fallback
        fullName: `${doctor.nom} ${doctor.prenom}`.trim(),
        role: 'doctor' // Ajouter le rôle par défaut
      }));
      console.log('Médecins chargés:', doctors.value); // Ajouter un log pour déboguer
    } catch (err) {
      console.error('Erreur lors de la récupération des médecins:', err);
      error.value = err.message;
      doctors.value = [];
    } finally {
      loading.value = false;
    }
  }

  const fetchDoctorById = async (id) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.getDoctorById(id);
      if (!response || !response.data) {
        throw new Error('Médecin non trouvé');
      }
      currentDoctor.value = {
        ...response.data,
        fullName: `${response.data.nom} ${response.data.prenom}`.trim()
      };
    } catch (err) {
      console.error(`Erreur lors de la récupération du médecin ${id}:`, err);
      error.value = err.message;
      currentDoctor.value = null;
    } finally {
      loading.value = false;
    }
  }

  const createDoctor = async (doctorData) => {
    loading.value = true;
    error.value = null;
    formErrors.value = {};

    try {
        const result = await doctorService.createDoctor(doctorData);
        await fetchDoctors(); // Actualiser la liste
        return { success: true, data: result.data };
        
    } catch (error) {
        error.value = error.message;
        
        // Gestion spécifique des erreurs de validation
        if (error.field === 'email') {
            formErrors.value.email = error.message;
        }
        
        return { 
            success: false, 
            error: error.message,
            field: error.field 
        };
        
    } finally {
        loading.value = false;
    }
};
  const updateDoctor = async (id, doctorData) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.updateDoctor(id, doctorData);
      if (response.success) {
        await fetchDoctors(); // Rafraîchir la liste
        if (currentDoctor.value?.id === id) {
          await fetchDoctorById(id); // Rafraîchir le médecin courant
        }
      }
      return response;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour du médecin ${id}:`, err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  const deleteDoctor = async (id) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.deleteDoctor(id);
      if (response.success) {
        await fetchDoctors(); // Rafraîchir la liste
        if (currentDoctor.value?.id === id) {
          currentDoctor.value = null;
        }
      }
      return response;
    } catch (err) {
      console.error(`Erreur lors de la suppression du médecin ${id}:`, err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  const getWorkingHours = async (id) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.getWorkingHours(id);
      return response.data;
    } catch (err) {
      console.error(`Erreur lors de la récupération des horaires du médecin ${id}:`, err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  const updateWorkingHours = async (id, hours) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.updateWorkingHours(id, hours);
      if (response.success && currentDoctor.value?.id === id) {
        await fetchDoctorById(id); // Rafraîchir le médecin courant
      }
      return response;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour des horaires du médecin ${id}:`, err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  const checkAvailability = async (id, dateTime) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await doctorService.checkAvailability(id, dateTime);
      return response.data;
    } catch (err) {
      console.error(`Erreur lors de la vérification de la disponibilité du médecin ${id}:`, err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
// doctorStore.js - Improved getAvailableSlots
const getAvailableSlots = async (id, date) => {
  loading.value = true;
  error.value = null;
  try {
    const response = await doctorService.getAvailableSlots(id, date);
    return {
      success: true,
      data: response.data
    };
  } catch (err) {
    console.error(`Error fetching slots for doctor ${id}:`, err);
    error.value = err.message;
    
    // Return empty array to prevent UI breakage
    return {
      success: false,
      data: [],
      error: err.message
    };
  } finally {
    loading.value = false;
  }
}

  function setCurrentDoctor(doctor) {
    if (doctor && doctor.id) {
      currentDoctor.value = doctor
    } else {
      console.warn('Tentative de définition d\'un médecin invalide')
    }
  }

  function $reset() {
    doctors.value = []
    currentDoctor.value = null
    error.value = null
  }

  return {
    doctors,
    currentDoctor,
    loading,
    error,
    doctorsBySpecialty,
    availableDoctors,
    fetchDoctors,
    fetchDoctorById,
    createDoctor,
    updateDoctor,
    deleteDoctor,
    getWorkingHours,
    updateWorkingHours,
    checkAvailability,
    getAvailableSlots,
    setCurrentDoctor,
    $reset
  }
})
