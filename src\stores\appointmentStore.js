import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAppointmentStore = defineStore('appointment', {
  state: () => ({
    appointments: [],
    loading: false,
    error: null,
    currentAppointment: null,
    filters: {
      date: null,
      doctorId: null,
      status: null
    }
  }),

  getters: {
    getAppointmentsByDate: (state) => (date) => {
      return state.appointments.filter(app => {
        const appDate = new Date(app.date_rendez_vous || app.date)
        return appDate.toDateString() === date.toDateString()
      })
    },

    filteredAppointments: (state) => {
      let filtered = [...state.appointments]

      if (state.filters.date) {
        filtered = filtered.filter(a => (a.date_rendez_vous || a.date).startsWith(state.filters.date))
      }

      if (state.filters.doctorId) {
        filtered = filtered.filter(a => a.id_medecin === state.filters.doctorId)
      }

      if (state.filters.status) {
        filtered = filtered.filter(a => a.statut === state.filters.status)
      }

      return filtered
    },

    todayAppointments: (state) => {
      const today = new Date().toISOString().split('T')[0]
      const todayAppts = state.appointments.filter(a => {
        const appointmentDate = a.date_rendez_vous || a.date
        return appointmentDate && appointmentDate.startsWith(today)
      })
      console.log('Today appointments calculated:', {
        today,
        totalAppointments: state.appointments.length,
        todayCount: todayAppts.length,
        todayAppts
      })
      return todayAppts
    },

    upcomingAppointments: (state) => {
      const now = new Date()
      return state.filteredAppointments
        .filter(a => new Date(a.date) > now)
        .sort((a, b) => new Date(a.date) - new Date(b.date))
    },

    recentAppointments: (state) => {
      return [...state.filteredAppointments]
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 5)
    },

    appointmentsByDate: (state) => {
      const grouped = {}
      state.filteredAppointments.forEach(apt => {
        const date = apt.date.split('T')[0]
        if (!grouped[date]) grouped[date] = []
        grouped[date].push(apt)
      })
      return grouped
    },

    // Conflicts are now managed by the dedicated conflictStore
    // Use useConflictStore() to access conflict data

    nextAppointment: (state) => {
      const now = new Date()
      const upcoming = state.appointments
        .filter(a => new Date(a.date_rendez_vous || a.date) > now)
        .sort((a, b) => new Date(a.date_rendez_vous || a.date) - new Date(b.date_rendez_vous || b.date))
      return upcoming.length > 0 ? upcoming[0] : null
    }
  },

  actions: {
    async fetchAppointments() {
      this.loading = true
      this.error = null
      try {
        const response = await api.get('/appointments')
        if (response.data.status === 'success') {
          this.appointments = response.data.data || []
          console.log('Appointments loaded:', this.appointments)
        } else {
          throw new Error(response.data.message || 'Erreur lors du chargement des rendez-vous')
        }
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des rendez-vous'
        console.error('Erreur fetchAppointments:', error)
        this.appointments = []
      } finally {
        this.loading = false
      }
    },

    async fetchRecent() {
      this.loading = true
      this.error = null
      try {
        const response = await api.get('/appointments?limit=5')
        if (response.data.status === 'success') {
          // Since fetchRecent is for a specific view, we should not overwrite the main list
          // The component will use the `recentAppointments` getter which slices the main list.
          // Let's fetch all appointments and let the getter handle it, or create a dedicated state property.
          // For now, let's assume we fetch all and the getter does the job.
          this.appointments = response.data.data
        } else {
          throw new Error(response.data.message || 'Erreur lors du chargement des rendez-vous récents')
        }
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des rendez-vous récents'
        console.error('Erreur:', error)
      } finally {
        this.loading = false
      }
    },

    async fetchAppointmentsByDoctor(doctorId) {
      this.loading = true
      try {
        const response = await api.get(`/appointments/doctor/${doctorId}`)
        if (response.data.status === 'success') {
          const appointments = response.data.data || []
          this.appointments = appointments
          console.log('Doctor appointments loaded:', appointments)
          return appointments
        } else {
          throw new Error(response.data.message || 'Erreur lors du chargement des rendez-vous du médecin')
        }
      } catch (error) {
        this.error = 'Erreur lors du chargement des rendez-vous du médecin'
        console.error('Erreur fetchAppointmentsByDoctor:', error)
        this.appointments = []
        return []
      } finally {
        this.loading = false
      }
    },

    async createAppointment(appointmentData) {
      this.loading = true
      try {
        console.log('Store: Création du rendez-vous avec:', appointmentData)
        const response = await api.post('/appointments', appointmentData)
        console.log('Store: Réponse API:', response.data)

        if (response.data.status === 'success' && response.data.data) {
          const appointmentFromAPI = response.data.data

          // Créer l'objet rendez-vous avec une date valide
          const newAppointment = {
            id: appointmentFromAPI.id,
            date_rendez_vous: appointmentFromAPI.date_rendez_vous,
            date: appointmentFromAPI.date_rendez_vous, // Alias pour compatibilité
            patient_nom: appointmentFromAPI.patient_nom,
            medecin_nom: appointmentFromAPI.medecin_nom,
            type: appointmentFromAPI.type,
            statut: appointmentFromAPI.statut,
            notes: appointmentFromAPI.notes,
            priorite: appointmentFromAPI.priorite,
            duree: appointmentFromAPI.duree,
            specialite: appointmentFromAPI.specialite
          }

          this.appointments.push(newAppointment)
          this.error = null
          console.log('Store: Rendez-vous ajouté:', newAppointment)
          return { success: true, data: newAppointment }
        } else {
          throw new Error(response.data.message || 'Réponse API invalide')
        }
      } catch (error) {
        this.error = 'Erreur lors de la création du rendez-vous'
        console.error('Store: Erreur création:', error)
        return { success: false, error: error.response?.data?.message || error.message }
      } finally {
        this.loading = false
      }
    },

    async updateAppointment(id, updateData) {
      this.loading = true
      try {
        const response = await api.put(`/appointments/${id}`, updateData)

        if (response.data.status === 'success') {
          // Recharger le rendez-vous mis à jour depuis l'API
          const updatedResponse = await api.get(`/appointments/${id}`)

          if (updatedResponse.data.status === 'success' && updatedResponse.data.data) {
            const updatedAppointment = updatedResponse.data.data
            const index = this.appointments.findIndex(a => a.id == id)

            if (index !== -1) {
              this.appointments[index] = updatedAppointment
            }

            this.error = null
            return { success: true, data: updatedAppointment }
          }
        }

        throw new Error(response.data.message || 'Erreur lors de la mise à jour')
      } catch (error) {
        this.error = 'Erreur lors de la mise à jour du rendez-vous'
        console.error('Erreur:', error)
        return { success: false, error: error.response?.data?.message || error.message }
      } finally {
        this.loading = false
      }
    },

    async deleteAppointment(id) {
      this.loading = true
      try {
        await api.delete(`/appointments/${id}`)
        this.appointments = this.appointments.filter(a => a.id !== id)
        this.error = null
        return { success: true }
      } catch (error) {
        this.error = 'Erreur lors de la suppression du rendez-vous'
        console.error('Erreur:', error)
        return { success: false, error: error.response?.data?.message || error.message }
      } finally {
        this.loading = false
      }
    },

    async checkAvailability(doctorId, date) {
      this.loading = true
      try {
        const response = await api.get(`/appointments/availability/${doctorId}`, {
          params: { date }
        })
        return response.data
      } catch (error) {
        this.error = 'Erreur lors de la vérification des disponibilités'
        console.error('Erreur:', error)
        return { conflicts: [] }
      } finally {
        this.loading = false
      }
    },

    setCurrentAppointment(appointment) {
      this.currentAppointment = appointment
    },

    setFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
    },

    resetFilters() {
      this.filters = {
        date: null,
        doctorId: null,
        status: null
      }
    },

    clearError() {
      this.error = null
    },

    clearConflicts() {
      // Méthode pour compatibilité avec les composants existants
      this.error = null
    },

    // Méthodes pour les boutons d'action des cartes
    async cancelAppointment(id) {
      this.loading = true
      try {
        console.log('❌ Annulation du rendez-vous:', id)

        const response = await api.put(`/appointments/${id}`, {
          statut: 'annule'
        })

        if (response.data.status === 'success') {
          // Mettre à jour le rendez-vous dans la liste locale
          const index = this.appointments.findIndex(a => a.id == id)
          if (index !== -1) {
            this.appointments[index].statut = 'annule'
            this.appointments[index].status = 'annulé'
          }

          // Mettre à jour aussi dans todayAppointments si c'est aujourd'hui
          const todayIndex = this.todayAppointments.findIndex(a => a.id == id)
          if (todayIndex !== -1) {
            this.todayAppointments[todayIndex].statut = 'annule'
            this.todayAppointments[todayIndex].status = 'annulé'
          }

          this.error = null
          console.log('✅ Rendez-vous annulé avec succès')
          return { success: true, data: response.data.data }
        }

        throw new Error(response.data.message || 'Erreur lors de l\'annulation')
      } catch (error) {
        this.error = 'Erreur lors de l\'annulation du rendez-vous'
        console.error('❌ Erreur:', error)
        return { success: false, error: error.response?.data?.message || error.message }
      } finally {
        this.loading = false
      }
    },

    async confirmAppointment(id) {
      this.loading = true
      try {
        console.log('✅ Confirmation du rendez-vous:', id)

        const response = await api.put(`/appointments/${id}`, {
          statut: 'confirme'
        })

        if (response.data.status === 'success') {
          // Mettre à jour le rendez-vous dans la liste locale
          const index = this.appointments.findIndex(a => a.id == id)
          if (index !== -1) {
            this.appointments[index].statut = 'confirme'
            this.appointments[index].status = 'confirmé'
          }

          // Mettre à jour aussi dans todayAppointments si c'est aujourd'hui
          const todayIndex = this.todayAppointments.findIndex(a => a.id == id)
          if (todayIndex !== -1) {
            this.todayAppointments[todayIndex].statut = 'confirme'
            this.todayAppointments[todayIndex].status = 'confirmé'
          }

          this.error = null
          console.log('✅ Rendez-vous confirmé avec succès')
          return { success: true, data: response.data.data }
        }

        throw new Error(response.data.message || 'Erreur lors de la confirmation')
      } catch (error) {
        this.error = 'Erreur lors de la confirmation du rendez-vous'
        console.error('❌ Erreur:', error)
        return { success: false, error: error.response?.data?.message || error.message }
      } finally {
        this.loading = false
      }
    }
  }
})
