<?php

class CorsMiddleware {
    public static function handle() {
        // Autoriser l'origine spécifique
        $allowedOrigins = ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'];
        if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], $allowedOrigins)) {
            header('Access-Control-Allow-Origin: ' . $_SERVER['HTTP_ORIGIN']);
        } else {
            // Par défaut, autoriser localhost:3000
            header('Access-Control-Allow-Origin: http://localhost:3000');
        }

        // Autoriser les credentials
        header('Access-Control-Allow-Credentials: true');
        
        // Autoriser les en-têtes spécifiques
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization');
        
        // Autoriser les méthodes
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, PATCH, OPTIONS');
        
        // Cache pour les requêtes preflight
        header('Access-Control-Max-Age: 86400');    // 24 heures

        // Gérer les requêtes OPTIONS (preflight)
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
} 