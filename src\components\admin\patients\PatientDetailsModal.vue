<template>
  <div class="patient-details-modal">
    <div class="modal-header">
      <h2>Détails du Patient</h2>
      <button @click="$emit('close')" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-body">
      <div class="patient-info">
        <div class="info-section">
          <h3>Informations Personnelles</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>Nom Complet</label>
              <p>{{ patient.fullName }}</p>
            </div>
            <div class="info-item">
              <label>Âge</label>
              <p>{{ patient.age }} ans</p>
            </div>
            <div class="info-item">
              <label>Téléphone</label>
              <p>{{ patient.phone }}</p>
            </div>
            <div class="info-item">
              <label>Email</label>
              <p>{{ patient.email }}</p>
            </div>
            <div class="info-item">
              <label>Adresse</label>
              <p>{{ patient.address }}</p>
            </div>
            <div class="info-item">
              <label>Numéro de Sécurité Sociale</label>
              <p>{{ patient.socialSecurityNumber }}</p>
            </div>
          </div>
        </div>

        <div class="info-section">
          <h3>Informations Médicales</h3>
          <div class="medical-info">
            <div v-if="patient.allergies && patient.allergies.length" class="medical-item">
              <h4>Allergies</h4>
              <ul>
                <li v-for="allergy in patient.allergies" :key="allergy">{{ allergy }}</li>
              </ul>
            </div>
            <div v-if="patient.medications && patient.medications.length" class="medical-item">
              <h4>Médicaments</h4>
              <ul>
                <li v-for="medication in patient.medications" :key="medication">{{ medication }}</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-section">
          <h3>Contact d'Urgence</h3>
          <div class="emergency-contact">
            <p><strong>Nom :</strong> {{ patient.emergencyContact?.name }}</p>
            <p><strong>Relation :</strong> {{ patient.emergencyContact?.relationship }}</p>
            <p><strong>Téléphone :</strong> {{ patient.emergencyContact?.phone }}</p>
          </div>
        </div>

        <div class="info-section">
          <h3>Historique Médical</h3>
          <div class="medical-history">
            <div v-if="patient.medicalHistory && patient.medicalHistory.length" class="history-list">
              <div v-for="(record, index) in patient.medicalHistory" :key="index" class="history-item">
                <div class="history-date">{{ formatDate(record.date) }}</div>
                <div class="history-details">
                  <p><strong>Type :</strong> {{ record.type }}</p>
                  <p><strong>Médecin :</strong> {{ record.doctor }}</p>
                  <p><strong>Notes :</strong> {{ record.notes }}</p>
                </div>
              </div>
            </div>
            <p v-else>Aucun historique médical disponible</p>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button @click="$emit('update', patient)" class="btn btn-primary">
        <i class="fas fa-edit"></i>
        Modifier
      </button>
      <button @click="$emit('close')" class="btn btn-secondary">
        <i class="fas fa-times"></i>
        Fermer
      </button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  patient: {
    type: Object,
    required: true
  }
})

defineEmits(['close', 'update'])

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('fr-FR')
}
</script>

<style scoped>
.patient-details-modal {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #718096;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f7fafc;
  color: #e53e3e;
}

.modal-body {
  margin-bottom: 1.5rem;
}

.info-section {
  margin-bottom: 2rem;
}

.info-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 0.5rem;
}

.info-item label {
  font-size: 0.875rem;
  color: #718096;
  margin-bottom: 0.25rem;
  display: block;
}

.info-item p {
  font-size: 1rem;
  color: #2d3748;
  margin: 0;
}

.medical-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.medical-item {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 0.5rem;
}

.medical-item h4 {
  font-size: 1rem;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.medical-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.medical-item li {
  font-size: 0.875rem;
  color: #4a5568;
  padding: 0.25rem 0;
}

.emergency-contact {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 0.5rem;
}

.emergency-contact p {
  margin: 0.5rem 0;
  color: #4a5568;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 0.5rem;
  display: flex;
  gap: 1rem;
}

.history-date {
  min-width: 100px;
  font-weight: 500;
  color: #4a5568;
}

.history-details {
  flex: 1;
}

.history-details p {
  margin: 0.25rem 0;
  color: #4a5568;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #4299e1;
  color: white;
}

.btn-primary:hover {
  background: #3182ce;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}
</style> 