<template>
  <div class="patient-slot-calendar">
    <!-- Header -->
    <div class="calendar-header">
      <h3>
        <i class="fas fa-calendar-alt"></i>
        Aperçu des Créneaux
      </h3>
      <p class="subtitle">Vue d'ensemble des disponibilités</p>
    </div>

    <!-- Filtres simplifiés -->
    <div class="filters-section">
      <div class="filters">
        <input
          v-model="selectedDate"
          type="date"
          :min="minDate"
          class="filter-input"
          placeholder="Filtrer par date"
        >

        <select v-model="selectedDoctor" class="filter-select">
          <option value="">Tous les médecins</option>
          <option v-for="doctor in doctors" :key="doctor.id" :value="doctor.id">
            Dr. {{ doctor.nom }} {{ doctor.prenom }}
          </option>
        </select>
      </div>
    </div>

    <!-- État de chargement -->
    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Chargement des créneaux disponibles...</p>
    </div>

    <!-- État d'erreur -->
    <div v-else-if="error" class="error-state">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button @click="loadSlots" class="retry-btn">Réessayer</button>
    </div>

    <!-- Calendrier des créneaux -->
    <div v-else-if="groupedSlots && Object.keys(groupedSlots).length > 0" class="calendar-grid">
      <div v-for="(daySlots, date) in groupedSlots" :key="date" class="day-column">
        <div class="day-header">
          <div class="day-name">{{ formatDayName(date) }}</div>
          <div class="day-date">{{ formatDate(date) }}</div>
        </div>
        
        <div class="time-slots">
          <div 
            v-for="slot in daySlots" 
            :key="slot.id"
            :class="['time-slot', { 
              'available': slot.statut === 'disponible',
              'selected': selectedSlot?.id === slot.id 
            }]"
            @click="selectSlot(slot)"
          >
            <div class="slot-time">
              <i class="fas fa-clock"></i>
              {{ formatTime(slot.date_heure_suggeree) }}
            </div>
            
            <div class="slot-duration">
              {{ slot.duree }} min
            </div>
            
            <div v-if="slot.medecin_nom" class="slot-doctor">
              {{ slot.medecin_nom }}
            </div>
            
            <div v-if="slot.raison" class="slot-reason">
              {{ slot.raison }}
            </div>
            
            <div class="slot-confidence">
              <i class="fas fa-star"></i>
              {{ Math.round((slot.score_confiance || 0) * 100) }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- État vide -->
    <div v-else class="empty-state">
      <i class="fas fa-calendar-times"></i>
      <h3>Aucun créneau disponible</h3>
      <p>Aucun créneau ne correspond à vos critères de recherche</p>
      <button @click="clearFilters" class="btn-secondary">
        Effacer les filtres
      </button>
    </div>

    <!-- Modal de confirmation -->
    <div v-if="showConfirmModal" class="modal-overlay" @click="closeConfirmModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>
            <i class="fas fa-calendar-check"></i>
            Confirmer le rendez-vous
          </h3>
          <button @click="closeConfirmModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div v-if="selectedSlot" class="appointment-details">
            <div class="detail-row">
              <strong>Date :</strong>
              <span>{{ formatFullDate(selectedSlot.date_heure_suggeree) }}</span>
            </div>
            
            <div class="detail-row">
              <strong>Heure :</strong>
              <span>{{ formatTime(selectedSlot.date_heure_suggeree) }}</span>
            </div>
            
            <div class="detail-row">
              <strong>Durée :</strong>
              <span>{{ selectedSlot.duree }} minutes</span>
            </div>
            
            <div v-if="selectedSlot.medecin_nom" class="detail-row">
              <strong>Médecin :</strong>
              <span>{{ selectedSlot.medecin_nom }}</span>
            </div>
            
            <div v-if="selectedSlot.raison" class="detail-row">
              <strong>Notes :</strong>
              <span>{{ selectedSlot.raison }}</span>
            </div>
          </div>
          
          <div class="form-group">
            <label for="appointment-reason">Motif de consultation</label>
            <textarea 
              id="appointment-reason"
              v-model="appointmentReason" 
              rows="3"
              placeholder="Décrivez brièvement le motif de votre consultation..."
              class="form-control"
            ></textarea>
          </div>
        </div>
        
        <div class="modal-actions">
          <button @click="closeConfirmModal" class="btn-secondary">
            Annuler
          </button>
          <button 
            @click="confirmAppointment" 
            :disabled="bookingAppointment"
            class="btn-primary"
          >
            <i v-if="bookingAppointment" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-check"></i>
            Confirmer le rendez-vous
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useSuggestedSlotStore } from '@/stores/suggestedSlotStore'
import { useAppointmentStore } from '@/stores/appointmentStore'
import { useAuthStore } from '@/stores/authStore'
import suggestedSlotService from '@/services/suggestedSlotService'

const suggestedSlotStore = useSuggestedSlotStore()
const appointmentStore = useAppointmentStore()
const authStore = useAuthStore()

// État local
const selectedDoctor = ref('')
const selectedDate = ref('')
const selectedDuration = ref('')
const selectedSlot = ref(null)
const showConfirmModal = ref(false)
const appointmentReason = ref('')
const bookingAppointment = ref(false)
const doctors = ref([])

// Computed properties
const loading = computed(() => suggestedSlotStore.loading)
const error = computed(() => suggestedSlotStore.error)
const slots = computed(() => suggestedSlotStore.slots)

const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const filteredSlots = computed(() => {
  let filtered = slots.value.filter(slot => 
    slot.statut === 'disponible' && 
    new Date(slot.date_heure_suggeree) > new Date()
  )

  if (selectedDoctor.value) {
    filtered = filtered.filter(slot => slot.id_medecin == selectedDoctor.value)
  }

  if (selectedDate.value) {
    filtered = filtered.filter(slot => 
      slot.date_heure_suggeree.startsWith(selectedDate.value)
    )
  }

  if (selectedDuration.value) {
    filtered = filtered.filter(slot => slot.duree == selectedDuration.value)
  }

  return filtered.sort((a, b) => 
    new Date(a.date_heure_suggeree) - new Date(b.date_heure_suggeree)
  )
})

const groupedSlots = computed(() => {
  const groups = {}

  filteredSlots.value.forEach(slot => {
    const date = slot.date_heure_suggeree.split(' ')[0]
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(slot)
  })

  // Trier les créneaux de chaque jour par heure
  Object.keys(groups).forEach(date => {
    groups[date].sort((a, b) =>
      new Date(a.date_heure_suggeree) - new Date(b.date_heure_suggeree)
    )
  })

  // Limiter à 3 jours pour la version compacte
  const sortedDates = Object.keys(groups).sort().slice(0, 3)
  const limitedGroups = {}
  sortedDates.forEach(date => {
    limitedGroups[date] = groups[date].slice(0, 4) // Max 4 créneaux par jour
  })

  return limitedGroups
})

// Méthodes
const loadSlots = async () => {
  try {
    // Charger tous les créneaux disponibles (sans filtrer par médecin)
    await suggestedSlotStore.loadSlotsByDoctor(null, false)
  } catch (error) {
    console.error('Erreur lors du chargement des créneaux:', error)
  }
}

const loadDoctors = async () => {
  try {
    // Charger la liste des médecins
    // TODO: Implémenter l'API des médecins
    doctors.value = [
      { id: 1, nom: 'Martin', prenom: 'Jean', specialite: 'Médecine générale' },
      { id: 2, nom: 'Dubois', prenom: 'Marie', specialite: 'Cardiologie' }
    ]
  } catch (error) {
    console.error('Erreur lors du chargement des médecins:', error)
  }
}

const selectSlot = (slot) => {
  if (slot.statut === 'disponible') {
    selectedSlot.value = slot
    showConfirmModal.value = true
  }
}

const closeConfirmModal = () => {
  showConfirmModal.value = false
  selectedSlot.value = null
  appointmentReason.value = ''
}

const confirmAppointment = async () => {
  if (!selectedSlot.value) return

  try {
    bookingAppointment.value = true
    
    // Créer le rendez-vous
    const appointmentData = {
      id_medecin: selectedSlot.value.id_medecin,
      id_patient: authStore.user?.id || 1, // ID du patient connecté
      date_rendez_vous: selectedSlot.value.date_heure_suggeree,
      duree: selectedSlot.value.duree,
      type: 'consultation',
      notes: appointmentReason.value,
      statut: 'planifie',
      priorite: 'moyenne'
    }
    
    const result = await appointmentStore.createAppointment(appointmentData)
    
    if (result.success) {
      // Marquer le créneau comme accepté
      await suggestedSlotStore.updateAcceptance(
        selectedSlot.value.id, 
        true, 
        authStore.user?.id
      )
      
      // Recharger les créneaux
      await loadSlots()
      
      closeConfirmModal()
      
      // Notification de succès
      alert('Rendez-vous confirmé avec succès !')
    } else {
      alert('Erreur lors de la confirmation du rendez-vous')
    }
  } catch (error) {
    console.error('Erreur lors de la confirmation:', error)
    alert('Erreur lors de la confirmation du rendez-vous')
  } finally {
    bookingAppointment.value = false
  }
}

const clearFilters = () => {
  selectedDoctor.value = ''
  selectedDate.value = ''
  selectedDuration.value = ''
}

// Utilitaires de formatage
const formatDayName = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', { weekday: 'long' })
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', { 
    day: 'numeric', 
    month: 'short' 
  })
}

const formatTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('fr-FR', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const formatFullDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Watchers
watch([selectedDoctor, selectedDate, selectedDuration], () => {
  // Les filtres sont réactifs via computed properties
})

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadSlots(),
    loadDoctors()
  ])
})
</script>

<style scoped>
.patient-slot-calendar {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.calendar-header {
  text-align: center;
  margin-bottom: 2rem;
}

.calendar-header h3 {
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.calendar-header h3 i {
  color: #3b82f6;
  margin-right: 0.5rem;
}

.subtitle {
  color: #6b7280;
  margin: 0;
}

.filters-section {
  margin-bottom: 2rem;
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-select, .filter-input {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  min-width: 150px;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-state i {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.error-state i {
  font-size: 2rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.empty-state i {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.retry-btn, .btn-secondary, .btn-primary {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.retry-btn, .btn-primary {
  background: #3b82f6;
  color: white;
}

.retry-btn:hover, .btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  flex: 1;
  overflow-y: auto;
}

.day-column {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.day-header {
  background: #3b82f6;
  color: white;
  padding: 1rem;
  text-align: center;
}

.day-name {
  font-weight: 600;
  text-transform: capitalize;
}

.day-date {
  font-size: 0.875rem;
  opacity: 0.9;
}

.time-slots {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.time-slot {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: #f9fafb;
}

.time-slot.available {
  border-color: #10b981;
  background: #ecfdf5;
}

.time-slot.available:hover {
  border-color: #059669;
  background: #d1fae5;
  transform: translateY(-1px);
}

.time-slot.selected {
  border-color: #3b82f6;
  background: #dbeafe;
}

.slot-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.slot-duration {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.slot-doctor {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.slot-reason {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
  margin-bottom: 0.5rem;
}

.slot-confidence {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #f59e0b;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.modal-header h3 i {
  color: #3b82f6;
  margin-right: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 1.5rem;
}

.appointment-details {
  background: #f9fafb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .patient-slot-calendar {
    padding: 1rem;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-select, .filter-input {
    min-width: auto;
  }
  
  .calendar-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}
</style>
