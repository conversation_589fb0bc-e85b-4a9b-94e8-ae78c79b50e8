import { exec } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Démarrer le serveur PHP
const phpServer = exec('cd api && php -S localhost:8000 index.php', (error, stdout, stderr) => {
    if (error) {
        console.error(`Erreur d'exécution : ${error}`);
        return;
    }
    console.log(`Sortie : ${stdout}`);
    console.error(`Erreur : ${stderr}`);
});

phpServer.stdout.on('data', (data) => {
    console.log(`PHP Server: ${data}`);
});

phpServer.stderr.on('data', (data) => {
    console.error(`PHP Server Error: ${data}`);
});

// Démarrer le serveur de développement Vue.js
const vueServer = exec('npm run dev', (error, stdout, stderr) => {
    if (error) {
        console.error(`Erreur d'exécution : ${error}`);
        return;
    }
    console.log(`Sortie : ${stdout}`);
    console.error(`Erreur : ${stderr}`);
});

vueServer.stdout.on('data', (data) => {
    console.log(`Vue Server: ${data}`);
});

vueServer.stderr.on('data', (data) => {
    console.error(`Vue Server Error: ${data}`);
}); 