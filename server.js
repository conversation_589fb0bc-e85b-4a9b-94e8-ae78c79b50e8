import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Démarrage de l\'Agenda Médical...\n');

// Configuration des couleurs pour les logs
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// Fonction pour logger avec couleurs
const log = (prefix, message, color = colors.reset) => {
    console.log(`${color}${colors.bright}[${prefix}]${colors.reset} ${message}`);
};

// Fonction pour gérer l'arrêt propre
const cleanup = () => {
    log('SYSTEM', 'Arrêt des serveurs...', colors.yellow);
    if (phpServer) {
        phpServer.kill('SIGTERM');
    }
    if (vueServer) {
        vueServer.kill('SIGTERM');
    }
    process.exit(0);
};

// Gestion des signaux d'arrêt
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Démarrer le serveur PHP
log('PHP', 'Démarrage du serveur API PHP...', colors.blue);
const phpServer = spawn('php', ['-S', 'localhost:8000', '-t', 'api', 'api/index.php'], {
    cwd: __dirname,
    stdio: 'pipe'
});

phpServer.stdout.on('data', (data) => {
    const message = data.toString().trim();
    if (message) {
        log('PHP', message, colors.blue);
    }
});

phpServer.stderr.on('data', (data) => {
    const message = data.toString().trim();
    if (message && !message.includes('Development Server')) {
        log('PHP', message, colors.red);
    } else if (message.includes('Development Server')) {
        log('PHP', '✅ Serveur API démarré sur http://localhost:8000', colors.green);
    }
});

phpServer.on('error', (error) => {
    log('PHP', `❌ Erreur: ${error.message}`, colors.red);
});

// Attendre un peu avant de démarrer Vue pour laisser PHP s'initialiser
setTimeout(() => {
    log('VUE', 'Démarrage du serveur frontend Vue.js...', colors.cyan);

    const vueServer = spawn('npm', ['run', 'dev'], {
        cwd: __dirname,
        stdio: 'pipe',
        shell: true
    });

    vueServer.stdout.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
            // Filtrer les messages de Vite pour ne garder que les importants
            if (message.includes('Local:') || message.includes('ready in') || message.includes('✓')) {
                log('VUE', message, colors.cyan);
            }
        }
    });

    vueServer.stderr.on('data', (data) => {
        const message = data.toString().trim();
        if (message && !message.includes('WARN')) {
            log('VUE', message, colors.red);
        }
    });

    vueServer.on('error', (error) => {
        log('VUE', `❌ Erreur: ${error.message}`, colors.red);
    });

    // Stocker la référence globalement pour le cleanup
    global.vueServer = vueServer;

}, 2000); // Attendre 2 secondes

// Stocker la référence globalement pour le cleanup
global.phpServer = phpServer;

// Message de bienvenue
setTimeout(() => {
    console.log('\n' + '='.repeat(60));
    log('SYSTEM', '🏥 Agenda Médical - Serveurs démarrés', colors.green);
    console.log('='.repeat(60));
    log('API', '📡 Backend PHP: http://localhost:8000', colors.blue);
    log('WEB', '🌐 Frontend Vue: http://localhost:3000', colors.cyan);
    console.log('='.repeat(60));
    log('SYSTEM', '💡 Appuyez sur Ctrl+C pour arrêter les serveurs', colors.yellow);
    console.log('='.repeat(60) + '\n');
}, 5000);