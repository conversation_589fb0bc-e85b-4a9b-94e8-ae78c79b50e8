<template>
  <AdminCard title="Paramètres Système" header-color="indigo">
    <div class="settings-container">
      <div v-for="setting in settings" :key="setting.key" class="setting-item">
        <div class="setting-content">
          <div class="setting-info">
            <h4 class="setting-label">{{ setting.label }}</h4>
            <p class="setting-description">{{ setting.description }}</p>
          </div>
          <div class="setting-control">
            <template v-if="setting.type === 'toggle'">
              <ToggleSwitch 
                v-model="setting.value"
                @change="updateSetting(setting)"
                class="toggle-switch"
              />
            </template>
            <template v-else-if="setting.type === 'select'">
              <select
                v-model="setting.value"
                @change="updateSetting(setting)"
                class="setting-select"
              >
                <option v-for="option in setting.options" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </template>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="settings-footer">
        <button 
          @click="resetSettings"
          class="btn btn-secondary"
        >
          Réinitialiser
        </button>
        <button 
          @click="saveSettings"
          class="btn btn-primary"
        >
          Enregistrer
        </button>
      </div>
    </template>
  </AdminCard>
</template>

<script setup>
import { ref } from 'vue'
import { useSystemStore } from '@/stores/systemStore'
import ToggleSwitch from '../shared/ToggleSwitch.vue'

const systemStore = useSystemStore()

const settings = ref([
  {
    key: 'maintenance_mode',
    label: 'Mode Maintenance',
    description: 'Activez pour limiter l\'accès au site aux administrateurs',
    type: 'toggle',
    value: false
  },
  {
    key: 'session_timeout',
    label: 'Durée de session',
    description: 'Temps d\'inactivité avant déconnexion automatique',
    type: 'select',
    value: 30,
    options: [
      { value: 15, label: '15 minutes' },
      { value: 30, label: '30 minutes' },
      { value: 60, label: '1 heure' },
      { value: 120, label: '2 heures' }
    ]
  },
  {
    key: 'backup_frequency',
    label: 'Fréquence des sauvegardes',
    description: 'Fréquence des sauvegardes automatiques de la base de données',
    type: 'select',
    value: 'daily',
    options: [
      { value: 'daily', label: 'Quotidienne' },
      { value: 'weekly', label: 'Hebdomadaire' },
      { value: 'monthly', label: 'Mensuelle' }
    ]
  }
])

async function loadSettings() {
  const savedSettings = await systemStore.getSettings()
  settings.value = settings.value.map(setting => ({
    ...setting,
    value: savedSettings[setting.key] || setting.value
  }))
}

function updateSetting(setting) {
  console.log(`Setting ${setting.key} updated to:`, setting.value)
}

async function saveSettings() {
  const settingsToSave = settings.value.reduce((acc, setting) => {
    acc[setting.key] = setting.value
    return acc
  }, {})
  await systemStore.saveSettings(settingsToSave)
}

function resetSettings() {
  loadSettings()
}

loadSettings()
</script>

<style scoped>
/* Styles de base */
.settings-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 8px;
}

.setting-item {
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-size: 16px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.setting-description {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.setting-control {
  min-width: 192px;
}

/* Styles pour le select */
.setting-select {
  display: block;
  width: 100%;
  padding: 8px 40px 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
  background-color: white;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 24px;
  appearance: none;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.setting-select:focus {
  outline: none;
  border-color: #818cf8;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* Styles pour les boutons */
.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #4338ca;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

/* Responsive */
@media (max-width: 640px) {
  .setting-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .setting-control {
    width: 100%;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-container {
  animation: fadeIn 0.4s ease-out;
}
</style>