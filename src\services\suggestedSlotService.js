import api from './api.js'

/**
 * Service pour la gestion des créneaux suggérés
 */
export const suggestedSlotService = {
  /**
   * Récupérer tous les créneaux suggérés d'un médecin
   */
  async getByDoctor(medecinId, includeExpired = false) {
    try {
      console.log('🕒 Récupération des créneaux suggérés du médecin...', medecinId)
      const params = { 
        medecin_id: medecinId,
        include_expired: includeExpired
      }
      const response = await api.get('/suggested-slots', { params })
      console.log('✅ Créneaux suggérés récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des créneaux suggérés:', error)
      throw error
    }
  },

  /**
   * Récupérer les créneaux suggérés d'un patient
   */
  async getByPatient(patientId, includeExpired = false) {
    try {
      console.log('🕒 Récupération des créneaux suggérés du patient...', patientId)
      const params = includeExpired ? { include_expired: true } : {}
      const response = await api.get(`/suggested-slots/patient/${patientId}`, { params })
      console.log('✅ Créneaux suggérés du patient récupérés:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des créneaux suggérés:', error)
      throw error
    }
  },

  /**
   * Créer un nouveau créneau suggéré
   */
  async create(slotData) {
    try {
      console.log('🕒 Création d\'un nouveau créneau suggéré...', slotData)
      const response = await api.post('/suggested-slots', slotData)
      console.log('✅ Créneau suggéré créé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la création du créneau suggéré:', error)
      throw error
    }
  },

  /**
   * Mettre à jour l'acceptation d'un créneau suggéré
   */
  async updateAcceptance(slotId, accepted, userId = null) {
    try {
      console.log(`🕒 Mise à jour de l'acceptation du créneau ${slotId}...`, { accepted, userId })
      const data = { accepted, user_id: userId }
      const response = await api.put(`/suggested-slots/${slotId}/accept`, data)
      console.log('✅ Acceptation mise à jour:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour de l\'acceptation:', error)
      throw error
    }
  },

  /**
   * Supprimer un créneau suggéré
   */
  async delete(slotId) {
    try {
      console.log(`🕒 Suppression du créneau suggéré ${slotId}...`)
      const response = await api.delete(`/suggested-slots/${slotId}`)
      console.log('✅ Créneau suggéré supprimé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la suppression du créneau suggéré:', error)
      throw error
    }
  },

  /**
   * Récupérer les statistiques des créneaux suggérés
   */
  async getStats(medecinId = null) {
    try {
      console.log('📊 Récupération des statistiques des créneaux suggérés...')
      const params = medecinId ? { medecin_id: medecinId } : {}
      const response = await api.get('/suggested-slots/stats', { params })
      console.log('✅ Statistiques récupérées:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error)
      throw error
    }
  },

  /**
   * Nettoyer les créneaux expirés
   */
  async cleanExpired() {
    try {
      console.log('🧹 Nettoyage des créneaux expirés...')
      const response = await api.post('/suggested-slots/clean-expired')
      console.log('✅ Nettoyage terminé:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage:', error)
      throw error
    }
  },

  /**
   * Formater un créneau suggéré pour l'affichage
   */
  formatSlotForDisplay(slot) {
    return {
      id: slot.id,
      medecinId: slot.id_medecin,
      patientId: slot.id_patient,
      patientNom: slot.patient_nom,
      patientTelephone: slot.patient_telephone,
      patientEmail: slot.patient_email,
      dateHeureSuggeree: slot.date_heure_suggeree,
      duree: slot.duree,
      scoreConfiance: slot.score_confiance,
      raison: slot.raison,
      accepte: slot.accepte,
      statut: slot.statut,
      creeLe: slot.cree_le,
      expireLe: slot.expire_le,
      
      // Propriétés calculées
      isExpired: new Date(slot.expire_le) < new Date(),
      isAccepted: slot.accepte === 1,
      isRefused: slot.accepte === 0,
      isPending: slot.accepte === null,
      
      // Formatage des dates
      dateFormatted: this.formatDate(slot.date_heure_suggeree),
      timeFormatted: this.formatTime(slot.date_heure_suggeree),
      expiresInDays: this.getDaysUntilExpiry(slot.expire_le)
    }
  },

  /**
   * Formater une date
   */
  formatDate(dateString) {
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  },

  /**
   * Formater une heure
   */
  formatTime(dateString) {
    const date = new Date(dateString)
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  /**
   * Calculer les jours jusqu'à expiration
   */
  getDaysUntilExpiry(expiryDate) {
    const now = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  },

  /**
   * Obtenir la couleur selon le statut
   */
  getStatusColor(statut) {
    const colors = {
      'en_attente': '#f59e0b',    // Jaune
      'accepte': '#10b981',       // Vert
      'refuse': '#ef4444',        // Rouge
      'expire': '#6b7280'         // Gris
    }
    return colors[statut] || '#6b7280'
  },

  /**
   * Obtenir l'icône selon le statut
   */
  getStatusIcon(statut) {
    const icons = {
      'en_attente': 'fas fa-clock',
      'accepte': 'fas fa-check-circle',
      'refuse': 'fas fa-times-circle',
      'expire': 'fas fa-hourglass-end'
    }
    return icons[statut] || 'fas fa-question-circle'
  },

  /**
   * Obtenir le libellé du statut
   */
  getStatusLabel(statut) {
    const labels = {
      'en_attente': 'En attente',
      'accepte': 'Accepté',
      'refuse': 'Refusé',
      'expire': 'Expiré'
    }
    return labels[statut] || 'Inconnu'
  },

  /**
   * Calculer la couleur du score de confiance
   */
  getConfidenceColor(score) {
    if (score >= 0.8) return '#10b981' // Vert
    if (score >= 0.6) return '#f59e0b' // Jaune
    if (score >= 0.4) return '#f97316' // Orange
    return '#ef4444' // Rouge
  },

  /**
   * Valider les données d'un créneau suggéré
   */
  validateSlotData(slotData) {
    const errors = []
    
    if (!slotData.id_medecin) {
      errors.push('ID du médecin requis')
    }
    
    if (!slotData.id_patient) {
      errors.push('ID du patient requis')
    }
    
    if (!slotData.date_heure_suggeree) {
      errors.push('Date et heure suggérées requises')
    } else {
      const suggestedDate = new Date(slotData.date_heure_suggeree)
      const now = new Date()
      
      if (suggestedDate <= now) {
        errors.push('La date suggérée doit être dans le futur')
      }
    }
    
    if (!slotData.duree || slotData.duree <= 0) {
      errors.push('Durée valide requise')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * Générer des créneaux suggérés automatiquement
   */
  generateSuggestedSlots(medecinId, patientId, preferences = {}) {
    const slots = []
    const now = new Date()
    const {
      daysAhead = 7,
      preferredHours = [9, 10, 11, 14, 15, 16, 17],
      duration = 30,
      excludeWeekends = true
    } = preferences
    
    for (let day = 1; day <= daysAhead; day++) {
      const date = new Date(now)
      date.setDate(date.getDate() + day)
      
      // Exclure les weekends si demandé
      if (excludeWeekends && (date.getDay() === 0 || date.getDay() === 6)) {
        continue
      }
      
      preferredHours.forEach(hour => {
        const slotDate = new Date(date)
        slotDate.setHours(hour, 0, 0, 0)
        
        slots.push({
          id_medecin: medecinId,
          id_patient: patientId,
          date_heure_suggeree: slotDate.toISOString().slice(0, 19).replace('T', ' '),
          duree: duration,
          raison: 'Créneau généré automatiquement',
          expire_days: 7
        })
      })
    }
    
    return slots
  }
}

export default suggestedSlotService
