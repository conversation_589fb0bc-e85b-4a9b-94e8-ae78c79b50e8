<?php
class RateLimitMiddleware {
    const RATE_LIMIT = 100; // Requêtes autorisées
    const TIME_WINDOW = 3600; // Fenêtre de temps en secondes (1h)
    const BAN_TIME = 86400; // Durée de bannissement en secondes (24h)

    public static function check($identifier) {
        $cacheDir = __DIR__ . '/../cache/rate_limit/';
        if (!file_exists($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        $ip = self::sanitizeIdentifier($identifier);
        $cacheFile = $cacheDir . md5($ip);

        // Vérifier si l'IP est bannie
        if (self::isBanned($cacheFile)) {
            self::sendRateLimitResponse('Too many requests - Banned for 24 hours', 429);
        }

        // Lire ou initialiser le compteur
        $data = self::readCache($cacheFile);
        $currentTime = time();

        if ($data && $data['timestamp'] > ($currentTime - self::TIME_WINDOW)) {
            $data['count'] += 1;
        } else {
            $data = [
                'count' => 1,
                'timestamp' => $currentTime
            ];
        }

        // Vérifier la limite
        if ($data['count'] > self::RATE_LIMIT) {
            // Activer le bannissement
            $data['banned_until'] = $currentTime + self::BAN_TIME;
            file_put_contents($cacheFile, json_encode($data));
            self::sendRateLimitResponse('Too many requests - Rate limit exceeded', 429);
        }

        // Mettre à jour le cache
        file_put_contents($cacheFile, json_encode($data));

        // En-têtes de réponse
        header('X-RateLimit-Limit: ' . self::RATE_LIMIT);
        header('X-RateLimit-Remaining: ' . max(0, self::RATE_LIMIT - $data['count']));
        header('X-RateLimit-Reset: ' . ($data['timestamp'] + self::TIME_WINDOW));
    }

    private static function isBanned($cacheFile) {
        if (!file_exists($cacheFile)) {
            return false;
        }

        $data = json_decode(file_get_contents($cacheFile), true);
        return isset($data['banned_until']) && $data['banned_until'] > time();
    }

    private static function readCache($cacheFile) {
        if (!file_exists($cacheFile)) {
            return null;
        }

        return json_decode(file_get_contents($cacheFile), true);
    }

    private static function sanitizeIdentifier($identifier) {
        // Nettoyage de l'identifiant (IP ou user ID)
        return filter_var($identifier, FILTER_SANITIZE_STRING);
    }

    private static function sendRateLimitResponse($message, $code) {
        http_response_code($code);
        header('Content-Type: application/json');
        die(json_encode([
            'status' => 'error',
            'message' => $message,
            'retry_after' => self::TIME_WINDOW
        ]));
    }
}