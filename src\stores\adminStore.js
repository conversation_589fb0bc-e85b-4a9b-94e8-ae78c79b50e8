import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAdminStore = defineStore('admin', () => {
  const users = ref([])
  const appointments = ref([])
  const systemStats = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  const fetchUsers = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await api.get('/admin/users')
      users.value = response.data
    } catch (err) {
      error.value = 'Erreur lors du chargement des utilisateurs'
      console.error(err)
    } finally {
      isLoading.value = false
    }
  }

  const createUser = async (userData) => {
    isLoading.value = true
    error.value = null
    try {
      await api.post('/admin/users', userData)
      await fetchUsers() // Refresh the list
    } catch (err) {
      error.value = 'Erreur lors de la création de l\'utilisateur'
      console.error(err)
    } finally {
      isLoading.value = false
    }
  }

  const updateUser = async (userId, data) => {
    isLoading.value = true
    error.value = null
    try {
      await api.patch(`/admin/users/${userId}`, data)
      await fetchUsers() // Rafraîchir la liste
    } catch (err) {
      error.value = 'Erreur lors de la mise à jour de l\'utilisateur'
      console.error(err)
    } finally {
      isLoading.value = false
    }
  }

  const deleteUser = async (userId) => {
    isLoading.value = true
    error.value = null
    try {
      await api.delete(`/admin/users/${userId}`)
      await fetchUsers() // Refresh the list
    } catch (err) {
      error.value = 'Erreur lors de la suppression de l\'utilisateur'
      console.error(err)
    } finally {
      isLoading.value = false
    }
  }

  return {
    users,
    appointments,
    systemStats,
    isLoading,
    error,
    fetchUsers,
    createUser,
    updateUser,
    deleteUser
  }
})