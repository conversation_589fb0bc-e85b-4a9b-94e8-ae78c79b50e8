<?php
/**
 * Script pour ajouter des données de test directement
 * Exécutez ce script depuis la ligne de commande ou via le navigateur
 */

require_once __DIR__ . '/../config/database.php';

echo "🚀 AJOUT DE DONNÉES DE TEST\n";
echo "===========================\n\n";

try {
    // Lire le fichier SQL
    $sqlFile = __DIR__ . '/../../add_test_data.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Fichier SQL non trouvé: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Diviser en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($queries as $query) {
        if (empty($query) || strpos($query, '--') === 0) {
            continue; // Ignorer les commentaires et lignes vides
        }
        
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute();
            $successCount++;
            
            // Afficher les résultats pour les requêtes SELECT
            if (stripos($query, 'SELECT') === 0) {
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($results)) {
                    foreach ($results as $row) {
                        echo implode(' | ', $row) . "\n";
                    }
                    echo "\n";
                }
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ Erreur dans la requête: " . substr($query, 0, 50) . "...\n";
            echo "   Erreur: " . $e->getMessage() . "\n\n";
        }
    }
    
    echo "✅ Traitement terminé!\n";
    echo "   Requêtes réussies: $successCount\n";
    echo "   Erreurs: $errorCount\n\n";
    
    // Vérification finale
    echo "📊 VÉRIFICATION FINALE:\n";
    echo "=======================\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'medecin'");
    echo "👨‍⚕️ Médecins: " . $stmt->fetchColumn() . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE role = 'patient'");
    echo "👤 Patients: " . $stmt->fetchColumn() . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM rendez_vous");
    echo "📅 Rendez-vous: " . $stmt->fetchColumn() . "\n";
    
    // Vérifier si la table creneaux_suggeres existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'creneaux_suggeres'");
    if ($stmt->fetch()) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM creneaux_suggeres");
        echo "🕒 Créneaux suggérés: " . $stmt->fetchColumn() . "\n";
    } else {
        echo "🕒 Table 'creneaux_suggeres' n'existe pas\n";
    }
    
    echo "\n🎉 Données de test ajoutées avec succès!\n";
    echo "Vous pouvez maintenant utiliser l'application avec des données de test.\n\n";
    
    echo "📋 COMPTES DE TEST:\n";
    echo "==================\n";
    echo "Médecins:\n";
    echo "  - <EMAIL> (Dr. Jean Dupont - Cardiologie)\n";
    echo "  - <EMAIL> (Dr. Marie Martin - Dermatologie)\n";
    echo "  - <EMAIL> (Dr. Pierre Bernard - Médecine générale)\n\n";
    echo "Patients:\n";
    echo "  - <EMAIL> (Sophie Durand)\n";
    echo "  - <EMAIL> (Paul Moreau)\n";
    echo "  - <EMAIL> (Claire Petit)\n";
    echo "  - <EMAIL> (Michel Roux)\n\n";
    echo "Mot de passe pour tous: password123\n";
    
} catch (Exception $e) {
    echo "❌ Erreur fatale: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
