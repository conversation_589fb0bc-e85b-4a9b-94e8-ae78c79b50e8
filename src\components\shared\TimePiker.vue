
<!-- components/shared/TimePicker.vue -->
<template>
  <div class="time-picker">
    <label v-if="label" class="time-picker-label">{{ label }}</label>
    <div class="time-slots">
      <button
        v-for="slot in availableSlots"
        :key="slot"
        @click="selectTime(slot)"
        :class="[
          'time-slot',
          { 
            'selected': modelValue === slot,
            'disabled': isSlotDisabled(slot)
          }
        ]"
        :disabled="isSlotDisabled(slot)"
      >
        {{ slot }}
      </button>
    </div>
    <div v-if="error" class="time-picker-error">{{ error }}</div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { dateHelpers } from '@/utils/dateHelpers.js'

export default {
  name: 'TimePicker',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    startTime: {
      type: String,
      default: '08:00'
    },
    endTime: {
      type: String,
      default: '18:00'
    },
    duration: {
      type: Number,
      default: 30
    },
    disabledSlots: {
      type: Array,
      default: () => []
    },
    error: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const availableSlots = computed(() => {
      return dateHelpers.getTimeSlots(props.startTime, props.endTime, props.duration)
    })
    
    const isSlotDisabled = (slot) => {
      return props.disabledSlots.includes(slot)
    }
    
    const selectTime = (slot) => {
      if (!isSlotDisabled(slot)) {
        emit('update:modelValue', slot)
      }
    }
    
    return {
      availableSlots,
      isSlotDisabled,
      selectTime
    }
  }
}
</script>

<style scoped>
.time-picker {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.time-picker-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
}

.time-slot {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.time-slot:hover:not(:disabled) {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.time-slot.selected {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.time-slot.disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

.time-picker-error {
  color: #ef4444;
  font-size: 0.75rem;
}
</style>